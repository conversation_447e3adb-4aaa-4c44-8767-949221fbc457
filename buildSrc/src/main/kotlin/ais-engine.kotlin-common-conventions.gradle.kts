import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    kotlin("jvm")
    `jvm-test-suite`

    id("org.springframework.boot")
    kotlin("plugin.spring")
    id("io.spring.dependency-management")

    id("org.jetbrains.kotlinx.kover")
    id("org.jlleitschuh.gradle.ktlint")
}

ext["mongodb.version"] = mongodbVersion

// "hacky" way to disable boot-related tasks by default
// this is required as we must always apply spring boot configuration, since skeleton-plugins requires its usage
afterEvaluate {
    tasks.forEach { if (it.name.startsWith("boot")) it.enabled = false }
}

group = "nl.teqplay.aisengine"

val s3_access_key: String by project
val s3_secret_key: String by project

repositories {
    mavenCentral()
    maven {
        url = uri("s3://repo.teqplay.nl/release")
        credentials(AwsCredentials::class) {
            accessKey = s3_access_key
            secretKey = s3_secret_key
        }
    }
    maven {
        url = uri("s3://repo.teqplay.nl/snapshot")
        credentials(AwsCredentials::class) {
            accessKey = s3_access_key
            secretKey = s3_secret_key
        }
    }
}

dependencies {
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")

    implementation("io.github.oshai:kotlin-logging-jvm:$kotlinLoggingVersion")

    testImplementation("org.junit.jupiter:junit-jupiter:$junitVersion")

    testImplementation("org.mockito:mockito-core:$mockitoVersion")
    testImplementation("org.mockito:mockito-junit-jupiter:$mockitoVersion")
    testImplementation("org.mockito.kotlin:mockito-kotlin:$mockitoKotlinVersion")
}

tasks.withType<KotlinCompile> {
    compilerOptions {
        freeCompilerArgs.set(listOf("-Xjsr305=strict"))
        jvmTarget.set(JvmTarget.JVM_17)
    }
}
