plugins {
    id("ais-engine.kotlin-common-conventions")
}

dependencies {
    if (project.path != ":lib:common" && !project.path.startsWith(":api")) {
        implementation(project(":lib:common"))
        testImplementation(project(":lib:common"))
    }

    api(project(":api:models"))

    implementation("org.springframework.boot:spring-boot-starter")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    implementation("org.springframework.cloud:spring-cloud-starter-kubernetes-fabric8-config:$springKubernetesVersion")

    implementation("nl.teqplay.skeleton:common:$skeletonVersion")
    implementation("nl.teqplay.skeleton:common-network:$skeletonVersion")

    implementation("com.fasterxml.jackson.core:jackson-databind")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml")
}

testing {
    suites {
        val test by getting(JvmTestSuite::class) {
            useJUnitJupiter()
        }
    }
}
