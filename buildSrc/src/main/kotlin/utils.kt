import java.text.SimpleDateFormat
import java.util.Date

fun fromBaseVersion(baseVersion: String): String {
    val reventsEnabled = System.getenv("REVENTS") != null
    if (System.getenv("GH") != null) {
        val branch = System.getenv("BRANCH").replace("/", "_").lowercase()
        // use BUILD_NUM, instead of CIRCLECI_BUILD_NUM, we'll overwrite it to keep the same version across jobs
        val buildNum = System.getenv("GITHUB_RUN_NUMBER")
        val formattedTimestamp = SimpleDateFormat("yyyy-MM-dd").format(Date())
        return when {
            reventsEnabled -> "revents-$branch-b$buildNum"
            branch == "master" -> "$branch-$baseVersion"
            else -> "$branch-$formattedTimestamp-b$buildNum"
        }
    } else if (reventsEnabled) {
        val datetime = SimpleDateFormat("yyyyMMdd").format(Date())
        return "revents-$datetime"
    } else {
        val datetime = SimpleDateFormat("yyyyMMdd").format(Date())
        return "local-$datetime-$baseVersion"
    }
}