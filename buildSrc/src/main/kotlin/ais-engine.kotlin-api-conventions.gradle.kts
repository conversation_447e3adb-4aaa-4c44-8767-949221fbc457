plugins {
    id("ais-engine.kotlin-common-conventions")
    `maven-publish`
}

val apiVersion: String by rootProject.extra
version = fromBaseVersion(apiVersion)

val sourcesJar by tasks.registering(Jar::class) {
    archiveClassifier.set("sources")
    from(sourceSets.main.get().allSource)
}

val s3_access_key: String by project
val s3_secret_key: String by project

publishing {
    repositories {
        maven {
            url = when {
                project.version.toString().startsWith("master-") -> uri("s3://repo.teqplay.nl/release")
                else -> uri("s3://repo.teqplay.nl/snapshot")
            }

            credentials(AwsCredentials::class) {
                accessKey = s3_access_key
                secretKey = s3_secret_key
            }
        }
    }

    publications {
        register("mavenJava", MavenPublication::class) {
            from(components["java"])
            artifact(sourcesJar.get())
        }
    }
}
