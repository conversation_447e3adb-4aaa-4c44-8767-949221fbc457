
import com.bmuschko.gradle.docker.tasks.image.DockerPushImage
import org.springframework.boot.gradle.tasks.bundling.BootBuildImage

val ecrRepoUrl = "050356841556.dkr.ecr.eu-west-1.amazonaws.com"

plugins {
    id("ais-engine.kotlin-lib-conventions")
    id("ais-engine.kotlin-kapt-conventions")

    id("com.bmuschko.docker-remote-api")
    id("com.patdouble.awsecr")
}

val baseVersion: String by project.extra
version = fromBaseVersion(baseVersion)

// "hacky" way to (re)enable boot-related tasks in the context of an "app", see
// ais-engine.kotlin-common-conventions for reasoning
afterEvaluate {
    tasks.forEach { if (it.name.startsWith("boot")) it.enabled = true }
}

docker {
    registryCredentials {
        url.set(ecrRepoUrl)
    }
}

tasks.withType<BootBuildImage> {
    afterEvaluate {
        docker {
            val basename = "$ecrRepoUrl/ais-engine/${project.name}"
            imageName.set("$basename:${project.version}")
            tags.set(listOf("$basename:latest"))
        }
    }
}

tasks.register<DockerPushImage>("dockerPushImage") {
    images.set(listOf("$ecrRepoUrl/ais-engine/${project.name}:${project.version}"))
}

tasks.register("getVersion") {
    doLast {
        println(project.version)
    }
}

dependencies {
    implementation("nl.teqplay.skeleton:metrics:$skeletonVersion")

    implementation("org.springframework.boot:spring-boot-starter-web")
}


testing {
    suites {
        val test by getting(JvmTestSuite::class) {
            useJUnitJupiter()
        }

        register("integrationTest", JvmTestSuite::class) {
            sources {
                kotlin {
                    setSrcDirs(listOf("src/it/kotlin"))
                }
                resources {
                    setSrcDirs(listOf("src/it/resources"))
                }
            }

            dependencies {
                implementation(project())
                implementation("io.github.oshai:kotlin-logging-jvm:$kotlinLoggingVersion")
                implementation("org.testcontainers:testcontainers:$testContainersVersion")
                implementation("org.testcontainers:junit-jupiter:$testContainersVersion")
            }

            targets {
                all {
                    testTask.configure {
                        enabled = System.getProperty("enableIntegrationTests") == "true"

                        if (enabled) {
                            dependsOn(project.tasks.withType<BootBuildImage>())
                        }
                    }
                }
            }
        }
    }
}
