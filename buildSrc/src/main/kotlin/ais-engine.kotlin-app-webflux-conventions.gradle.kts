import com.bmuschko.gradle.docker.tasks.image.DockerPushImage
import org.springframework.boot.gradle.tasks.bundling.BootBuildImage

val ecrRepoUrl = "050356841556.dkr.ecr.eu-west-1.amazonaws.com"

plugins {
    id("ais-engine.kotlin-lib-conventions")
    id("ais-engine.kotlin-kapt-conventions")

    id("com.bmuschko.docker-remote-api")
    id("com.patdouble.awsecr")
}

val baseVersion: String by project.extra
version = fromBaseVersion(baseVersion)

// "hacky" way to (re)enable boot-related tasks in the context of an "app", see
// ais-engine.kotlin-common-conventions for reasoning
afterEvaluate {
    tasks.forEach { if (it.name.startsWith("boot")) it.enabled = true }
}

docker {
    registryCredentials {
        url.set(ecrRepoUrl)
    }
}

tasks.withType<BootBuildImage> {
    afterEvaluate {
        docker {
            imageName.set("$ecrRepoUrl/ais-engine/${project.name}:${project.version}")
        }
    }
}

tasks.register<DockerPushImage>("dockerPushImage") {
    images.set(listOf("$ecrRepoUrl/ais-engine/${project.name}:${project.version}"))
}

tasks.register("getVersion") {
    doLast {
        println(project.version)
    }
}

dependencies {
    implementation("nl.teqplay.skeleton:metrics:$skeletonVersion")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("io.projectreactor.kotlin:reactor-kotlin-extensions")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
}

testing {
    suites {
        val test by getting(JvmTestSuite::class) {
            useJUnitJupiter()
        }
    }
}

configurations.implementation {
    exclude(group = "org.springframework.boot", module = "spring-boot-starter-web")
}