package nl.teqplay.aisengine.nats.stream.revents

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_DIFF
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_DIFF_SUBJECT
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_HISTORY
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_HISTORY_SUBJECT
import nl.teqplay.aisengine.nats.stream.properties.AisStreamNatsProperties
import nl.teqplay.aisengine.revents.ReventsProfile
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientHealthBuilder
import nl.teqplay.skeleton.nats.NatsOrderedSingleProducerStream
import nl.teqplay.skeleton.nats.NatsProducerStream
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration

@Configuration
@ReventsProfile
class AisStreamPublishReventsAutoConfiguration(
    private val natsClientBuilder: NatsClientBuilder,
    private val natsClientHealthBuilder: NatsClientHealthBuilder,
    private val properties: AisStreamNatsProperties,
    private val objectMapper: ObjectMapper,
) {

    @Bean
    fun natsAisDiffProducerStream(): NatsOrderedSingleProducerStream<AisDiffMessage?>? {
        return natsClientBuilder.orderedSingleProducerStream(
            config = properties,
            stream = AIS_STREAM_DIFF,
            subjects = listOf(AIS_STREAM_DIFF_SUBJECT),
            serializer = {
                if (it == null) ByteArray(0)
                else objectMapper.writeValueAsBytes(it)
            },
            maxAge = Duration.ZERO,
            replicas = 1,
            storeOnDisk = false
        )
    }

    @Bean
    fun natsAisHistoryProducerStream(): NatsOrderedSingleProducerStream<AisWrapper<out BaseAisMessage>>? {
        return natsClientBuilder.orderedSingleProducerStream(
            config = properties,
            stream = AIS_STREAM_HISTORY,
            subjects = listOf(AIS_STREAM_HISTORY_SUBJECT),
            serializer = objectMapper::writeValueAsBytes,
            maxAge = Duration.ZERO,
            replicas = 1,
            storeOnDisk = false
        )
    }

    @Bean
    fun natsAisDiffProducerStreamHealthIndicator(
        producerStream: NatsProducerStream<AisDiffMessage?>,
    ): HealthIndicator {
        return natsClientHealthBuilder.natsClientHealthIndicator(producerStream)
    }

    @Bean
    fun natsAisHistoryProducerStreamHealthIndicator(
        producerStream: NatsProducerStream<AisWrapper<out BaseAisMessage>>,
    ): HealthIndicator {
        return natsClientHealthBuilder.natsClientHealthIndicator(producerStream)
    }
}
