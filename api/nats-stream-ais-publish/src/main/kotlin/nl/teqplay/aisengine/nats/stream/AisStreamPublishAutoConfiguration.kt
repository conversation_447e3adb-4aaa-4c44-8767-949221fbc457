package nl.teqplay.aisengine.nats.stream

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_DIFF
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_DIFF_SUBJECT
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_HISTORY
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_HISTORY_SUBJECT
import nl.teqplay.aisengine.nats.stream.properties.AisStreamNatsProperties
import nl.teqplay.aisengine.revents.NotReventsProfile
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientHealthBuilder
import nl.teqplay.skeleton.nats.NatsProducerStream
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration

@Configuration
@NotReventsProfile
@ConditionalOnProperty(prefix = "nats.ais-stream", name = ["enabled"], havingValue = "true")
class AisStreamPublishAutoConfiguration(
    private val natsClientBuilder: NatsClientBuilder,
    private val natsClientHealthBuilder: NatsClientHealthBuilder,
    private val properties: AisStreamNatsProperties,
    private val objectMapper: ObjectMapper
) {

    @Bean
    fun natsAisDiffProducerStream(): NatsProducerStream<AisDiffMessage>? {
        return natsClientBuilder.producerStream(
            config = properties,
            stream = AIS_STREAM_DIFF,
            subjects = listOf(AIS_STREAM_DIFF_SUBJECT),
            serializer = objectMapper::writeValueAsBytes,
            maxAge = Duration.ofHours(1),
            storeOnDisk = true
        )
    }

    @Bean
    fun natsAisHistoryProducerStream(): NatsProducerStream<AisWrapper<out BaseAisMessage>>? {
        return natsClientBuilder.producerStream(
            config = properties,
            stream = AIS_STREAM_HISTORY,
            subjects = listOf(AIS_STREAM_HISTORY_SUBJECT),
            serializer = objectMapper::writeValueAsBytes,
            maxAge = Duration.ofHours(1),
            storeOnDisk = true
        )
    }

    @Bean
    fun natsAisDiffProducerStreamHealthIndicator(
        producerStream: NatsProducerStream<AisDiffMessage>
    ): HealthIndicator {
        return natsClientHealthBuilder.natsClientHealthIndicator(producerStream)
    }

    @Bean
    fun natsAisHistoryProducerStreamHealthIndicator(
        producerStream: NatsProducerStream<AisWrapper<out BaseAisMessage>>
    ): HealthIndicator {
        return natsClientHealthBuilder.natsClientHealthIndicator(producerStream)
    }
}
