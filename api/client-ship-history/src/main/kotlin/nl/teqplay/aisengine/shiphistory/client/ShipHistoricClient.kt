package nl.teqplay.aisengine.shiphistory.client

import nl.teqplay.aisengine.client.annotations.InternalApiRestTemplate
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.skeleton.common.network.getForObjectWithParams
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate

/**
 * Client used to get current ship state, by mmsi, imo, query, and by area.
 */
@Component
class ShipHistoricClient(
    @InternalApiRestTemplate private val restTemplate: RestTemplate,
) {

    /**
     * Find the historic state for ship with [mmsi] and [window]
     */
    fun findHistoricByMmsi(
        mmsi: Int,
        window: TimeWindow,
    ): List<AisHistoricMessage> = restTemplate.getForObjectWithParams<Array<AisHistoricMessage>>(
        path = "/v1/ship/history/mmsi/{mmsi}",
        pathParams = listOf(mmsi),
        queryParams = mapOf(
            "from" to window.from,
            "to" to window.to
        )
    ).toList()
}
