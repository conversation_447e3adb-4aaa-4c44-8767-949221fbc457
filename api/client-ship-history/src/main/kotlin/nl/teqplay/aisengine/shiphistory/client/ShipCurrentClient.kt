package nl.teqplay.aisengine.shiphistory.client

import nl.teqplay.aisengine.client.annotations.InternalApiRestTemplate
import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage
import nl.teqplay.aisengine.shiphistory.model.AisMessageCurrentQuery
import nl.teqplay.skeleton.common.network.getForObjectWithParams
import nl.teqplay.skeleton.common.network.postForObjectWithParams
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForObject
import java.time.Duration

/**
 * Client used to get current ship state, by mmsi, imo, query, and by area.
 */
@Component
class ShipCurrentClient(
    @InternalApiRestTemplate private val restTemplate: RestTemplate,
) {

    /**
     * Find current state for ship with [mmsi]
     */
    fun findCurrentByMmsi(
        mmsi: Int,
    ): AisCurrentMessage? = restTemplate.getForObjectWithParams<AisCurrentMessage?>(
        path = "/v1/ship/current/mmsi/{mmsi}",
        pathParams = listOf(mmsi)
    )

    /**
     * Find current ships matching the [mmsiList]
     */
    fun findCurrentByMmsiList(
        mmsiList: Collection<Int>,
    ): List<AisCurrentMessage> = restTemplate.postForObject<Array<AisCurrentMessage>>(
        url = "/v1/ship/current/mmsi",
        request = mmsiList
    ).toList()

    /**
     * Find current state for ship with [imo]
     */
    fun findCurrentByImo(
        imo: Int,
    ): List<AisCurrentMessage> = restTemplate.getForObjectWithParams<Array<AisCurrentMessage>>(
        path = "/v1/ship/current/imo/{imo}",
        pathParams = listOf(imo)
    ).toList()

    /**
     * Find current ships matching the [imoList]
     */
    fun findCurrentByImoList(
        imoList: List<Int>,
    ): List<AisCurrentMessage> = restTemplate.postForObject<Array<AisCurrentMessage>>(
        url = "/v1/ship/current/imo",
        request = imoList
    ).toList()

    /**
     * Find current ships within the [boundingBox]
     */
    fun findCurrentInBoundingBox(
        boundingBox: BoundingBox? = null,
        showAged: Boolean? = null,
        maxAge: Duration? = null,
    ): List<AisCurrentMessage> = restTemplate.postForObjectWithParams<Array<AisCurrentMessage>>(
        path = "/v1/ship/current/boundingBox",
        pathParams = emptyList(),
        queryParams = mapOf(
            "showAged" to showAged,
            "maxAge" to maxAge
        ),
        body = boundingBox
    ).toList()

    /**
     * Find current ships within the [polygon]
     */
    fun findCurrentInPolygon(
        polygon: Polygon,
        showAged: Boolean? = null,
        maxAge: Duration? = null,
    ): List<AisCurrentMessage> = restTemplate.postForObjectWithParams<Array<AisCurrentMessage>>(
        path = "/v1/ship/current/polygon",
        pathParams = emptyList(),
        queryParams = mapOf(
            "showAged" to showAged,
            "maxAge" to maxAge
        ),
        body = polygon
    ).toList()

    /**
     * Find current ships within a circle, with a specified [center] and [radiusInKm].
     */
    fun findCurrentInCircle(
        center: Location,
        radiusInKm: Double,
        showAged: Boolean? = null,
        maxAge: Duration? = null,
    ): List<AisCurrentMessage> = restTemplate.postForObjectWithParams<Array<AisCurrentMessage>>(
        path = "/v1/ship/current/circle",
        pathParams = emptyList(),
        queryParams = mapOf(
            "radiusInKm" to radiusInKm,
            "showAged" to showAged,
            "maxAge" to maxAge
        ),
        body = center
    ).toList()

    /**
     * Find current ships which match the query
     */
    fun findCurrentMatchingQuery(
        query: AisMessageCurrentQuery,
        limit: Int? = null,
        seaVessel: Boolean? = null,
        showAged: Boolean? = null,
        maxAge: Duration? = null,
    ): List<AisCurrentMessage> = restTemplate.postForObjectWithParams<Array<AisCurrentMessage>>(
        path = "/v1/ship/current/query",
        pathParams = emptyList(),
        queryParams = mapOf(
            "limit" to limit,
            "seaVessel" to seaVessel,
            "showAged" to showAged,
            "maxAge" to maxAge
        ),
        body = query
    ).toList()
}
