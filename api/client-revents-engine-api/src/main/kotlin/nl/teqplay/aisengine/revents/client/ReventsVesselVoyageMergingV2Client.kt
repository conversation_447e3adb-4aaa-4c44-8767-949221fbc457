package nl.teqplay.aisengine.revents.client

import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntryV2
import nl.teqplay.skeleton.common.AllOpen
import nl.teqplay.skeleton.common.network.getForObjectWithParams
import org.springframework.web.client.RestTemplate

@AllOpen
class ReventsVesselVoyageMergingV2Client(private val restTemplate: RestTemplate) {

    /**
     * Get the interests of a scenario by its [id], but specific to VesselVoyage merging.
     */
    fun getInterestsForMerging(
        id: String
    ): Array<Int> = restTemplate.getForObjectWithParams("/vesselvoyage/v2/{id}/merging/interests", listOf(id))

    /**
     * Fetches all VesselVoyage entries of a scenario by its [id], specific to this [imo].
     */
    fun fetchEntriesForMerging(
        id: String,
        imo: Int
    ): Array<MergeEntryV2> = restTemplate.getForObjectWithParams("/vesselvoyage/v2/{id}/merging/entries/imo/{imo}", listOf(id, imo))
}
