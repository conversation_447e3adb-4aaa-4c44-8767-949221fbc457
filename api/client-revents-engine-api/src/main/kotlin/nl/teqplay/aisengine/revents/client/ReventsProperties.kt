package nl.teqplay.aisengine.revents.client

import nl.teqplay.skeleton.common.config.KeycloakS2SClientProperties
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "revents")
data class ReventsProperties(
    override val url: String = "",
    override val domain: String? = null,
    override val realm: String? = null,
    override val clientId: String = "",
    override val clientSecret: String = "",
) : KeycloakS2SClientProperties(url, domain, realm, clientId, clientSecret)
