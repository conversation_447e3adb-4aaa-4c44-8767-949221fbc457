package nl.teqplay.aisengine.revents.client

import nl.teqplay.skeleton.auth.credentials.keycloak.s2s.client.KeycloakS2SClientWrapper
import nl.teqplay.skeleton.common.logging.OutgoingRequestLogger
import org.springframework.beans.factory.ObjectProvider
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate

@Configuration
@EnableConfigurationProperties(ReventsProperties::class)
class ReventsAutoConfiguration {

    @Bean(BeanNames.REVENTS_REST_TEMPLATE)
    fun reventsRestTemplate(
        restTemplateBuilder: RestTemplateBuilder,
        properties: ReventsProperties,
        outgoingRequestLoggerProvider: ObjectProvider<OutgoingRequestLogger>,
    ): RestTemplate {
        return KeycloakS2SClientWrapper.create(
            restTemplateBuilder,
            outgoingRequestLoggerProvider,
            properties
        )
    }

    @Bean
    @ConditionalOnMissingBean
    fun reventsClient(@ReventsRestTemplate restTemplate: RestTemplate): ReventsClient =
        ReventsClient(restTemplate)

    @Bean
    @ConditionalOnMissingBean
    fun reventsVesselVoyageClient(@ReventsRestTemplate restTemplate: RestTemplate): ReventsVesselVoyageClient =
        ReventsVesselVoyageClient(restTemplate)

    @Bean
    @ConditionalOnMissingBean
    fun reventsVesselVoyageMergingClient(@ReventsRestTemplate restTemplate: RestTemplate): ReventsVesselVoyageMergingClient =
        ReventsVesselVoyageMergingClient(restTemplate)

    @Bean
    @ConditionalOnMissingBean
    fun reventsVesselVoyageV2Client(@ReventsRestTemplate restTemplate: RestTemplate): ReventsVesselVoyageV2Client =
        ReventsVesselVoyageV2Client(restTemplate)

    @Bean
    @ConditionalOnMissingBean
    fun reventsVesselVoyageMergingV2Client(@ReventsRestTemplate restTemplate: RestTemplate): ReventsVesselVoyageMergingV2Client =
        ReventsVesselVoyageMergingV2Client(restTemplate)
}
