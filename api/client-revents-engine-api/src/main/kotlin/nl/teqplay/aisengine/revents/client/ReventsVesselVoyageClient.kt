package nl.teqplay.aisengine.revents.client

import nl.teqplay.aisengine.reventsengine.model.interest.InterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.ReventsVesselVoyageVisitQuery
import nl.teqplay.skeleton.common.AllOpen
import nl.teqplay.skeleton.common.network.getForObjectWithParams
import nl.teqplay.skeleton.common.network.postForObjectWithParams
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.Visit
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForObject

@AllOpen
class ReventsVesselVoyageClient(private val restTemplate: RestTemplate) {

    /**
     * Get the interests of a scenario by its [id], but specific to VesselVoyage.
     */
    fun getInterests(
        id: String,
    ): Array<InterestVesselVoyage> = restTemplate.getForObject("/vesselvoyage/{id}/interests", id)

    /**
     * Fetches VesselVoyage entries of a scenario by its [id], specific to this [imo] within the [window].
     */
    fun fetchEntries(
        id: String,
        imo: String,
        window: TimeWindow,
    ): Array<Entry> = restTemplate.postForObjectWithParams("/vesselvoyage/{id}/entries/imo/{imo}", window, listOf(id, imo))

    /**
     * Get one entry by its [entryId] for a scenario by [id].
     */
    fun getEntryById(
        id: String,
        entryId: String,
    ): Entry = restTemplate.getForObjectWithParams("/vesselvoyage/{id}/entries/{entryId}", listOf(id, entryId))

    /**
     * Get multiple entries by their [entryIds] for a scenario by [id].
     */
    fun getEntriesById(
        id: String,
        entryIds: List<String>,
    ): Array<Entry> = restTemplate.postForObjectWithParams("/vesselvoyage/{id}/entries", entryIds, listOf(id))

    /**
     * Query visits based on the supplied [query] for a scenario by [id].
     */
    fun queryVisits(
        id: String,
        query: ReventsVesselVoyageVisitQuery,
    ): Array<Visit> = restTemplate.postForObjectWithParams("/vesselvoyage/{id}/visits/query", query, listOf(id))
}
