package nl.teqplay.aisengine.revents.client

import nl.teqplay.aisengine.reventsengine.model.interest.InterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.ReventsVesselVoyageVisitV2Query
import nl.teqplay.skeleton.common.AllOpen
import nl.teqplay.skeleton.common.network.getForObjectWithParams
import nl.teqplay.skeleton.common.network.postForObjectWithParams
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import org.springframework.web.client.RestTemplate

@AllOpen
class ReventsVesselVoyageV2Client(private val restTemplate: RestTemplate) {

    /**
     * Get the interests of a scenario by its [id], but specific to VesselVoyage.
     */
    fun getInterests(
        id: String,
    ): Array<InterestVesselVoyage> = restTemplate.getForObjectWithParams("/vesselvoyage/v2/{id}/interests", listOf(id))

    /**
     * Fetches VesselVoyage entries of a scenario by its [id], specific to this [imo] within the [window].
     */
    fun fetchEntries(
        id: String,
        imo: String,
        window: TimeWindow,
    ): Array<EntryESoFWrapper<out NewEntry>> = restTemplate.postForObjectWithParams("/vesselvoyage/v2/{id}/entries/imo/{imo}", window, listOf(id, imo))

    /**
     * Get one entry by its [entryId] for a scenario by [id].
     */
    fun getEntryById(
        id: String,
        entryId: String,
    ): EntryESoFWrapper<out NewEntry> = restTemplate.getForObjectWithParams("/vesselvoyage/v2/{id}/entries/{entryId}", listOf(id, entryId))

    /**
     * Get multiple entries by their [entryIds] for a scenario by [id].
     */
    fun getEntriesById(
        id: String,
        entryIds: List<String>,
    ): Array<EntryESoFWrapper<out NewEntry>> = restTemplate.postForObjectWithParams("/vesselvoyage/v2/{id}/entries", entryIds, listOf(id))

    /**
     * Query visits based on the supplied [query] for a scenario by [id].
     */
    fun queryVisits(
        id: String,
        query: ReventsVesselVoyageVisitV2Query,
    ): Array<EntryESoFWrapper<NewVisit>> = restTemplate.postForObjectWithParams("/vesselvoyage/v2/{id}/visits/query", query, listOf(id))
}
