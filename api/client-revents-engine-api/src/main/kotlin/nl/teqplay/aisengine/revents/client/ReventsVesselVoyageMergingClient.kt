package nl.teqplay.aisengine.revents.client

import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntry
import nl.teqplay.skeleton.common.AllOpen
import nl.teqplay.skeleton.common.network.getForObjectWithParams
import org.springframework.web.client.RestTemplate

@AllOpen
class ReventsVesselVoyageMergingClient(private val restTemplate: RestTemplate) {

    /**
     * Get the interests of a scenario by its [id], but specific to VesselVoyage merging.
     */
    fun getInterestsForMerging(
        id: String
    ): Array<String> = restTemplate.getForObjectWithParams("/vesselvoyage/{id}/merging/interests", listOf(id))

    /**
     * Fetches all VesselVoyage entries of a scenario by its [id], specific to this [imo].
     */
    fun fetchEntriesForMerging(
        id: String,
        imo: String
    ): Array<MergeEntry> = restTemplate.getForObjectWithParams("/vesselvoyage/{id}/merging/entries/imo/{imo}", listOf(id, imo))
}
