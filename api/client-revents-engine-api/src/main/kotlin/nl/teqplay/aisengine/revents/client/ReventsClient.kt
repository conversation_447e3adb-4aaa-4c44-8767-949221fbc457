package nl.teqplay.aisengine.revents.client

import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.reventsengine.model.interest.InterestRelevantShip
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioQueryResponse
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse
import nl.teqplay.skeleton.common.AllOpen
import nl.teqplay.skeleton.common.network.postForObjectWithParams
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForObject
import org.springframework.web.client.postForObject

@AllOpen
class ReventsClient(private val restTemplate: RestTemplate) {

    /**
     * Creates a [scenario] and schedules it.
     */
    fun createScenario(
        scenario: ScenarioCreateRequest
    ): ScenarioResponse = restTemplate.postForObject("/scenario", scenario)

    /**
     * Cancel a scenario by its [id].
     */
    fun cancelScenario(
        id: String
    ) = restTemplate.put("/scenario/{id}/cancel", null, id)

    /**
     * Get a scenario by its [id], turning it into a [ScenarioResponse].
     */
    fun getScenario(
        id: String
    ): ScenarioResponse = restTemplate.getForObject("/scenario/{id}", id)

    /**
     * Query scenarios based on a [ScenarioCreateRequest], to decide whether to reuse
     * a pre-existing scenario or create a new one using [createScenario].
     */
    fun queryScenarios(
        scenario: ScenarioCreateRequest
    ): ScenarioQueryResponse = restTemplate.postForObject("/scenario/query", scenario)

    /**
     * Get the interests of a scenario by its [id].
     */
    fun getScenarioInterests(
        id: String
    ): Array<InterestRelevantShip> = restTemplate.getForObject("/scenario/{id}/interests", id)

    /**
     * Fetch [ActualEvent] generated in the [scenario], specific to [ship],
     * filtered by event [types], and within the [window].
     */
    fun <T : ShipByIdentifier> fetchEventsForScenario(
        scenario: String,
        ship: T,
        types: Set<String>,
        window: TimeWindow
    ): Array<ActualEvent> = restTemplate.postForObjectWithParams(
        path = "/events",
        body = window,
        pathParams = emptyList(),
        queryParams = mapOf(
            "scenario" to scenario,
            "types" to types.joinToString(","),
            ship.toPair()
        )
    )

    sealed interface ShipByIdentifier {
        fun toPair(): Pair<String, Int>
    }

    data class ShipByMmsi(
        val mmsi: Int
    ) : ShipByIdentifier {
        override fun toPair(): Pair<String, Int> = "mmsi" to mmsi
    }

    data class ShipByImo(
        val imo: Int
    ) : ShipByIdentifier {
        override fun toPair(): Pair<String, Int> = "imo" to imo
    }
}
