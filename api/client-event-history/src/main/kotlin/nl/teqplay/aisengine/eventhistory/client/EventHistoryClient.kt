package nl.teqplay.aisengine.eventhistory.client

import nl.teqplay.aisengine.client.annotations.InternalApiRestTemplate
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.skeleton.common.network.postForObjectWithParams
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate

/**
 * Client used to get events from event-history using the teqplay api.
 */
@Component
class EventHistoryClient(
    @InternalApiRestTemplate private val restTemplate: RestTemplate,
) {
    /**
     * Find the events for all ships using the provided [mmsis] in a given time [window].
     */
    fun findHistoryByMmsi(
        mmsis: List<Int>,
        window: TimeWindow,
        includeEvents: Set<String>? = null,
        excludeEvents: Set<String>? = null,
        includeAreaTypes: Set<AreaIdentifier.AreaType>? = null,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>? = null,
        maxDays: Int? = null
    ): List<Event> {
        return restTemplate.postForObjectWithParams<Array<Event>>(
            path = "/v1/event/history/mmsilist",
            body = mmsis,
            pathParams = emptyList(),
            queryParams = listOfNotNull(
                "from" to window.from,
                "to" to window.to,
                includeEvents?.let { "includeEvents" to includeEvents.joinToString(",") },
                excludeEvents?.let { "excludeEvents" to excludeEvents.joinToString(",") },
                includeAreaTypes?.let { "includeAreaTypes" to includeAreaTypes.joinToString(",") },
                excludeAreaTypes?.let { "excludeAreaTypes" to excludeAreaTypes.joinToString(",") },
                maxDays?.let { "maxDays" to maxDays }
            ).toMap()
        ).toList()
    }

    /**
     * Find the events for all ships using the provided [imos] in a given time [window].
     */
    fun findHistoryByImo(
        imos: List<Int>,
        window: TimeWindow,
        includeEvents: Set<String>? = null,
        excludeEvents: Set<String>? = null,
        includeAreaTypes: Set<AreaIdentifier.AreaType>? = null,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>? = null,
        maxDays: Int? = null
    ): List<Event> {
        return restTemplate.postForObjectWithParams<Array<Event>>(
            path = "/v1/event/history/imolist",
            body = imos,
            pathParams = emptyList(),
            queryParams = listOfNotNull(
                "from" to window.from,
                "to" to window.to,
                includeEvents?.let { "includeEvents" to includeEvents.joinToString(",") },
                excludeEvents?.let { "excludeEvents" to excludeEvents.joinToString(",") },
                includeAreaTypes?.let { "includeAreaTypes" to includeAreaTypes.joinToString(",") },
                excludeAreaTypes?.let { "excludeAreaTypes" to excludeAreaTypes.joinToString(",") },
                maxDays?.let { "maxDays" to maxDays }
            ).toMap()
        ).toList()
    }

    /**
     * Find the events for all ships using the provided [polygon] in a given time [window].
     */
    fun findHistoryInPolygon(
        polygon: Polygon,
        window: TimeWindow,
        includeEvents: Set<String>? = null,
        excludeEvents: Set<String>? = null,
        includeAreaTypes: Set<AreaIdentifier.AreaType>? = null,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>? = null,
        maxDays: Int? = null,
        maxAreaInKm2: Double? = null
    ): List<Event> {
        return restTemplate.postForObjectWithParams<Array<Event>>(
            path = "/v1/event/history/polygon",
            body = polygon,
            pathParams = emptyList(),
            queryParams = listOfNotNull(
                "from" to window.from,
                "to" to window.to,
                includeEvents?.let { "includeEvents" to includeEvents.joinToString(",") },
                excludeEvents?.let { "excludeEvents" to excludeEvents.joinToString(",") },
                includeAreaTypes?.let { "includeAreaTypes" to includeAreaTypes.joinToString(",") },
                excludeAreaTypes?.let { "excludeAreaTypes" to excludeAreaTypes.joinToString(",") },
                maxDays?.let { "maxDays" to maxDays },
                maxAreaInKm2?.let { "maxAreaInKm2" to maxAreaInKm2 }
            ).toMap()
        ).toList()
    }
}
