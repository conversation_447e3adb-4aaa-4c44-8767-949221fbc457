package nl.teqplay.aisengine.event.util

import nl.teqplay.aisengine.event.interfaces.SpeedEvent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SubjectUtilTest {
    private fun testData() = Stream.of(
        Arguments.of(
            SpeedEvent.SpeedType.ACCELERATING,
            "accelerating"
        ),
        Arguments.of(
            SpeedEvent.SpeedType.SLOWING_DOWN,
            "slowing-down"
        )
    )

    @ParameterizedTest
    @MethodSource("testData")
    fun `should parse enum correctly to nats subject part`(
        enum: Enum<*>,
        expectedValue: String
    ) {
        val result = enum.nameAsSubject()

        assertEquals(expectedValue, result)
    }
}
