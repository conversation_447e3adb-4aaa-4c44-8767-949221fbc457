package nl.teqplay.aisengine.reventsengine.model.scenario

import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant

class ScenarioTest {

    private val scenario = ScenarioCreateRequest(
        window = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
        interests = emptyList()
    )

    @Test
    fun `Settings - init without postProcessing`() {
        assertEquals(
            Scenario.Settings(scenario).copy(
                resolveShipInterestsForImo = false
            ),
            Scenario.Settings(scenario)
        )
    }

    @Test
    fun `Settings - init with VesselVoyage postProcessing`() {
        assertEquals(
            Scenario.Settings(scenario).copy(
                resolveShipInterestsForImo = true
            ),
            Scenario.Settings(
                scenario.copy(
                    postProcessing = setOf(ScenarioPostProcessing.VESSEL_VOYAGE)
                )
            )
        )
    }
}
