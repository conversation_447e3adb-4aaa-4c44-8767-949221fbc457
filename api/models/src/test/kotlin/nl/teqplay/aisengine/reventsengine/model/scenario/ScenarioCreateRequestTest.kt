package nl.teqplay.aisengine.reventsengine.model.scenario

import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant

class ScenarioCreateRequestTest {

    private val window = TimeWindow(Instant.EPOCH, Duration.ofDays(1))

    @Test
    fun `ScenarioCreateRequest - default windowMargin`() {
        assertEquals(
            Scenario.WindowMargin(beforeInDays = 0, afterInDays = 0),
            ScenarioCreateRequest(
                window = window,
                interests = emptyList()
            ).windowMargin
        )
    }

    @Test
    fun `ScenarioCreateRequest - custom windowMargin`() {
        assertEquals(
            Scenario.WindowMargin(beforeInDays = 31, afterInDays = 14),
            ScenarioCreateRequest(
                window = window,
                windowMargin = Scenario.WindowMargin(beforeInDays = 31, afterInDays = 14),
                interests = emptyList()
            ).windowMargin
        )
    }
}
