package nl.teqplay.aisengine.reventsengine.model.scenario

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.PORT
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.TERMINAL
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestPolygon
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestShip
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING_V2
import nl.teqplay.skeleton.model.Polygon
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class ScenarioGuaranteeTest {

    @Test
    fun `postProcessing - VESSEL_VOYAGE_MERGING`() {
        assertEquals(setOf(ScenarioPostProcessing.VESSEL_VOYAGE), VESSEL_VOYAGE_MERGING.postProcessing)
    }

    @Test
    fun `postProcessing - VESSEL_VOYAGE_MERGING_V2`() {
        assertEquals(setOf(ScenarioPostProcessing.VESSEL_VOYAGE_V2), VESSEL_VOYAGE_MERGING_V2.postProcessing)
    }

    @ParameterizedTest
    @EnumSource(ScenarioGuarantee::class, names = ["VESSEL_VOYAGE_MERGING", "VESSEL_VOYAGE_MERGING_V2"])
    fun `adjustWindowMargin minimum - VesselVoyage merging`(guarantee: ScenarioGuarantee) {
        val expectedMin = Scenario.WindowMargin(1, 1)
        val windowMarginMin = Scenario.WindowMargin()
        assertEquals(expectedMin, guarantee.adjustWindowMargin(windowMarginMin))
    }

    @ParameterizedTest
    @EnumSource(ScenarioGuarantee::class, names = ["VESSEL_VOYAGE_MERGING", "VESSEL_VOYAGE_MERGING_V2"])
    fun `adjustWindowMargin exceeds minimum - VesselVoyage merging`(guarantee: ScenarioGuarantee) {
        val windowMargin = Scenario.WindowMargin(2, 2)
        assertEquals(windowMargin, guarantee.adjustWindowMargin(windowMargin))
    }

    @ParameterizedTest
    @EnumSource(ScenarioGuarantee::class, names = ["VESSEL_VOYAGE_MERGING", "VESSEL_VOYAGE_MERGING_V2"])
    fun `adjustSettings - VesselVoyage merging`(guarantee: ScenarioGuarantee) {
        val expected = Scenario.Settings(
            resolveShipInterestsForImo = true,
            filterHistory = Scenario.FilterHistory.USE_ALL_DATA,
            allowPartialInterests = true
        )
        val initial = expected.copy(
            resolveShipInterestsForImo = false,
        )
        assertEquals(expected, guarantee.adjustSettings(initial))
    }

    @ParameterizedTest
    @EnumSource(ScenarioGuarantee::class, names = ["VESSEL_VOYAGE_MERGING", "VESSEL_VOYAGE_MERGING_V2"])
    fun `validateInterests allow ship interests - VesselVoyage merging`(guarantee: ScenarioGuarantee) {
        val interests = listOf(
            InterestShip(ship = InterestShip.Identifier(mmsi = 0))
        )
        assertDoesNotThrow {
            guarantee.validateInterests(interests)
        }
    }

    @ParameterizedTest
    @EnumSource(ScenarioGuarantee::class, names = ["VESSEL_VOYAGE_MERGING", "VESSEL_VOYAGE_MERGING_V2"])
    fun `validateInterests allow area (port) interests - VesselVoyage merging`(guarantee: ScenarioGuarantee) {
        val interests = listOf(
            InterestArea(area = InterestArea.Area(type = PORT, id = "ID"))
        )
        assertDoesNotThrow {
            guarantee.validateInterests(interests)
        }
    }

    @ParameterizedTest
    @EnumSource(ScenarioGuarantee::class, names = ["VESSEL_VOYAGE_MERGING", "VESSEL_VOYAGE_MERGING_V2"])
    fun `validateInterests disallow non-port area interests - VesselVoyage merging`(guarantee: ScenarioGuarantee) {
        val interests = listOf(
            InterestArea(area = InterestArea.Area(type = TERMINAL, id = "ID"))
        )
        assertThrows<IllegalArgumentException> {
            guarantee.validateInterests(interests)
        }
    }

    @ParameterizedTest
    @EnumSource(ScenarioGuarantee::class, names = ["VESSEL_VOYAGE_MERGING", "VESSEL_VOYAGE_MERGING_V2"])
    fun `validateInterests disallow polygon interests - VesselVoyage merging`(guarantee: ScenarioGuarantee) {
        val interests = listOf(
            InterestPolygon(polygon = Polygon())
        )
        assertThrows<IllegalArgumentException> {
            guarantee.validateInterests(interests)
        }
    }
}
