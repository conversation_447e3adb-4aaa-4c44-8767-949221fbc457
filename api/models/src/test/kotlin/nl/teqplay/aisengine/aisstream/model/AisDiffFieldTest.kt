package nl.teqplay.aisengine.aisstream.model

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.skeleton.common.BaseTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.test.context.TestConstructor
import java.util.stream.Stream

typealias AisDiffFieldString = AisDiffField<String?, String?>

@EnableAutoConfiguration
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
internal class AisDiffFieldTest(
    private val objectMapper: ObjectMapper
) : BaseTest() {

    private fun aisDiffFieldTestData() = Stream.of(
        Arguments.of(
            "no data",
            "[null,null]",
            AisDiffFieldString(null, null)
        ),
        Arguments.of(
            "only old value",
            "[\"value\",null]",
            AisDiffFieldString("value", null)
        ),
        Arguments.of(
            "null change to value",
            "[null,[\"value\"]]",
            AisDiffFieldString(null, AisDiffField.Change("value"))
        ),
        Arguments.of(
            "value change to null",
            "[\"value\",[null]]",
            AisDiffFieldString("value", AisDiffField.Change(null))
        ),
        Arguments.of(
            "old value changes to new value",
            "[\"value\",[\"new\"]]",
            AisDiffFieldString("value", AisDiffField.Change("new"))
        )
    )

    @ParameterizedTest
    @MethodSource("aisDiffFieldTestData")
    fun testAisDiffField(
        message: String,
        json: String,
        field: AisDiffFieldString
    ) {
        assertEquals(json, objectMapper.writeValueAsString(field), message)
        assertEquals(field, objectMapper.readValue<AisDiffFieldString>(json), message)
    }

    @Test
    fun testGetLatest() {
        assertEquals(null, AisDiffFieldString(null, null).latest())
        assertEquals("old", AisDiffFieldString("old", null).latest())
        assertEquals("new", AisDiffFieldString(null, AisDiffField.Change("new")).latest())
        assertEquals("new", AisDiffFieldString("old", AisDiffField.Change("new")).latest())
    }
}
