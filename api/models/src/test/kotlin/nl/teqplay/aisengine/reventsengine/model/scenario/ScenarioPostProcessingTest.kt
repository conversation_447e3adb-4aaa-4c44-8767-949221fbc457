package nl.teqplay.aisengine.reventsengine.model.scenario

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ScenarioPostProcessingTest {

    @Test
    fun `eventTypeNames - VesselVoyage`() {
        val expected = setOf(
            "AisStatusChangedEvent",
            "AisDestinationChangedEvent",
            "ShipMovingStartEvent",
            "ShipMovingEndEvent",
            "AnchoredStartEvent",
            "AnchoredEndEvent",
            "AreaStartEvent",
            "AreaEndEvent",
            "ConfirmedBerthStartEvent",
            "ConfirmedBerthEndEvent",
            "UniqueBerthStartEvent",
            "UniqueBerthEndEvent",
            "EncounterStartEvent",
            "EncounterEndEvent",
        )
        assertEquals(expected, ScenarioPostProcessing.VESSEL_VOYAGE.eventTypeNames)
    }

    @Test
    fun `eventTypeNames - VesselVoyageV2`() {
        val expected = setOf(
            "AnchoredStartEvent",
            "AnchoredEndEvent",
            "AreaStartEvent",
            "AreaEndEvent",
            "ConfirmedBerthStartEvent",
            "ConfirmedBerthEndEvent",
            "UniqueBerthStartEvent",
            "UniqueBerthEndEvent",
            "EncounterStartEvent",
            "EncounterEndEvent",
            "StopStartEvent",
            "StopEndEvent",
        )
        assertEquals(expected, ScenarioPostProcessing.VESSEL_VOYAGE_V2.eventTypeNames)
    }
}
