package nl.teqplay.aisengine.event.model

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.ObjectMapperConfiguration
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.SpeedEvent
import nl.teqplay.aisengine.event.model.hamis.HamisNauticalOrderEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.testing.event.createAisDestinationChangedEvent
import nl.teqplay.aisengine.testing.event.createAisDraughtChangedEvent
import nl.teqplay.aisengine.testing.event.createAisEtaChangedEvent
import nl.teqplay.aisengine.testing.event.createAisLostEvent
import nl.teqplay.aisengine.testing.event.createAisRecoverEvent
import nl.teqplay.aisengine.testing.event.createAisStatusChangedEvent
import nl.teqplay.aisengine.testing.event.createAnchoredEndEvent
import nl.teqplay.aisengine.testing.event.createAnchoredStartEvent
import nl.teqplay.aisengine.testing.event.createAreaEndEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.aisengine.testing.event.createConfirmedBerthEndEvent
import nl.teqplay.aisengine.testing.event.createConfirmedBerthStartEvent
import nl.teqplay.aisengine.testing.event.createEncounterEndEvent
import nl.teqplay.aisengine.testing.event.createEncounterStartEvent
import nl.teqplay.aisengine.testing.event.createEtaEvent
import nl.teqplay.aisengine.testing.event.createEtdEvent
import nl.teqplay.aisengine.testing.event.createHamisAddPortcallVisitEvent
import nl.teqplay.aisengine.testing.event.createHamisAgentOrderEvent
import nl.teqplay.aisengine.testing.event.createHamisAgentReportsTugsEvent
import nl.teqplay.aisengine.testing.event.createHamisAtaEvent
import nl.teqplay.aisengine.testing.event.createHamisAtdEvent
import nl.teqplay.aisengine.testing.event.createHamisCancelPortcallVisitEvent
import nl.teqplay.aisengine.testing.event.createHamisEtaBerthEvent
import nl.teqplay.aisengine.testing.event.createHamisEtaCancelEvent
import nl.teqplay.aisengine.testing.event.createHamisEtaEvent
import nl.teqplay.aisengine.testing.event.createHamisEtaRequestEvent
import nl.teqplay.aisengine.testing.event.createHamisEtdEvent
import nl.teqplay.aisengine.testing.event.createHamisEtdRequestEvent
import nl.teqplay.aisengine.testing.event.createHamisNauticalOrderEvent
import nl.teqplay.aisengine.testing.event.createHamisPilotBoardingEtaEvent
import nl.teqplay.aisengine.testing.event.createHamisPilotOnBoardEndEvent
import nl.teqplay.aisengine.testing.event.createHamisPilotOnBoardStartEvent
import nl.teqplay.aisengine.testing.event.createHamisUpdatePortcallVisitEvent
import nl.teqplay.aisengine.testing.event.createHamisVisitCancellationEvent
import nl.teqplay.aisengine.testing.event.createHamisVisitDeclarationEvent
import nl.teqplay.aisengine.testing.event.createLockEtaEvent
import nl.teqplay.aisengine.testing.event.createLockEtdEvent
import nl.teqplay.aisengine.testing.event.createPortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusAtaEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusAtdEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusEtaBerthEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusEtaEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusEtdEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusPortcallFinishEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusShipChangedEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusVisitsUpdateEvent
import nl.teqplay.aisengine.testing.event.createShipMovingEndEvent
import nl.teqplay.aisengine.testing.event.createShipMovingStartEvent
import nl.teqplay.aisengine.testing.event.createSpeedChangedEvent
import nl.teqplay.aisengine.testing.event.createStopEndEvent
import nl.teqplay.aisengine.testing.event.createStopStartEvent
import nl.teqplay.aisengine.testing.event.createTrueDestinationChangedEvent
import nl.teqplay.aisengine.testing.event.createUniqueBerthEndEvent
import nl.teqplay.aisengine.testing.event.createUniqueBerthStartEvent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.nio.file.Files
import java.nio.file.Paths
import java.time.Instant
import java.util.stream.Stream

@ExtendWith(SpringExtension::class)
@SpringBootTest(classes = [ObjectMapperConfiguration::class])
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class EventModelTest {

    companion object {
        const val TEST_ID = "test-id"
    }

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    data class EventTestData(
        val event: Event,
        val natsSubject: String,
        val eventFile: String
    )

    private fun testData() = Stream.of(
        EventTestData(createAisDestinationChangedEvent(), "event.ais-changed.destination", "AisDestinationChangedEvent.json"),
        EventTestData(createAisDraughtChangedEvent(), "event.ais-changed.draught", "AisDraughtChangedEvent.json"),
        EventTestData(createAisEtaChangedEvent(), "event.ais-changed.eta", "AisEtaChangedEvent.json"),
        EventTestData(createAisStatusChangedEvent(), "event.ais-changed.status", "AisStatusChangedEvent.json"),
        EventTestData(createAisLostEvent(), "event.ais.lost", "AisLostEvent.json"),
        EventTestData(createAisRecoverEvent(), "event.ais.recover", "AisRecoverEvent.json"),
        EventTestData(createAnchoredStartEvent(), "event.anchored.start.anchor.ANCHOR_ID", "AnchoredStartEvent.json"),
        EventTestData(createAnchoredEndEvent(), "event.anchored.end.anchor.ANCHOR_ID", "AnchoredEndEvent.json"),
        EventTestData(createAreaStartEvent(), "event.area.start.port.NLRTM", "AreaStartEventPort.json"),
        EventTestData(createAreaEndEvent(), "event.area.end.port.NLRTM", "AreaEndEventPort.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.BERTH)), "event.area.start.berth.test-id", "AreaStartEventBerth.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(null, AreaIdentifier.AreaType.BERTH, "test-berth-name")), "event.area.start.berth.unknown", "AreaStartEventBerthOnlyName.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.ANCHOR)), "event.area.start.anchor.test-id", "AreaStartEventAnchor.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.PILOT_BOARDING_PLACE)), "event.area.start.pilot-boarding-place.test-id", "AreaStartEventPilotBoardingPlace.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.TERMINAL)), "event.area.start.terminal.test-id", "AreaStartEventTerminal.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.VHF)), "event.area.start.vhf.test-id", "AreaStartEventVhf.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.END_OF_SEA_PASSAGE)), "event.area.start.end-of-sea-passage.test-id", "AreaStartEventEOSP.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.LOCK)), "event.area.start.lock.test-id", "AreaStartEventLock.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.BASIN)), "event.area.start.basin.test-id", "AreaStartEventBasin.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.TUG)), "event.area.start.tug.test-id", "AreaStartEventTug.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.NAUTICAL_MILE)), "event.area.start.nautical-mile.test-id", "AreaStartEventNauticalMile.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.TERMINAL_NEARBY)), "event.area.start.terminal-nearby.test-id", "AreaStartEventTerminalNearby.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.APPROACH_POINT)), "event.area.start.approach-point.test-id", "AreaStartEventApproachPoint.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.FAIRWAY)), "event.area.start.fairway.test-id", "AreaStartEventFairway.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.REGION)), "event.area.start.region.test-id", "AreaStartEventRegion.json"),
        EventTestData(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.CUSTOM)), "event.area.start.custom.test-id", "AreaStartEventCustom.json"),
        EventTestData(createEncounterStartEvent(), "event.encounter.start.bunker", "EncounterStartEvent.json"),
        EventTestData(createEncounterEndEvent(), "event.encounter.end.bunker", "EncounterEndEvent.json"),
        EventTestData(createEncounterStartEvent(encounterType = EncounterEvent.EncounterType.TUG), "event.encounter.start.tug", "EncounterStartEventTug.json"),
        EventTestData(createEncounterEndEvent(encounterType = EncounterEvent.EncounterType.TUG), "event.encounter.end.tug", "EncounterEndEventTug.json"),
        EventTestData(createEncounterStartEvent(encounterType = EncounterEvent.EncounterType.BARGE_BUNKER), "event.encounter.start.barge-bunker", "EncounterStartEventBargeBunker.json"),
        EventTestData(createEncounterEndEvent(encounterType = EncounterEvent.EncounterType.BARGE_BUNKER), "event.encounter.end.barge-bunker", "EncounterEndEventBargeBunker.json"),
        EventTestData(createEtaEvent(), "event.prediction.eta.port.NLRTM", "EtaEvent.json"),
        EventTestData(createEtdEvent(), "event.prediction.etd.port.NLRTM", "EtdEvent.json"),
        EventTestData(createLockEtaEvent(), "event.prediction.lock-eta.lock.unknown", "LockEtaEvent.json"),
        EventTestData(createLockEtdEvent(), "event.prediction.lock-etd.lock.unknown", "LockEtdEvent.json"),
        EventTestData(createPortcallPilotBoardingEtaEvent(), "event.prediction.portcall-pilot-boarding-eta.NLRTM", "PortcallPilotBoardingEtaEvent.json"),
        EventTestData(createShipMovingStartEvent(), "event.ship-moving.start", "ShipMovingStartEvent.json"),
        EventTestData(createShipMovingEndEvent(), "event.ship-moving.end", "ShipMovingEndEvent.json"),
        EventTestData(createSpeedChangedEvent(speedType = SpeedEvent.SpeedType.ACCELERATING), "event.ship-speed-changed.accelerating", "SpeedChangedEvent.json"),
        EventTestData(createSpeedChangedEvent(speedType = SpeedEvent.SpeedType.SLOWING_DOWN), "event.ship-speed-changed.slowing-down", "SpeedChangedEventSlowDown.json"),
        EventTestData(createStopStartEvent(), "event.stop.start", "StopStartEvent.json"),
        EventTestData(createStopEndEvent(), "event.stop.end", "StopEndEvent.json"),
        EventTestData(createTrueDestinationChangedEvent(), "event.true-changed.destination", "TrueDestinationChangedEvent.json"),
        EventTestData(createConfirmedBerthStartEvent(), "event.berth-confirmed.start.berth.BERT", "ConfirmedBerthStartEvent.json"),
        EventTestData(createConfirmedBerthEndEvent(), "event.berth-confirmed.end.berth.BERT", "ConfirmedBerthEndEvent.json"),
        EventTestData(createUniqueBerthStartEvent(), "event.berth-unique.start.berth.BERT", "UniqueBerthStartEvent.json"),
        EventTestData(createUniqueBerthEndEvent(), "event.berth-unique.end.berth.BERT", "UniqueBerthEndEvent.json"),
        EventTestData(createHamisAtaEvent(), "event.external.hamis.actual.ata.berth.BERT", "HamisAtaEvent.json"),
        EventTestData(createHamisAtdEvent(), "event.external.hamis.actual.atd.berth.BERT", "HamisAtdEvent.json"),
        EventTestData(createHamisEtaEvent(), "event.external.hamis.prediction.eta.berth.BERT", "HamisEtaEvent.json"),
        EventTestData(createHamisEtaBerthEvent(), "event.external.hamis.prediction.eta.berth.berth.BERT", "HamisEtaBerthEvent.json"),
        EventTestData(createHamisEtdEvent(), "event.external.hamis.prediction.etd.berth.BERT", "HamisEtdEvent.json"),
        EventTestData(createHamisAddPortcallVisitEvent(), "event.external.hamis.portcall.visit.add", "HamisAddPortcallVisitEvent.json"),
        EventTestData(createHamisUpdatePortcallVisitEvent(), "event.external.hamis.portcall.visit.update", "HamisUpdatePortcallVisitEvent.json"),
        EventTestData(createHamisCancelPortcallVisitEvent(), "event.external.hamis.portcall.visit.cancel", "HamisCancelPortcallVisitEvent.json"),
        EventTestData(createHamisAgentOrderEvent(), "event.external.hamis.agent.order", "HamisAgentOrderEvent.json"),
        EventTestData(createHamisAgentReportsTugsEvent(), "event.external.hamis.agent.reports.tug", "HamisAgentReportsTugsEvent.json"),
        EventTestData(createHamisEtaCancelEvent(), "event.external.hamis.eta.cancel", "HamisEtaCancelEvent.json"),
        EventTestData(createHamisEtaRequestEvent(), "event.external.hamis.eta.request", "HamisEtaRequestEvent.json"),
        EventTestData(createHamisEtdRequestEvent(), "event.external.hamis.etd.request", "HamisEtdRequestEvent.json"),
        EventTestData(createHamisNauticalOrderEvent(), "event.external.hamis.nautical.order", "HamisNauticalOrderEvent.json"),
        EventTestData(createHamisPilotBoardingEtaEvent(), "event.external.hamis.eta.pilot-boarding-place", "HamisPilotBoardingEtaEvent.json"),
        EventTestData(createHamisPilotOnBoardStartEvent(), "event.external.hamis.pilot.onboard.start", "HamisPilotOnBoardStartEvent.json"),
        EventTestData(createHamisPilotOnBoardEndEvent(), "event.external.hamis.pilot.onboard.end", "HamisPilotOnBoardEndEvent.json"),
        EventTestData(createHamisVisitCancellationEvent(), "event.external.hamis.visit.cancel", "HamisVisitCancellationEvent.json"),
        EventTestData(createHamisVisitDeclarationEvent(), "event.external.hamis.visit.declaration", "HamisVisitDeclarationEvent.json"),
        EventTestData(createPortcallPlusAtaEvent(), "event.external.portcallplus.actual.ata.berth.BERT", "PortcallPlusAtaEvent.json"),
        EventTestData(createPortcallPlusAtdEvent(), "event.external.portcallplus.actual.atd.berth.BERT", "PortcallPlusAtdEvent.json"),
        EventTestData(createPortcallPlusEtaEvent(), "event.external.portcallplus.prediction.eta.berth.BERT", "PortcallPlusEtaEvent.json"),
        EventTestData(createPortcallPlusEtaBerthEvent(), "event.external.portcallplus.prediction.eta.berth.berth.BERT", "PortcallPlusEtaBerthEvent.json"),
        EventTestData(createPortcallPlusEtdEvent(), "event.external.portcallplus.prediction.etd.berth.BERT", "PortcallPlusEtdEvent.json"),
        EventTestData(createPortcallPlusAgentChangedEvent(), "event.external.portcallplus.agent.changed", "PortcallPlusAgentChangedEvent.json"),
        EventTestData(createPortcallPlusShipChangedEvent(), "event.external.portcallplus.ship.changed", "PortcallPlusShipChangedEvent.json"),
        EventTestData(createPortcallPlusVisitsUpdateEvent(), "event.external.portcallplus.visits.update", "PortcallPlusVisitsUpdateEvent.json"),
        EventTestData(createPortcallPlusPortcallFinishEvent(), "event.external.portcallplus.portcall.finish", "PortcallPlusPortcallFinishEvent.json")
    )

    @ParameterizedTest
    @MethodSource("testData")
    fun `should generate nats subject as expected`(testData: EventTestData) {
        val result = testData.event.getSubject()

        assertEquals(testData.natsSubject, result)
    }

    @ParameterizedTest
    @MethodSource("testData")
    fun `should created expected json when deserialized`(testData: EventTestData) {
        val result = objectMapper.writeValueAsString(testData.event)
        val expected = objectMapper.readEventFile(testData.eventFile)

        assertEquals(expected, result)
    }

    @Test
    fun `should create expected model from portcall event with ucrn instead of portcallId`() {
        val json = objectMapper.readEventFile("PortcallPlusAgentChangedEventWithUcrn.json")
        val modelFromUcrnJson = json?.let { objectMapper.readValue<PortcallPlusAgentChangedEvent>(it) }
        val expectedModel = createPortcallPlusAgentChangedEvent()
        assertEquals(expectedModel, modelFromUcrnJson)

        val expectedJson = objectMapper.writeValueAsString(expectedModel)
        val resultJson = objectMapper.writeValueAsString(modelFromUcrnJson)
        assertEquals(expectedJson, resultJson)
    }

    @Test
    fun `should create expected model from json when serializing`() {
        val file = this.javaClass.classLoader.getResource("AisDestinationChangedEvent.json")
        val result = file?.let { objectMapper.readValue<AisDestinationChangedEvent>(it) }

        val timestamp = Instant.ofEpochMilli(1682899200000)
        val expected = createAisDestinationChangedEvent(actualTime = timestamp, createdTime = timestamp)

        assertEquals(expected, result)
    }

    @Test
    fun `should create expected model from json when serializing external event`() {
        val file = this.javaClass.classLoader.getResource("HamisNauticalOrderEvent.json")
        val result = file?.let { objectMapper.readValue<HamisNauticalOrderEvent>(it) }

        val timestamp = Instant.ofEpochMilli(1682899200000)
        val expected = createHamisNauticalOrderEvent(createdTime = timestamp)

        assertEquals(expected, result)
    }

    private fun ObjectMapper.readEventFile(resourceLocation: String): String? {
        val eventFileURI = this.javaClass.classLoader.getResource(resourceLocation)?.toURI()
        return eventFileURI?.let { Files.readString(Paths.get(it)) }
    }
}
