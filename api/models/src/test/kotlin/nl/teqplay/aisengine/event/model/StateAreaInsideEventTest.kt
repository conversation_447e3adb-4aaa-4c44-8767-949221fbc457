package nl.teqplay.aisengine.event.model

import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID

class StateAreaInsideEventTest {

    @Test
    fun getSubject() {
        val event = StateAreaInsideEvent(
            _id = UUID.randomUUID().toString(),
            ship = AisShipIdentifier(mmsi = 0),
            area = AreaIdentifier(
                id = "BERTH_ID",
                type = AreaIdentifier.AreaType.BERTH
            ),
            berth = null,
            heading = null,
            draught = null,
            location = Location(0.0, 0.0),
            speedOverGround = 0f,
            actualTime = Instant.EPOCH
        )
        assertEquals(
            "event.area.state-inside.berth.BERTH_ID",
            event.getSubject()
        )
    }
}
