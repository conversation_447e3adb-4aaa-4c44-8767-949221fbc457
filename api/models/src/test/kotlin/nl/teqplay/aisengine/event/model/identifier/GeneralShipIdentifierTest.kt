package nl.teqplay.aisengine.event.model.identifier

import nl.teqplay.aisengine.testing.event.defaultImo
import nl.teqplay.aisengine.testing.event.defaultMmsi
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GeneralShipIdentifierTest {
    @Test
    fun shouldCreateObjectOnlyMmsi() {
        assertDoesNotThrow {
            GeneralShipIdentifier(defaultMmsi, null)
        }
    }

    @Test
    fun shouldCreateObjectOnlyImo() {
        assertDoesNotThrow {
            GeneralShipIdentifier(null, defaultImo)
        }
    }

    @Test
    fun shouldThrowAssertionErrorOnInvalidObjectCreation() {
        assertThrows<AssertionError> {
            GeneralShipIdentifier(null, null)
        }
    }

    @ParameterizedTest
    @MethodSource("testCases")
    fun shouldGetMainIdentifierCorrectly(identifier: GeneralShipIdentifier, expected: String) {
        val result = identifier.getMainIdentifier()
        Assertions.assertEquals(expected, result)
    }

    private fun testCases() = Stream.of(
        Arguments.of(GeneralShipIdentifier(111111111, null), "111111111"),
        Arguments.of(GeneralShipIdentifier(111111111, 1234567), "111111111"),
        Arguments.of(GeneralShipIdentifier(null, 1234567), "I1234567"),
    )
}
