import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.dsl.KotlinVersion
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("ais-engine.kotlin-api-conventions")
}

dependencies {
    api("nl.teqplay.skeleton:models:$skeletonVersion")
    api("nl.teqplay.vesselvoyage:api:$vesselVoyageVersion") {
        exclude(group = "nl.teqplay.platform", module = "api")
    }

    implementation("com.fasterxml.jackson.core:jackson-annotations:$jacksonVersion")

    testImplementation(project(":lib:common"))
    testImplementation(project(":lib:testing-event"))
    testImplementation("nl.teqplay.skeleton:common:$skeletonVersion") {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-web")
    }
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("com.fasterxml.jackson.module:jackson-module-kotlin")
}

tasks.withType<KotlinCompile> {
    compilerOptions {
        freeCompilerArgs.set(listOf("-Xjsr305=strict"))
        jvmTarget.set(JvmTarget.JVM_17)
        languageVersion.set(KotlinVersion.KOTLIN_1_9)
    }
}
