package nl.teqplay.aisengine.nats.stream

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_HISTORY
import nl.teqplay.aisengine.nats.stream.properties.AisStreamNatsProperties
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientHealthBuilder
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@ConditionalOnProperty(prefix = "nats.ais-stream", name = ["enabled"], havingValue = "true")
class AisStreamConsumeHistoryAutoConfiguration(
    private val natsClientBuilder: NatsClientBuilder,
    private val natsClientHealthBuilder: NatsClientHealthBuilder,
    private val properties: AisStreamNatsProperties,
    private val objectMapper: ObjectMapper
) {
    @Bean
    fun natsAisHistoryConsumerStream(): NatsConsumerStream<AisWrapper<out BaseAisMessage>>? {
        return natsClientBuilder.consumerStream(
            config = properties,
            stream = AIS_STREAM_HISTORY,
            deserializer = objectMapper::readValue
        )
    }

    @Bean
    fun natsAisHistoryConsumerStreamHealthIndicator(
        consumerStream: NatsConsumerStream<AisWrapper<out BaseAisMessage>>
    ): HealthIndicator {
        return natsClientHealthBuilder.natsClientHealthIndicator(consumerStream)
    }
}
