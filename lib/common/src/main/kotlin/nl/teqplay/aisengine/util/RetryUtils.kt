package nl.teqplay.aisengine.util

import io.github.oshai.kotlinlogging.KotlinLogging
import java.util.concurrent.TimeUnit

private val LOG = KotlinLogging.logger { }

fun <T> withLimitedRetries(action: () -> T): T {
    var retries = 0
    val maxRetries = 60
    while (true) {
        try {
            // ensure to break out the loop
            return action()
        } catch (e: Exception) {
            LOG.error(e) { "Something went wrong, retry ($retries/$maxRetries)" }

            // retry for a long enough time, otherwise just crash
            if (retries++ < maxRetries) {
                TimeUnit.SECONDS.sleep(5)
            } else {
                throw RuntimeException("Resource unavailable")
            }
        }
    }
}
