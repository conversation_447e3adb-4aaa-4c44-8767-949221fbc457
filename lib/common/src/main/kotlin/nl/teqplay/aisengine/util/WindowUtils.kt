package nl.teqplay.aisengine.util

import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.skeleton.model.TimeWindow
import java.time.Duration

fun TimeWindow.withMargin(marginFrom: Duration, marginTo: Duration): TimeWindow = TimeWindow(
    from = from - marginFrom,
    to = to + marginTo
)

fun TimeWindow.withMargin(windowMargin: Scenario.WindowMargin): TimeWindow = withMargin(
    marginFrom = Duration.ofDays(windowMargin.beforeInDays),
    marginTo = Duration.ofDays(windowMargin.afterInDays)
)

fun TimeWindow.removeMargin(windowMargin: Scenario.WindowMargin): TimeWindow {
    val negatedWindowMargin = Scenario.WindowMargin(
        beforeInDays = -windowMargin.beforeInDays,
        afterInDays = -windowMargin.afterInDays,
    )
    return withMargin(negatedWindowMargin)
}

fun TimeWindow.withMargin(margin: Duration): TimeWindow = withMargin(margin, margin)

fun TimeWindow.removeMargin(margin: Duration): TimeWindow = withMargin(margin.negated(), margin.negated())

fun TimeWindow.contains(other: TimeWindow): Boolean = this.from <= other.from && other.to <= this.to

fun TimeWindow.isOverlapping(other: TimeWindow): Boolean = this.from <= other.to && this.to >= other.from

/**
 * Merges the [windows] when they are overlapping.
 */
fun mergeTimeWindows(windows: List<TimeWindow>): List<TimeWindow> {
    val mergedWindows = mutableListOf<TimeWindow>()
    windows.forEach { window ->
        val overlappingWindow = mergedWindows.find { window.isOverlapping(it) }
        if (overlappingWindow == null) {
            mergedWindows.add(window)
        } else {
            mergedWindows.remove(overlappingWindow)
            mergedWindows.add(
                overlappingWindow.copy(
                    from = minOf(overlappingWindow.from, window.from),
                    to = maxOf(overlappingWindow.to, window.to)
                )
            )
        }
    }
    return mergedWindows
}

/**
 * Coerces the [window] to be within the [boundary].
 */
fun coerceTimeWindowWithinBoundary(window: TimeWindow, boundary: TimeWindow): TimeWindow {
    return TimeWindow(
        from = window.from.coerceIn(boundary.from, boundary.to),
        to = window.to.coerceIn(boundary.from, boundary.to),
    )
}
