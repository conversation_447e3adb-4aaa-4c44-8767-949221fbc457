package nl.teqplay.aisengine.util

import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffField.Change
import nl.teqplay.aisengine.aisstream.model.AisDiffFieldInitialNullable
import nl.teqplay.aisengine.aisstream.model.AisDiffFieldNonNullable
import nl.teqplay.aisengine.aisstream.model.AisDiffFieldNullable
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.aisstream.model.AisMessage

fun getAisDiff(old: AisMessage?, new: AisMessage) =
    AisDiffMessage(
        mmsi = new.mmsi,
        messageTime = new.messageTime,
        oldMessageTime = old?.messageTime,
        sources = new.sources,

        location = diffNonNullNew(old?.location, new.location),
        heading = diffIfNotNullChange(old?.heading, new.heading),
        positionAccuracy = diff(old?.positionAccuracy, new.positionAccuracy),
        speedOverGround = diff(old?.speedOverGround, new.speedOverGround),
        courseOverGround = diff(old?.courseOverGround, new.courseOverGround),
        status = diffIfNotNullChange(old?.status, new.status),
        rateOfTurn = diffIfNotNullChange(old?.rateOfTurn, new.rateOfTurn),
        specialManeuverStatus = diffIfNotNullChange(old?.specialManeuverStatus, new.specialManeuverStatus),

        imo = diff(old?.imo, new.imo),
        name = diff(old?.name, new.name),
        callSign = diff(old?.callSign, new.callSign),
        shipType = diff(old?.shipType, new.shipType),
        draught = diff(old?.draught, new.draught),
        eta = diff(old?.eta, new.eta),
        destination = diff(old?.destination, new.destination),
        transponderPosition = diff(old?.transponderPosition, new.transponderPosition),
        positionSensorType = diff(old?.positionSensorType, new.positionSensorType),
        aisVersion = diff(old?.aisVersion, new.aisVersion),
        usingDataTerminal = diff(old?.usingDataTerminal, new.usingDataTerminal),

        eni = diff(old?.eni, new.eni)
    )

inline fun <reified T : Any> diff(old: T?, new: T?): AisDiffFieldNullable<T> {
    return AisDiffField(
        old = old,
        changed = if (old != new) {
            Change(new)
        } else {
            null
        }
    )
}

/**
 * Get a diff object with a [Change] if the provided [new] is not nullable or this is the first time we get any info for this field.
 */
inline fun <reified T : Any> diffNonNullNew(old: T?, new: T): AisDiffFieldNonNullable<T> {
    return AisDiffField(
        // The old should always be provided, except for the first time we create a diff object
        // This means this is the first time we got this information for this ship
        old = old ?: new,
        changed = if (old == null || old != new) {
            Change(new)
        } else {
            null
        }
    )
}

/**
 * Get the diff object with a [Change] if the provided [new] is a change and not null.
 */
inline fun <reified T : Any> diffIfNotNullChange(old: T?, new: T?): AisDiffFieldInitialNullable<T> {
    return AisDiffField(
        old = old,
        changed = if (new != null && old != new) {
            Change(new)
        } else {
            null
        }
    )
}
