package nl.teqplay.aisengine

import org.springframework.boot.env.YamlPropertySourceLoader
import org.springframework.core.env.CompositePropertySource
import org.springframework.core.env.PropertySource
import org.springframework.core.io.support.DefaultPropertySourceFactory
import org.springframework.core.io.support.EncodedResource
import java.io.IOException

/**
 * Used to specify YAML is used to load properties from a YAML file in an @PropertySource annotation
 *
 * Use like: @PropertySource("classpath:application-lib-bucketing.yml", factory = YamlPropertyLoaderFactory::class)
 */
class YamlPropertyLoaderFactory : DefaultPropertySourceFactory() {

    @Throws(IOException::class)
    override fun createPropertySource(
        name: String?,
        resource: EncodedResource
    ): PropertySource<*> {
        val propertySource = CompositePropertySource(requireNotNull(resource.resource.filename))
        YamlPropertySourceLoader()
            .load(resource.resource.filename, resource.resource)
            .forEach { propertySource.addPropertySource(it) }
        return propertySource
    }
}
