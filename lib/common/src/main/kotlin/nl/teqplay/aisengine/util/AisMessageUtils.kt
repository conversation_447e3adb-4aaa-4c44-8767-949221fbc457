package nl.teqplay.aisengine.util

import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.aisstream.model.AisLongRangeWrapper
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.AisStationWrapper
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.model.TypeNames
import nl.teqplay.skeleton.util.haversineDistance
import kotlin.math.abs

/**
 * Creating correct AIS Source out of [AisWrapper] taking into consideration
 * the message type, source and subSource
 */
private fun getAisSource(wrapper: AisWrapper<out BaseAisMessage>): String {
    val messageType = wrapper.type
    return getAisSource(wrapper.source, wrapper.subSource, messageType)
}

fun getAisSource(
    source: String,
    subSource: String?,
    messageType: String?,
): String {
    val uppercaseMessageType = when (val text = messageType?.uppercase()) {
        TypeNames.POSITION.uppercase(),
        TypeNames.STATIC.uppercase() -> text
        TypeNames.LONGRANGE.uppercase() -> "LONG_RANGE"
        TypeNames.STATION.uppercase() -> "BASE"
        null -> null
        else -> text
    }

    val uppercaseSource = source.uppercase()
    val uppercaseSubSource = with(subSource?.lowercase()) {
        when {
            this == null -> null
            startsWith("t") -> "T" // terrestrial
            startsWith("s") -> "S" // static
            startsWith("d") -> "D" // dynamic
            else -> "U"
        }
    }

    val prefix = when (uppercaseSubSource) {
        null -> "AIS:$uppercaseSource"
        else -> "AIS:$uppercaseSource-$uppercaseSubSource"
    }

    return when (uppercaseMessageType) {
        null -> prefix
        else -> "$prefix:$uppercaseMessageType"
    }
}

data class AisSource(
    val source: String,
    val subSource: String?,
    val messageType: String?,
)

fun parseAisSource(aisSource: String?): AisSource {
    var source = aisSource ?: "UNKNOWN"
    var subSource: String? = null
    var messageType: String? = null
    if (source.startsWith("AIS:")) {
        // source like: "AIS:SPIRE-T:POSITION" or containing multiple like "AIS:SPIRE-T:POSITION, AIS:AISHUB:STATIC"
        // in the latter case we only use the first before the ','
        val tokens = source.substringBefore(",").trim().split(":")
        val tokenSourceAndSubSource = tokens.getOrNull(1)
        val tokenMessageType = tokens.getOrNull(2)

        if (tokenSourceAndSubSource != null) {
            val sourceTokens = tokenSourceAndSubSource.split("-")
            source = sourceTokens[0]
            subSource = sourceTokens.getOrNull(1)?.lowercase()
        }

        if (tokenMessageType != null) {
            messageType = when (val text = tokenMessageType.lowercase()) {
                "long_range" -> TypeNames.LONGRANGE
                "base" -> TypeNames.STATION
                else -> text
            }
        }
    }
    return AisSource(source, subSource, messageType)
}

fun getAisMessage(
    position: AisPositionWrapper?,
    static: AisStaticWrapper?,
    longRange: AisLongRangeWrapper?
): AisMessage? {
    // at least a position or longRange update must exist
    val positionLocation = position?.message?.location
    val longRangeLocation = longRange?.message?.location

    return when {
        longRangeLocation == null -> createAisMessageFromPosition(
            position = position,
            static = static
        )
        positionLocation == null -> createAisMessageFromLongRange(
            longRange = longRange,
            static = static,
            positionMessage = null
        )

        // Only take the long range message if the position change is significant enough
        longRange.timestamp.isAfter(position.timestamp) &&
            abs(haversineDistance(longRangeLocation, positionLocation)) > 300 -> {
            createAisMessageFromLongRange(
                longRange = longRange,
                static = static,
                // We still provide data from the position messages to ensure that we don't flip-flopping
                positionMessage = position.message
            )
        }

        else -> createAisMessageFromPosition(
            position = position,
            static = static
        )
    }
}

fun keepLongRange(longRange: AisLongRangeWrapper, position: AisPositionWrapper?): Boolean {
    val positionLocation = position?.message?.location
    val longRangeLocation = longRange.message.location

    return when {
        longRangeLocation == null -> false
        positionLocation == null -> true
        else -> {
            val moving = longRange.message.speedOverGround?.let { it > 3.0f } ?: false
            val positionUpdated = abs(haversineDistance(longRangeLocation, positionLocation)) > 300
            val newUpdate = longRange.timestamp.isAfter(position.timestamp)

            (newUpdate && positionUpdated) || moving
        }
    }
}

fun createAisMessageFromLongRange(
    longRange: AisLongRangeWrapper?,
    static: AisStaticWrapper?,
    positionMessage: AisPositionMessage?
): AisMessage? {
    val longRangeMessage = longRange?.message

    // at least a longRange update must exist
    val location = longRangeMessage?.location ?: return null
    val maxTimestamp = listOfNotNull(
        longRange.timestamp,
        static?.timestamp,
    ).maxOrNull() ?: return null

    return AisMessage(
        mmsi = longRange.message.mmsi,
        messageTime = maxTimestamp,
        sources = setOfNotNull(getAisSource(longRange), static?.let { getAisSource(it) }),
        location = location,
        positionAccuracy = longRangeMessage.positionAccuracy,
        speedOverGround = longRangeMessage.speedOverGround,
        courseOverGround = longRangeMessage.courseOverGround,
        // We use the fields of the position message as long range messages don't have this information
        status = positionMessage?.status,
        heading = positionMessage?.heading,
        rateOfTurn = positionMessage?.rateOfTurn,
        specialManeuverStatus = positionMessage?.specialManeuverStatus
    ).applyStaticFields(static)
}

fun createAisMessageFromPosition(
    position: AisPositionWrapper?,
    static: AisStaticWrapper?
): AisMessage? {
    // at least a position update must exist
    val location = position?.message?.location ?: return null
    val maxTimestamp = listOfNotNull(
        position.timestamp,
        static?.timestamp,
    ).maxOrNull() ?: return null

    return AisMessage(
        mmsi = position.message.mmsi,
        messageTime = maxTimestamp,
        sources = setOfNotNull(getAisSource(position), static?.let { getAisSource(it) }),
        location = location,
        heading = position.message.heading,
        positionAccuracy = position.message.positionAccuracy,
        speedOverGround = position.message.speedOverGround,
        courseOverGround = position.message.courseOverGround,
        status = position.message.status,
        rateOfTurn = position.message.rateOfTurn,
        specialManeuverStatus = position.message.specialManeuverStatus,
    ).applyStaticFields(static)
}

private fun AisMessage.applyStaticFields(static: AisStaticWrapper?) =
    this.copy(
        imo = static?.message?.imo,
        name = static?.message?.name,
        callSign = static?.message?.callSign,
        shipType = static?.message?.shipType,
        draught = static?.message?.draught,
        eta = static?.message?.eta,
        destination = static?.message?.destination,
        transponderPosition = static?.message?.transponderPosition,
        positionSensorType = static?.message?.positionSensorType,
        aisVersion = static?.message?.aisVersion,
        usingDataTerminal = static?.message?.usingDataTerminal
    )

fun getAisMessage(
    station: AisStationWrapper
): AisMessage? {
    val location = station.message.location ?: return null

    return AisMessage(
        mmsi = station.message.mmsi,
        messageTime = station.timestamp,
        sources = setOf(getAisSource(station)),
        location = location,
        positionAccuracy = station.message.positionAccuracy,
        transponderPosition = station.message.transponderPosition,
        positionSensorType = station.message.positionSensorType
    )
}

fun AisDiffMessage.isEmpty() =
    listOf(
        location, heading, positionAccuracy, speedOverGround, courseOverGround, status, rateOfTurn,
        specialManeuverStatus, imo, name, callSign, shipType, draught, eta, destination, transponderPosition,
        positionSensorType, aisVersion, usingDataTerminal, eni,
    ).none { it.changed != null }
