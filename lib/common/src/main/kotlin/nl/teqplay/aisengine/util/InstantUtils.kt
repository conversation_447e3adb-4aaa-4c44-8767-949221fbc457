package nl.teqplay.aisengine.util

import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

fun Instant.floorToLocalDate(): LocalDate = LocalDate.ofInstant(this, ZoneOffset.UTC)

fun Instant.ceilToLocalDate(): LocalDate {
    val time = floorToLocalDate()
    return if (time.atStartOfDay().toInstant(ZoneOffset.UTC) != this) {
        time.plusDays(1)
    } else {
        time
    }
}
