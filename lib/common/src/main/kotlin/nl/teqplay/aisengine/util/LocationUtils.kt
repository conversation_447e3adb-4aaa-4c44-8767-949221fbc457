package nl.teqplay.aisengine.util

import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode

fun roundToDecimalsAsBigDecimal(value: Double, decimals: Int): BigDecimal = value
    .toBigDecimal(MathContext.DECIMAL64)
    .setScale(decimals, RoundingMode.HALF_DOWN)

fun floorLatitudeToDecimals(lat: Double, decimals: Int): Double =
    normalizeZero(floorToDecimals(lat, decimals))

fun floorLongitudeToDecimals(lon: Double, decimals: Int): Double =
    normalizeZero(floorToDecimals(normalizeLongitude(lon), decimals))

fun floorToDecimals(value: Double, decimals: Int): Double = value
    .toBigDecimal(MathContext.DECIMAL64)
    .setScale(decimals, RoundingMode.FLOOR)
    .toDouble()

/**
 * Change a negative zero -0.0 into a positive zero 0.0.
 * Leave value as is when non zero.
 */
fun normalizeZero(value: Double): Double {
    return if (value == -0.0) 0.0 else value
}

/**
 * Normalize a longitude into the range [-180, 180)
 * Start is included, end is excluded.
 */
fun normalizeLongitude(longitude: Double): Double {
    // Almost all values are already in the range [-180, 180)
    if (longitude >= -180.0 && longitude < 180.0) {
        return longitude
    }

    // Performing modulo on the original double messed up the decimals, which in turn messed up the rounding for the buckets
    val (intLon, restLon) = splitDouble(longitude)
    val normalizedLongitude = (intLon + 180) % 360 - 180 + restLon

    return if (normalizedLongitude < -180.0) {
        normalizedLongitude + 360.0
    } else {
        normalizedLongitude
    }
}

fun splitDouble(value: Double): Pair<Int, Double> {
    val intPart = value.toInt()
    val fracPart = value - intPart
    return intPart to fracPart
}

fun roundToDecimalString(value: Double, decimals: Int): String {
    return roundToDecimalsAsBigDecimal(value, decimals).toPlainString()
}
