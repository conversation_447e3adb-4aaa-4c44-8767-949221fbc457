package nl.teqplay.aisengine.datasource

import com.mongodb.client.model.BulkWriteOptions
import com.mongodb.client.model.InsertOneModel
import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisState
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.lt
import nl.teqplay.skeleton.datasource.kmongo.or
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.system.measureTimeMillis

private val LOG = KotlinLogging.logger {}

/**
 * Data source to persist the [AisState] which is kept in the [nl.teqplay.aisengine.service.StateService].
 */
abstract class StateDataSource<S, T : StateWrapper<S>>(
    mongoDatabase: MongoDatabase,
    stateClass: Class<T>
) {
    companion object {
        /**
         * The amount of hours we want to load when loading in our initial ship state at start-up
         */
        const val INITIAL_LOADING_HOURS = 4L
    }

    protected val collection = mongoDatabase.getCollection("state", stateClass)
        .apply(::applyIndexes)

    open fun applyIndexes(collection: MongoCollection<T>) {
        // Add an index on the time field to speed up loading in the initial state
        collection.ensureIndex(StateWrapper<S>::updatedAt)
    }

    abstract fun toStateWrapper(entry: Map.Entry<Int, S>): T

    /** Get the number of ships for which we have state. Used to size the initial state collection. */
    val size: Long
        get() = collection.estimatedDocumentCount()

    /**
     * Load in the ship state for only the first [INITIAL_LOADING_HOURS] hours of data, leaving out all data which isn't being updated
     */
    fun getInitialState(now: Instant = Instant.now()): FindIterable<T> {
        // Only load the state that is recent
        val dataEdge = now.minus(INITIAL_LOADING_HOURS, ChronoUnit.HOURS)
        val stateTimeField = StateWrapper<S>::updatedAt
        val filter = stateTimeField gte dataEdge
        return collection.find(filter)
    }

    /**
     * Load in the ship state older than [INITIAL_LOADING_HOURS] hours of data
     */
    fun getOlderState(now: Instant = Instant.now()): FindIterable<T> {
        // Only load the state that is recent
        val dataEdge = now.minus(INITIAL_LOADING_HOURS, ChronoUnit.HOURS)
        val stateTimeField = StateWrapper<S>::updatedAt
        val filter = or(stateTimeField lt dataEdge, stateTimeField eq null)
        return collection.find(filter)
    }

    /**
     * Find the ship state by the provided [id]
     */
    fun findStateById(id: Int): T? {
        return collection.findOneById(id)
    }

    /** Bulk update the state for the given MMSIs. */
    fun updateState(states: Map<Int, S>) {
        var count = 0
        states.entries.chunked(2000) { chunk ->
            collection.deleteMany(StateWrapperInterface::_id `in` chunk.map { it.key })
            val writes = chunk.map { InsertOneModel(toStateWrapper(it)) }
            collection.bulkWrite(writes, BulkWriteOptions().ordered(false))
            count += chunk.size
            LOG.info { "Wrote $count/${states.size} updates" }
        }
    }

    /** Bulk delete the given MMSIs. */
    fun remove(mmsis: Set<Int>) {
        val t = measureTimeMillis {
            mmsis.chunked(2000) { chunk ->
                collection.deleteMany(StateWrapperInterface::_id `in` chunk)
            }
        }
        LOG.info { "Deleted ${mmsis.size} entries in ${t}ms" }
    }
}

interface StateWrapper<S> : StateWrapperInterface {
    override val _id: Int
    val state: S
    val updatedAt: Instant
}

interface StateWrapperInterface {
    val _id: Int
}
