package nl.teqplay.aisengine.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.aisstream.model.AisState
import nl.teqplay.aisengine.model.MinimalState
import java.time.Instant

abstract class MinimalStateDataSource<S : AisState<S>, T : StateWrapper<S>>(
    mongoDatabase: MongoDatabase,
    stateClass: Class<T>
) : StateDataSource<S, T>(mongoDatabase, stateClass)

class MinimalStateDataSourceImpl(
    mongoDatabase: MongoDatabase
) : MinimalStateDataSource<MinimalState, MinimalStateWrapper>(
    mongoDatabase = mongoDatabase,
    stateClass = MinimalStateWrapper::class.java
) {
    override fun toStateWrapper(entry: Map.Entry<Int, MinimalState>): MinimalStateWrapper {
        return MinimalStateWrapper(
            _id = entry.key,
            state = entry.value,
            updatedAt = Instant.now()
        )
    }
}

data class MinimalStateWrapper(
    override val _id: Int,
    override val state: MinimalState,
    override val updatedAt: Instant = Instant.now()
) : StateWrapper<MinimalState>
