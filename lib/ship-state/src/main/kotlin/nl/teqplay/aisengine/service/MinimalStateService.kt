package nl.teqplay.aisengine.service

import nl.teqplay.aisengine.aisstream.model.AisLongRangeWrapper
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisState
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.AisStationWrapper
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.datasource.MinimalStateDataSource
import nl.teqplay.aisengine.datasource.MinimalStateDataSourceImpl
import nl.teqplay.aisengine.datasource.MinimalStateWrapper
import nl.teqplay.aisengine.datasource.StateWrapper
import nl.teqplay.aisengine.model.MinimalState
import java.time.Duration

abstract class MinimalStateService<S : AisState<S>, T : StateWrapper<S>>(
    stateDataSource: MinimalStateDataSource<S, T>,
    syncInterval: Duration
) : StateService<S, T>(stateDataSource, syncInterval) {

    override fun update(wrapper: AisWrapper<out BaseAisMessage>) {
        val current = getStateByMmsi(wrapper.message.mmsi)
        process(wrapper, current)
        updateState(wrapper)
    }

    abstract fun process(wrapper: AisWrapper<out BaseAisMessage>, previousState: S?)
}

abstract class MinimalStateServiceImpl(
    stateDataSource: MinimalStateDataSourceImpl,
    syncInterval: Duration
) : MinimalStateService<MinimalState, MinimalStateWrapper>(stateDataSource, syncInterval) {
    override fun createEmptyState(): MinimalState = MinimalState()

    override fun initialState(wrapper: AisWrapper<out BaseAisMessage>) = when (wrapper) {
        is AisPositionWrapper -> MinimalState(position = wrapper)
        is AisStaticWrapper -> MinimalState(static = wrapper)
        is AisStationWrapper -> MinimalState(station = wrapper)
        is AisLongRangeWrapper -> MinimalState(longrange = wrapper)
    }
}
