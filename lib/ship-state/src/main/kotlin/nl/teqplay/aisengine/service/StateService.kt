package nl.teqplay.aisengine.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisState
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.model.latestTimestamp
import nl.teqplay.aisengine.datasource.StateDataSource
import nl.teqplay.aisengine.datasource.StateWrapper
import java.time.Duration
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.concurrent.thread
import kotlin.system.measureTimeMillis

private val LOG = KotlinLogging.logger {}

abstract class StateService<S : AisState<S>, T : StateWrapper<S>>(
    private val stateDataSource: StateDataSource<S, T>,
    private val syncInterval: Duration
) {
    /**
     * Queue on which we can sleep but be interrupted by inserting a value. Used as an alternative to Thread.sleep()
     * and Thread#interrupt, which breaks the mongo client.
     */
    private val interrupt = LinkedBlockingQueue<Boolean>(1)

    /**
     * The last known state for every MMSI. On startup the [StateDataSource] is used to construct an appropriately-
     * sized map, and to fill it with the saved state.
     */
    private val state = ConcurrentHashMap<Int, S>(stateDataSource.size.toInt(), 0.75f, 4)
        .apply {
            LOG.info { "Loading ship state from database" }
            val duration = measureTimeMillis {
                stateDataSource.getInitialState().associateByTo(this, { it._id }, { it.state })
            }
            val speed = 1000.0 / duration * size
            LOG.info { "Restored $size ships ($duration ms, ${"%.1f".format(speed)} ships/sec)" }
        }

    private val flushThread = thread(start = false, name = "DB flush", block = ::flusher)

    /**
     * @return The ship state as a read only map.
     */
    fun getStateAsMap(): Map<Int, S> {
        return state.toMap()
    }

    /** Get the current number of entries in the state map. */
    val size
        get() = state.size

    /**
     * Get the state of a ship by its [mmsi].
     * Optionally execute the provided [remappingFunction] when we found any state from the database.
     *
     * @param mmsi The MMSI of the Ship we want to find the state for.
     * @param onLazyLoad callback function which is executed when we had to lazy load the state from the database.
     * @param remappingFunction A function which remaps the found [S] into an updated [S].
     * @return The found state, optionally remapped, or an empty state when we couldn't find it in our database.
     */
    fun getStateByMmsi(mmsi: Int, onLazyLoad: ((S?) -> Unit)? = null, remappingFunction: ((S) -> (S))? = null): S? {
        // Use compute to ensure exclusive access to the state
        return state.compute(mmsi) { _, value ->
            val foundState = if (value == null) {
                val databaseState = stateDataSource.findStateById(mmsi)?.state

                // Execute callback function when provided
                if (onLazyLoad != null) {
                    onLazyLoad(databaseState)
                }

                databaseState?.also {
                    LOG.debug { "Lazy loaded state from database for $mmsi" }
                }
            } else {
                LOG.trace { "Found state for $mmsi from in-memory map" }
                value
            }

            if (foundState != null) {
                // Only remap the state if we provided a remapping function
                if (remappingFunction != null) {
                    remappingFunction(foundState)
                } else {
                    // Nothing has to be done, we can just return the state
                    foundState
                }
            } else {
                LOG.warn { "Could not find state for $mmsi, this must be a new or fake ship" }
                // We create an empty state here, so we don't have to spam the database if we get a lot of data from a new ship
                createEmptyState()
            }
        }
    }

    /**
     * Remove entries from the state that are older than the given [age], as determined by looking at
     * [AisState.latestTimestamp].
     */
    fun expire(age: Instant): Int {
        val toBeRemoved = mutableSetOf<Int>()

        state.forEach { (mmsi, value) ->
            val timestamp = value.latestTimestamp()

            if (timestamp == null || timestamp < age) {
                val s = state.remove(mmsi)

                if (s != null && s.latestTimestamp() != timestamp) {
                    // Someone modified the state, add it back
                    state[mmsi] = s
                } else {
                    toBeRemoved.add(mmsi)
                }
            }
        }

        if (toBeRemoved.isNotEmpty()) {
            stateDataSource.remove(toBeRemoved)
        }

        return toBeRemoved.size
    }

    /**
     * Helper function used to create an empty state.
     *
     * @return An empty state of type [S].
     */
    abstract fun createEmptyState(): S

    /**
     * Set containing MMSIs that have been updated but not yet been written to the database.
     */
    private val writeUpdates = mutableSetOf<Int>()

    /**
     * AtomicBoolean used as spinlock to control access to the writeUpdates set. Using synchronized is less performant
     * and causes issues with the flush task not being able to get the lock, since there are so many updates.
     */
    private val writeUpdatesSpinlock = AtomicBoolean(false)

    private fun acquireWriteUpdatesSpinLock(action: () -> Unit) {
        while (!writeUpdatesSpinlock.compareAndSet(false, true)) {
        }
        action()
        writeUpdatesSpinlock.set(false)
    }

    /**
     * When the application has fully initialized, this function starts the publisher threads.
     */
    open fun startup() {
        flushThread.start()
    }

    open fun shutdown() {
        LOG.info { "Shutting down state flush thread..." }
        interrupt.put(true)
        flushThread.join(Duration.ofMinutes(1).toMillis())
    }

    abstract fun update(wrapper: AisWrapper<out BaseAisMessage>)

    /**
     * Convert the given [wrapper] message to the initial state for the state map. This stores the wrapper in the
     * correct field of the state data class based on the type.
     */
    protected abstract fun initialState(wrapper: AisWrapper<out BaseAisMessage>): S

    protected fun updateState(
        wrapper: AisWrapper<out BaseAisMessage>
    ) = updateState(wrapper) {}

    protected fun updateState(
        wrapper: AisWrapper<out BaseAisMessage>,
        onUpdate: (S) -> Unit
    ) {
        var updated = false

        // Convert the wrapper into the initial state for this vessel
        val newValue = initialState(wrapper)

        // Try to find the state by mmsi to ensure we've loaded in the state
        getStateByMmsi(wrapper.message.mmsi)

        // Merge or insert the initial state in the state map for this mmsi
        val inserted = state.merge(
            wrapper.message.mmsi, newValue
        ) { old, new ->
            // Copy the old state, and select the latest version for all types of AIS messages we track
            val newState = old.apply(
                position = latest(old.position, new.position),
                static = latest(old.static, new.static),
                station = latest(old.station, new.station),
                longrange = latest(old.longrange, new.longrange)
            )

            // Check if any of the messages have been updated. Done using reference equality for performance.
            if (old.position !== newState.position ||
                old.static !== newState.static ||
                old.station !== newState.station ||
                old.longrange !== newState.longrange
            ) {
                updated = true
            }

            // Return the new merged state, this is inserted into the map
            newState
        }

        // If the message is completely new, the lambda above is not executed. We can detect this by the fact that the
        // returned value is referentially equal to the initial value we created. In this case, we also trigger an
        // update
        if (inserted === newValue) updated = true

        if (updated) {
            onUpdate(inserted ?: newValue)

            val mmsi = wrapper.message.mmsi
            addToWriteUpdates(mmsi)
        }
    }

    protected fun addToWriteUpdates(mmsi: Int) {
        acquireWriteUpdatesSpinLock {
            writeUpdates.add(mmsi)
        }
    }

    /**
     * Convenience function to quickly return the latest of two wrapped AIS messages, based on [AisWrapper.timestamp].
     */
    private inline fun <reified T : AisWrapper<*>> latest(one: T?, two: T?): T? {
        return when {
            one == null -> two
            two == null -> one
            one.timestamp > two.timestamp -> one
            else -> two
        }
    }

    /**
     * Thread that flushes updated state to the database.
     */
    private fun flusher() {
        var running = true

        LOG.info { "State flush thread starting" }

        while (running) {
            try {
                val interrupted = interrupt.poll(syncInterval.toMillis(), TimeUnit.MILLISECONDS)

                if (interrupted == true) {
                    running = false
                }

                flushState()
            } catch (e: Throwable) {
                LOG.error(e) { "Failed flushing updates" }
            }
        }

        LOG.info { "State flush thread finished" }
    }

    private fun flushState() {
        LOG.info { "Starting state flush" }

        var updates = 0

        val duration = measureTimeMillis {
            // Get the write update spinlock and make a copy of the mmsis we need to flush
            val copiedUpdates = mutableSetOf<Int>()

            acquireWriteUpdatesSpinLock {
                copiedUpdates.addAll(writeUpdates)
                writeUpdates.clear()
            }

            // Get the state for those mmsis
            val toWrite = copiedUpdates.mapNotNull { mmsi ->
                getStateByMmsi(mmsi)?.let { mmsi to it }
            }.toMap()

            updates = toWrite.size

            // And update the database with it
            LOG.info { "State captured, writing $updates updates to database" }
            stateDataSource.updateState(toWrite)
        }

        val speed = 1000.0 / duration * updates
        LOG.info { "Wrote $updates state updates to database ($duration ms, ${"%.1f".format(speed)} updates/sec)" }
    }
}
