package nl.teqplay.aisengine.model

import nl.teqplay.aisengine.aisstream.model.AisLongRangeWrapper
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisState
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.AisStationWrapper

/**
 * Data class for the last-known state of a ship.
 */
data class MinimalState(
    /** Last-known AIS position message. */
    override val position: AisPositionWrapper? = null,

    /** Last-known AIS static message. */
    override val static: AisStaticWrapper? = null,

    /** Last-known AIS station message. */
    override val station: AisStationWrapper? = null,

    /** Last-known AIS long range message. */
    override val longrange: AisLongRangeWrapper? = null
) : AisState<MinimalState> {
    override fun apply(
        position: AisPositionWrapper?,
        static: AisStaticWrapper?,
        station: AisStationWrapper?,
        longrange: AisLongRangeWrapper?
    ): MinimalState = copy(
        position = position,
        static = static,
        station = station,
        longrange = longrange
    )
}
