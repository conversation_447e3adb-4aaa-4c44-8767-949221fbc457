package nl.teqplay.aisengine.bucketing.storage.event.ship

import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketCache
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.skeleton.model.TimeWindow

interface EventByShipCache : BucketCache<Event, String, BucketId<String>> {
    private fun findHistory(
        window: TimeWindow,
        shipId: String,
        extendToLongTerm: Boolean
    ): Sequence<Event> {
        return fetchHistoryInTimeWindow(
            window = window,
            extendToLongTerm = extendToLongTerm,
            key = shipId
        )
    }

    /**
     * Find the [Event] history of a ship in the provided time [window] using the given [mmsi].
     *
     * @param window The [TimeWindow] we want to search [Event]s in.
     * @param mmsi The MMSI of the vessel we want to find the events for.
     * @return All events found for this [mmsi] in the given time [window].
     */
    fun findHistoryByMmsi(
        window: TimeWindow,
        mmsi: Int,
        extendToLongTerm: Boolean = true
    ): Sequence<Event> {
        return findHistory(
            window = window,
            shipId = mmsi.toString(),
            extendToLongTerm = extendToLongTerm
        )
    }

    /**
     * Find the [Event] history of a ship in the provided time [window] using the given [imo].
     *
     * @param window The [TimeWindow] we want to search [Event]s in.
     * @param imo The IMO of the vessel we want to find the events for.
     * @return All events found for this [imo] number in the given time [window].
     */
    fun findHistoryByImo(
        window: TimeWindow,
        imo: Int,
        extendToLongTerm: Boolean = true
    ): Sequence<Event> {
        val shipId = "I$imo"

        return findHistory(
            window = window,
            shipId = shipId,
            extendToLongTerm = extendToLongTerm
        )
    }
}
