package nl.teqplay.aisengine.bucketing.storage.event.area

import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketCache
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.skeleton.util.createBoundingBoxFromCircle
import nl.teqplay.skeleton.util.haversineDistance
import nl.teqplay.skeleton.util.pointInBoundingBox
import nl.teqplay.skeleton.util.pointInPolygon

/**
 * Base interface which adds the ability to retrieve events from an area.
 *
 * @param Data The event we retrieve from the bucketing.
 * @param Key The value used in the [ID].
 * @param ID The [AreaBucketId] type which can be with or without a category.
 */
interface BaseEventByAreaCache<Data : Any, Key : Any, ID : AreaBucketId<Key>> : BucketCache<Data, Key, ID> {

    /**
     * Retrieve the [Location] from the [event]. We can later use this [Location] to filter out events
     *  which are in the buckets but don't need to be returned.
     *
     * @param event The event used to retrieve the location from.
     * @return The [Location] of the [event].
     */
    fun getLocationFromEvent(event: Data): Location

    fun findHistoryInBoundingBox(
        window: TimeWindow,
        boundingBox: BoundingBox,
        sort: Boolean,
        extendToLongTerm: Boolean = true
    ): Sequence<Data> = fetchHistoryInTimeWindow(
        window = window,
        getBucketIds = { factory.bucketIdentifier.id.getBucketIds(it, boundingBox) },
        getArchiveBucketIds = { factory.bucketIdentifier.id.getArchiveBucketIds(it, boundingBox) },
        sort = sort,
        extendToLongTerm = extendToLongTerm
    ).filter { event -> pointInBoundingBox(boundingBox, getLocationFromEvent(event)) }

    fun findHistoryInPolygon(
        window: TimeWindow,
        polygon: Polygon,
        sort: Boolean,
        extendToLongTerm: Boolean = true
    ): Sequence<Data> {
        val boundingBox = BoundingBox.fromPolygon(polygon)
        return findHistoryInBoundingBox(window, boundingBox, sort, extendToLongTerm)
            .filter { event -> pointInPolygon(polygon, getLocationFromEvent(event)) }
    }

    fun findHistoryInCircle(
        window: TimeWindow,
        center: Location,
        radiusInKm: Double,
        sort: Boolean,
        extendToLongTerm: Boolean = true
    ): Sequence<Data> {
        // Find the bounding box enclosing this circle
        // TODO: this method can be optimized by more accurately calculating the buckets inside the circle instead of a bounding box around the circle
        val radiusInMeters = radiusInKm * 1000
        val boundingBox = createBoundingBoxFromCircle(center, radiusInMeters)
        return findHistoryInBoundingBox(window, boundingBox, sort, extendToLongTerm)
            .filter { event -> haversineDistance(center, getLocationFromEvent(event)) <= radiusInMeters }
    }
}
