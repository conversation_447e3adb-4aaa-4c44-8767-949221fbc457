package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByMmsi
import nl.teqplay.aisengine.bucketing.model.TeqplayEventResolver
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.event.ship.bucket.PlatformTeqplayEventBucketIdentifierByMmsi
import nl.teqplay.platform.model.event.TeqplayEvent

/**
 * A [BucketFactory] for handling [TeqplayEvent]
 */
object TeqplayEventBucketFactory :
    BucketFactory<TeqplayEvent, PlatformEventBucketKeyPairByMmsi, BucketId<PlatformEventBucketKeyPairByMmsi>> {

    override val dataClass = TeqplayEvent::class.java

    override val resolver = TeqplayEventResolver
    override val bucketIdentifier = PlatformTeqplayEventBucketIdentifierByMmsi
    override val bucketFormatter = DefaultBucketFormatter<TeqplayEvent>()
}
