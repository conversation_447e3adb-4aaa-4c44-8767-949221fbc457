package nl.teqplay.aisengine.bucketing.storage.event.ship.bucket

import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByMmsi
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketKey
import nl.teqplay.aisengine.bucketing.util.createBucket<PERSON>ey
import nl.teqplay.platform.model.event.TeqplayEvent

object PlatformTeqplayEventBucketKeyByMmsi : BucketKey<TeqplayEvent, PlatformEventBucketKeyPairByMmsi> {
    override fun getBucketKey(item: TeqplayEvent): PlatformEventBucketKeyPairByMmsi = item.createBucketKey()
}
