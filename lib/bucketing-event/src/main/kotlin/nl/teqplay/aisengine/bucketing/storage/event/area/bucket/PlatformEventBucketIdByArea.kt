package nl.teqplay.aisengine.bucketing.storage.event.area.bucket

import nl.teqplay.aisengine.bucketing.BUCKET_ID_SEPARATOR
import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByArea
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId.Companion.ARCHIVE_BUCKET_LOCATION_DECIMALS
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId.Companion.BUCKET_LOCATION_DECIMALS
import nl.teqplay.aisengine.bucketing.storage.bucket.shared.BucketIdByArea.floorToBucketSize
import nl.teqplay.platform.model.event.TeqplayEvent.EventCategory
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import java.time.LocalDate

object PlatformEventBucketIdByArea : EventAreaBucketId<PlatformEventBucketKeyPairByArea> {

    override fun getBucketId(date: LocalDate, key: PlatformEventBucketKeyPairByArea, decimals: Int): String {
        val (location, category) = key
        val flooredLocation = floorToBucketSize(location, decimals)
        val flooredLon = roundToDecimalString(flooredLocation.lon, decimals)
        val flooredLat = roundToDecimalString(flooredLocation.lat, decimals)
        return "$date,$flooredLon,$flooredLat,$category"
    }

    override fun getArchiveBucketId(bucketId: String): String {
        val (date, lon, lat, category) = bucketId.split(BUCKET_ID_SEPARATOR)
        return getBucketId(
            date = LocalDate.parse(date),
            key = PlatformEventBucketKeyPairByArea(
                location = Location(
                    lat = lat.toDouble(),
                    lon = lon.toDouble()
                ),
                category = EventCategory.valueOf(category)
            ),
            decimals = ARCHIVE_BUCKET_LOCATION_DECIMALS
        )
    }

    override fun getBucketIds(window: TimeWindow, boundingBox: BoundingBox): Iterator<String> {
        return getBucketIds(
            window = window,
            boundingBox = boundingBox,
            decimals = BUCKET_LOCATION_DECIMALS,
            keys = createKeys(EventCategory.values().toSet())
        )
    }

    override fun getArchiveBucketIds(window: TimeWindow, boundingBox: BoundingBox): Iterator<String> {
        return getBucketIds(
            window = window,
            boundingBox = boundingBox,
            decimals = ARCHIVE_BUCKET_LOCATION_DECIMALS,
            keys = createKeys(EventCategory.values().toSet())
        )
    }

    private fun createKeys(categories: Set<EventCategory>): (Location) -> Iterator<PlatformEventBucketKeyPairByArea> = { location ->
        iterator {
            categories.forEach { category ->
                yield(PlatformEventBucketKeyPairByArea(location, category))
            }
        }
    }
}
