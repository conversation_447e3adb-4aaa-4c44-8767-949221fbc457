package nl.teqplay.aisengine.bucketing.storage.event.ship.bucket

import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByMmsi
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifier
import nl.teqplay.platform.model.event.TeqplayEvent

object PlatformTeqplayEventBucketIdentifierByMmsi :
    BucketIdentifier<TeqplayEvent, PlatformEventBucketKeyPairByMmsi, BucketId<PlatformEventBucketKeyPairByMmsi>> {
    override val id = PlatformEventBucketIdByMmsi
    override val key = PlatformTeqplayEventBucketKeyByMmsi
    override val archive = defaultArchive()
}
