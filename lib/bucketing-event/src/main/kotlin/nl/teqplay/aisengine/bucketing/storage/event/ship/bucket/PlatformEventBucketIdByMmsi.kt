package nl.teqplay.aisengine.bucketing.storage.event.ship.bucket

import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByMmsi
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import java.time.LocalDate

object PlatformEventBucketIdByMmsi : BucketId<PlatformEventBucketKeyPairByMmsi> {

    /**
     * Pattern: "date,mmsi,category", where date is formatted as "YYYY-MM-DD". Example:
     * "2016-11-25,244003000,BASIC"
     */
    override fun getBucketId(date: LocalDate, key: PlatformEventBucketKeyPairByMmsi) = "$date,${key.mmsi},${key.category}"
}
