package nl.teqplay.aisengine.bucketing.model

import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import nl.teqplay.aisengine.bucketing.util.getTimestamp
import nl.teqplay.platform.model.event.TeqplayEvent

/**
 * Instance of [BucketItemResolver] which is a helper class used to retrieve the timestamp and id from [TeqplayEvent].
 * @see BucketItemResolver
 */
object TeqplayEventResolver : BucketItemResolver<TeqplayEvent> {
    override fun toTimestamp(item: TeqplayEvent) = item.getTimestamp()
    override fun toId(item: TeqplayEvent) = item.uuid
}
