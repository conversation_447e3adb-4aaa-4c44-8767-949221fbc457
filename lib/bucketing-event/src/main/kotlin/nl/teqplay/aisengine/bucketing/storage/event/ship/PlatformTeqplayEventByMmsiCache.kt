package nl.teqplay.aisengine.bucketing.storage.event.ship

import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByMmsi
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketCache
import nl.teqplay.platform.model.event.TeqplayEvent

interface PlatformTeqplayEventByMmsiCache : BucketCache<TeqplayEvent, PlatformEventBucketKeyPairByMmsi,
        BucketId<PlatformEventBucketKeyPairByMmsi>>
