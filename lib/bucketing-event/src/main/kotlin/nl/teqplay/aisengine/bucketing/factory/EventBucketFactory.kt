package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.model.EventResolver
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.event.ship.bucket.EventBucketIdentifierByShip
import nl.teqplay.aisengine.event.interfaces.Event

/**
 * A [BucketFactory] for handling [Event]
 */
object EventBucketFactory : BucketFactory<Event, String, BucketId<String>> {

    override val dataClass = Event::class.java

    override val resolver = EventResolver
    override val bucketIdentifier = EventBucketIdentifierByShip
    override val bucketFormatter = DefaultBucketFormatter<Event>()
}
