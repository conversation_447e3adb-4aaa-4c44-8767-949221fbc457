package nl.teqplay.aisengine.bucketing.model

import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import nl.teqplay.aisengine.bucketing.util.getTimestamp
import nl.teqplay.aisengine.event.interfaces.Event

/**
 * Instance of [BucketItemResolver] which is a helper class used to retrieve the timestamp and id from [Event].
 * @see BucketItemResolver
 */
object EventResolver : BucketItemResolver<Event> {
    override fun toTimestamp(item: Event) = item.getTimestamp()
    override fun toId(item: Event) = item._id
}
