package nl.teqplay.aisengine.bucketing.storage.event.area.bucket

import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByArea
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketKey
import nl.teqplay.aisengine.bucketing.util.createBucketKey
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent

object PlatformTeqplayEventBucketKeyByArea : Bucket<PERSON>ey<TeqplayLocationBasedEvent, PlatformEventBucketKeyPairByArea> {
    override fun getBucketKey(item: TeqplayLocationBasedEvent): PlatformEventBucketKeyPairByArea = item.createBucketKey()
}
