package nl.teqplay.aisengine.bucketing.storage.event.ship.implementation

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.factory.EventBucketFactory
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.EventByShipArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCache
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.event.ship.EventByShipCache
import nl.teqplay.aisengine.event.interfaces.Event

/**
 * Read-only cache for event history by mmsi
 */
class EventByShipReadCache(
    bucketCacheFactory: BucketCacheFactory,
    archiveStorageFactory: ArchiveStorageFactory,
    mongoDatabase: MongoDatabase,
    objectMapper: ObjectMapper,
    bucket: BucketProperties,
    archive: EventByShipArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties
) : EventByShipCache,
    BucketReadCache<Event, String, BucketId<String>>(
        bucketCacheFactory.bucketReadCacheFactory(
            collectionName = EventByShipBucketDetails.collectionName,
            unorderedCollectionSuffix = EventByShipBucketDetails.unorderedCollectionSuffix,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,

            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            factory = EventBucketFactory,
            createArchiveReadStorage = archiveStorageFactory.archiveReadStoragePerMonth()
        )
    )
