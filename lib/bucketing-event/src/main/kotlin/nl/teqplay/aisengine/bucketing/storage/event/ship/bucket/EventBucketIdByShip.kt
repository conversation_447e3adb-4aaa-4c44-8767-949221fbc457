package nl.teqplay.aisengine.bucketing.storage.event.ship.bucket

import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import java.time.LocalDate

object EventBucketIdByShip : BucketId<String> {

    /**
     * Pattern: "date,mmsi", where date is formatted as "YYYY-MM-DD".
     * For example: "2016-11-25,244003000"
     */
    override fun getBucketId(date: LocalDate, key: String) = "$date,$key"
}
