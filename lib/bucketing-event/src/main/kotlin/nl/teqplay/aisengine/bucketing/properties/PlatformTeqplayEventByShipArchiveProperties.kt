package nl.teqplay.aisengine.bucketing.properties

import com.amazonaws.regions.Regions
import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.LocalDate

/**
 * Properties for fetching Platform [TeqplayEvent] by entity from platform's archive
 */
@ConfigurationProperties(prefix = "bucket.archive.platform.event.ship")
data class PlatformTeqplayEventByShipArchiveProperties(
    override val enabled: Boolean,
    override val prefix: String,
    override val name: String,
    override val region: Regions
) : BucketArchiveConfiguration {
    override val writeNotBefore: LocalDate
        get() = LocalDate.MAX
    override val prefixSeparator: String
        get() = "_"
}
