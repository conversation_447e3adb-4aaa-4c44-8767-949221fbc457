package nl.teqplay.aisengine.bucketing.config

import nl.teqplay.aisengine.YamlPropertyLoaderFactory
import nl.teqplay.aisengine.bucketing.properties.EventByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.EventByShipArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformTeqplayEventByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformTeqplayEventByShipArchiveProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.PropertySource

@Configuration
@EnableConfigurationProperties(
    EventByShipArchiveProperties::class,
    EventByAreaArchiveProperties::class,
    PlatformTeqplayEventByShipArchiveProperties::class,
    PlatformTeqplayEventByAreaArchiveProperties::class
)
@PropertySource("classpath:application-lib-bucketing-event.yml", factory = YamlPropertyLoaderFactory::class)
class BucketingEventAutoConfiguration
