package nl.teqplay.aisengine.bucketing.storage.event.area.bucket

import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifier
import nl.teqplay.aisengine.bucketing.storage.bucket.shared.BucketIdByArea
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.skeleton.model.Location

object EventBucketIdentifierByArea :
    BucketIdentifier<LocationBasedEvent, Location, AreaBucketId<Location>> {
    override val id = BucketIdByArea
    override val key = EventBucketKeyByArea
    override val archive = BucketIdByArea
}
