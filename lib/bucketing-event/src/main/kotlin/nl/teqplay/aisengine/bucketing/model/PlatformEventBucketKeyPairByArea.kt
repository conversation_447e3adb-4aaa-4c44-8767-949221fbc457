package nl.teqplay.aisengine.bucketing.model

import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.model.Location

/**
 * Bucket key pair containing a [location] and a [category].
 * This bucket key is eventually used to construct the database id of the bucket.
 */
data class PlatformEventBucketKeyPairByArea(
    val location: Location,
    val category: TeqplayEvent.EventCategory
)
