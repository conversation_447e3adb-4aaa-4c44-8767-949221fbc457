package nl.teqplay.aisengine.bucketing.storage.event.area.bucket

import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByArea
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifier
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent

object PlatformTeqplayEventBucketIdentifierByArea :
    BucketIdentifier<TeqplayLocationBasedEvent, PlatformEventBucketKeyPairByArea, EventAreaBucketId<PlatformEventBucketKeyPairByArea>> {
    override val id = PlatformEventBucketIdByArea
    override val key = PlatformTeqplayEventBucketKeyByArea
    override val archive = PlatformEventBucketIdByArea
}
