package nl.teqplay.aisengine.bucketing.util

import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByArea
import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByMmsi
import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.skeleton.model.Location
import java.time.Instant

/**
 * Extension method used to create a bucket key from a [TeqplayEvent]
 *
 * @return A bucket key contain ship mmsi and category
 */
fun TeqplayEvent.createBucketKey() = PlatformEventBucketKeyPairByMmsi(
    mmsi = shipMmsi.toIntOrNull() ?: 0,
    category = category
)

/**
 * Extension method used to create a bucket key from a [TeqplayLocationBasedEvent].
 *
 * @return An instance of [PlatformEventBucketKeyPairByArea] containing the [Location] and AisEngine [TeqplayEvent.EventCategory]
 */
fun TeqplayLocationBasedEvent.createBucketKey() = PlatformEventBucketKeyPairByArea(
    location = Location(location.latitude, location.longitude),
    category = category
)

/**
 * @return The timestamp used for bucketing
 */
fun TeqplayEvent.getTimestamp(): Instant = Instant.ofEpochMilli(this.datetime)

/**
 * @return The actualTime if this is an [ActualEvent] otherwise createdTime of the [Event]
 */
fun Event.getTimestamp(): Instant {
    return when (this) {
        is ActualEvent -> this.actualTime
        else -> this.createdTime
    }
}
