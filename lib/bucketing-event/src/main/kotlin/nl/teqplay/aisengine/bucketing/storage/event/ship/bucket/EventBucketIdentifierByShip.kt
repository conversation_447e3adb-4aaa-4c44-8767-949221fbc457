package nl.teqplay.aisengine.bucketing.storage.event.ship.bucket

import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifier
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.util.floorToLocalDate

object EventBucketIdentifierByShip :
    BucketIdentifier<Event, String, BucketId<String>> {
    override val id = EventBucketIdByShip
    override val key = EventBucketKeyByShip
    override val archive = defaultArchive()

    fun getOtherBucketIdForItem(
        item: EncounterEvent,
        resolver: BucketItemResolver<in Event>
    ): String {
        val timestamp = resolver.toTimestamp(item)
        val key = key.getOtherBucketKey(item)

        val date = timestamp.floorToLocalDate()
        return id.getBucketId(date, key)
    }
}
