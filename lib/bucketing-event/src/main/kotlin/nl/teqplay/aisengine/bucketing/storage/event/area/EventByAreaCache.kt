package nl.teqplay.aisengine.bucketing.storage.event.area

import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.skeleton.model.Location

interface EventByAreaCache : BaseEventByAreaCache<LocationBasedEvent, Location, AreaBucketId<Location>> {
    override fun getLocationFromEvent(event: LocationBasedEvent): Location {
        return event.location
    }
}
