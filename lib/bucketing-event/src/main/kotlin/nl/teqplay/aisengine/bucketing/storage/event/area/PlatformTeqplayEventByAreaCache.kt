package nl.teqplay.aisengine.bucketing.storage.event.area

import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByArea
import nl.teqplay.aisengine.bucketing.storage.event.area.bucket.EventAreaBucketId
import nl.teqplay.aisengine.platform.toLocation
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.skeleton.model.Location

interface PlatformTeqplayEventByAreaCache :
    BaseEventByAreaCache<TeqplayLocationBasedEvent, PlatformEventBucketKeyPairByArea, EventAreaBucketId<PlatformEventBucketKeyPairByArea>> {
    override fun getLocationFromEvent(event: TeqplayLocationBasedEvent): Location {
        return event.location.toLocation()
    }
}
