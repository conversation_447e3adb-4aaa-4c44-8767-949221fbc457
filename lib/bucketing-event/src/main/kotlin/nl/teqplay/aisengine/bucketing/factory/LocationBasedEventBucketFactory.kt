package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.model.EventResolver
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.event.area.bucket.EventBucketIdentifierByArea
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.skeleton.model.Location

/**
 * A [BucketFactory] for handling [LocationBasedEvent]
 */
object LocationBasedEventBucketFactory : BucketFactory<LocationBasedEvent, Location, AreaBucketId<Location>> {

    override val dataClass = LocationBasedEvent::class.java

    override val resolver = EventResolver
    override val bucketIdentifier = EventBucketIdentifierByArea
    override val bucketFormatter = DefaultBucketFormatter<LocationBasedEvent>()
}
