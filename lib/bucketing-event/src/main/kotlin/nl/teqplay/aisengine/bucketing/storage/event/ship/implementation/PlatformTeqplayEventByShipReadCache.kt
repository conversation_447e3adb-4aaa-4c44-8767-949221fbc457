package nl.teqplay.aisengine.bucketing.storage.event.ship.implementation

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.factory.TeqplayEventBucketFactory
import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByMmsi
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformTeqplayEventByShipArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCache
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.event.ship.PlatformTeqplayEventByMmsiCache
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.model.TimeWindow

/**
 * Read-only cache for platform's event history by mmsi
 *
 * IMPORTANT: platform compatibility must only expose reading, no writing should be done
 * since that would corrupt platform's data.
 */
class PlatformTeqplayEventByShipReadCache(
    bucketCacheFactory: BucketCacheFactory,
    archiveStorageFactory: ArchiveStorageFactory,
    mongoDatabase: MongoDatabase,
    objectMapper: ObjectMapper,
    bucket: BucketProperties,
    archive: PlatformTeqplayEventByShipArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties,
    platformBucketProperties: PlatformBucketProperties
) : PlatformTeqplayEventByMmsiCache,
    BucketReadCache<TeqplayEvent, PlatformEventBucketKeyPairByMmsi, BucketId<PlatformEventBucketKeyPairByMmsi>>(
        bucketCacheFactory.bucketReadCacheFactory(
            collectionName = PlatformTeqplayEventByShipBucketDetails.collectionName,
            unorderedCollectionSuffix = PlatformTeqplayEventByShipBucketDetails.unorderedCollectionSuffix,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,

            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            factory = TeqplayEventBucketFactory,
            createArchiveReadStorage = archiveStorageFactory.archiveReadStoragePerDay()
        ),
        overwriteMaxAge = platformBucketProperties.maxAge
    ) {

    fun findHistory(
        window: TimeWindow,
        shipId: Int,
        sort: Boolean,
        extendToLongTerm: Boolean = true
    ): Sequence<TeqplayEvent> {
        val bucketIds = TeqplayEvent.EventCategory.values().map { category ->
            PlatformEventBucketKeyPairByMmsi(shipId, category)
        }

        return fetchHistoryInTimeWindow(
            window = window,
            extendToLongTerm = extendToLongTerm,
            sort = sort,
            keys = bucketIds.toSet()
        ).filter { it.isDeleted == null || it.isDeleted == false }
    }
}
