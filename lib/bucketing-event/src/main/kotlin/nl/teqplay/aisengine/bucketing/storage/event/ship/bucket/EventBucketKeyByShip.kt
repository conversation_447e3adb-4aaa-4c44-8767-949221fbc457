package nl.teqplay.aisengine.bucketing.storage.event.ship.bucket

import nl.teqplay.aisengine.bucketing.storage.bucket.BucketKey
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.Event

object EventBucketKeyByShip : Bucket<PERSON>ey<Event, String> {
    override fun getBucketKey(item: Event): String = item.ship.getMainIdentifier()

    fun getOtherBucketKey(item: EncounterEvent): String = item.otherShip.getMainIdentifier()
}
