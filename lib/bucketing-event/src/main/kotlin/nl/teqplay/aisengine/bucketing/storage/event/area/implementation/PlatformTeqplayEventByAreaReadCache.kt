package nl.teqplay.aisengine.bucketing.storage.event.area.implementation

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.factory.TeqplayLocationBasedEventBucketFactory
import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByArea
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformTeqplayEventByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCache
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.event.area.PlatformTeqplayEventByAreaCache
import nl.teqplay.aisengine.bucketing.storage.event.area.bucket.EventAreaBucketId
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent

/**
 * Read-only cache for platform's event history by area
 *
 * IMPORTANT: platform compatibility must only expose reading, no writing should be done
 * since that would corrupt platform's data.
 */
class PlatformTeqplayEventByAreaReadCache(
    bucketCacheFactory: BucketCacheFactory,
    archiveStorageFactory: ArchiveStorageFactory,
    mongoDatabase: MongoDatabase,
    objectMapper: ObjectMapper,
    bucket: BucketProperties,
    archive: PlatformTeqplayEventByAreaArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties,
    platformBucketProperties: PlatformBucketProperties
) : PlatformTeqplayEventByAreaCache,
    BucketReadCache<TeqplayLocationBasedEvent, PlatformEventBucketKeyPairByArea, EventAreaBucketId<PlatformEventBucketKeyPairByArea>>(
        bucketCacheFactory.bucketReadCacheFactory(
            collectionName = PlatformTeqplayEventByAreaBucketDetails.collectionName,
            unorderedCollectionSuffix = PlatformTeqplayEventByAreaBucketDetails.unorderedCollectionSuffix,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,

            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            factory = TeqplayLocationBasedEventBucketFactory,
            createArchiveReadStorage = archiveStorageFactory.archiveReadStoragePerDay()
        ),
        overwriteMaxAge = platformBucketProperties.maxAge
    )
