package nl.teqplay.aisengine.bucketing.storage.event.area.implementation

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.config.BucketingMetricRegistry
import nl.teqplay.aisengine.bucketing.factory.LocationBasedEventBucketFactory
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.EventByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketWriteCache
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.event.area.EventByAreaCache
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.skeleton.model.Location
import java.time.Duration
import java.util.concurrent.ScheduledExecutorService

/**
 * Write/read cache for event history by area
 */
class EventByAreaWriteCache(
    bucketCacheFactory: BucketCacheFactory,
    archiveStorageFactory: ArchiveStorageFactory,
    mongoDatabase: MongoDatabase,
    objectMapper: ObjectMapper,
    bucket: BucketProperties,
    archive: EventByAreaArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties,
    scheduledExecutorService: ScheduledExecutorService,
    flushInitialDelay: Duration,
    bucketingMetricRegistry: BucketingMetricRegistry,
) : EventByAreaCache,
    BucketWriteCache<LocationBasedEvent, Location, AreaBucketId<Location>>(
        bucketCacheFactory.bucketWriteCacheFactory(
            collectionName = EventByAreaBucketDetails.collectionName,
            unorderedCollectionSuffix = EventByAreaBucketDetails.unorderedCollectionSuffix,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,

            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            scheduledExecutorService = scheduledExecutorService,
            flushInitialDelay = flushInitialDelay,
            factory = LocationBasedEventBucketFactory,
            createArchiveWriteStorage = archiveStorageFactory.archiveWriteStoragePerMonth()
        ),
        bucketingMetricRegistry
    )
