package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByArea
import nl.teqplay.aisengine.bucketing.model.TeqplayEventResolver
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.bucketing.storage.event.area.bucket.EventAreaBucketId
import nl.teqplay.aisengine.bucketing.storage.event.area.bucket.PlatformTeqplayEventBucketIdentifierByArea
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent

/**
 * A [BucketFactory] for handling [TeqplayLocationBasedEvent]
 */
object TeqplayLocationBasedEventBucketFactory :
    BucketFactory<TeqplayLocationBasedEvent, PlatformEventBucketKeyPairByArea, EventAreaBucketId<PlatformEventBucketKeyPairByArea>> {

    override val dataClass = TeqplayLocationBasedEvent::class.java

    override val resolver = TeqplayEventResolver
    override val bucketIdentifier = PlatformTeqplayEventBucketIdentifierByArea
    override val bucketFormatter = DefaultBucketFormatter<TeqplayLocationBasedEvent>()
}
