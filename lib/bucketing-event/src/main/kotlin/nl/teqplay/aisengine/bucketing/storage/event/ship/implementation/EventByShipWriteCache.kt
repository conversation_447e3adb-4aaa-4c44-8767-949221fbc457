package nl.teqplay.aisengine.bucketing.storage.event.ship.implementation

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.config.BucketingMetricRegistry
import nl.teqplay.aisengine.bucketing.factory.EventBucketFactory
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.EventByShipArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketWriteCache
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.event.ship.EventByShipCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.bucket.EventBucketIdentifierByShip
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.Event
import java.time.Duration
import java.util.concurrent.ScheduledExecutorService

/**
 * Write/read cache for event history by mmsi
 */
class EventByShipWriteCache(
    bucketCacheFactory: BucketCacheFactory,
    archiveStorageFactory: ArchiveStorageFactory,
    mongoDatabase: MongoDatabase,
    objectMapper: ObjectMapper,
    bucket: BucketProperties,
    archive: EventByShipArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties,
    scheduledExecutorService: ScheduledExecutorService,
    flushInitialDelay: Duration,
    bucketingMetricRegistry: BucketingMetricRegistry,
) : EventByShipCache,
    BucketWriteCache<Event, String, BucketId<String>>(
        bucketCacheFactory.bucketWriteCacheFactory(
            collectionName = EventByShipBucketDetails.collectionName,
            unorderedCollectionSuffix = EventByShipBucketDetails.unorderedCollectionSuffix,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,

            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            scheduledExecutorService = scheduledExecutorService,
            flushInitialDelay = flushInitialDelay,
            factory = EventBucketFactory,
            createArchiveWriteStorage = archiveStorageFactory.archiveWriteStoragePerMonth()
        ),
        bucketingMetricRegistry
    ) {

    override fun insert(item: Event) {
        val bucketIdentifier = factory.bucketIdentifier as EventBucketIdentifierByShip
        val bucketId = bucketIdentifier.getBucketIdForItem(item, factory.resolver)

        // Encounter events should also be saved in the bucket of the "other" ship
        if (item is EncounterEvent) {
            val otherBucketId = bucketIdentifier.getOtherBucketIdForItem(item, factory.resolver)
            super.insert(bucketId, item)
            super.insert(otherBucketId, item)
        } else {
            super.insert(bucketId, item)
        }
    }
}
