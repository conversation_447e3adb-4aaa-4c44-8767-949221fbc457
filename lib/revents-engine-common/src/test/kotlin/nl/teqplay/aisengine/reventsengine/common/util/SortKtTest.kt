package nl.teqplay.aisengine.reventsengine.common.util

import nl.teqplay.aisengine.reventsengine.common.model.event.VesselVoyageEntryV2Wrapper
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant

class SortKtTest {

    @Test
    fun `VesselVoyage sort by start time`() {
        val visit = mockVisitWrapper(Instant.EPOCH.plusSeconds(1))
        val voyage = mockVoyageWrapper(Instant.EPOCH)
        val input = listOf(visit, voyage)
        assertEquals(listOf(voyage, visit), input.sort())
    }

    @Test
    fun `VesselVoyage sort also prioritizes 0-second voyages`() {
        val visit = mockVisitWrapper(Instant.EPOCH)
        val voyage = mockVoyageWrapper(Instant.EPOCH)
        val input = listOf(visit, voyage)
        assertEquals(listOf(voyage, visit), input.sort())
    }

    private fun mockVisitWrapper(time: Instant): VesselVoyageEntryV2Wrapper {
        val entry = mock<NewVisit>().apply {
            whenever(start).thenReturn(LocationTime(Location(0.0, 0.0), time))
        }
        return mockEntryWrapper(entry)
    }

    private fun mockVoyageWrapper(time: Instant): VesselVoyageEntryV2Wrapper {
        val entry = mock<NewVoyage>().apply {
            whenever(start).thenReturn(LocationTime(Location(0.0, 0.0), time))
        }
        return mockEntryWrapper(entry)
    }

    private fun mockEntryWrapper(entry: NewEntry): VesselVoyageEntryV2Wrapper {
        return mock<VesselVoyageEntryV2Wrapper>().apply {
            whenever(this.entry).thenReturn(entry)
        }
    }
}
