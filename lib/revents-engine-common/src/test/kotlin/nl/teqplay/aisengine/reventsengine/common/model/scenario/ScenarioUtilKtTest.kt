package nl.teqplay.aisengine.reventsengine.common.model.scenario

import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse.Status.ScenarioDuration
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

class ScenarioUtilKtTest {

    @Test
    fun `aggregateScenarioDurations - empty list should return null`() {
        assertNull(aggregateScenarioDurations(emptyList()))
    }

    @Test
    fun `aggregateScenarioDurations - one entry should stay the same`() {
        val input = ScenarioDuration(
            time = Instant.EPOCH,
            spent = Duration.ofHours(1),
            estimate = Duration.ofMinutes(30)
        )
        assertEquals(input, aggregateScenarioDurations(listOf(input)))
    }

    @Test
    fun `aggregateScenarioDurations - two overlapping entries should be merged`() {
        val input1 = ScenarioDuration(
            time = Instant.EPOCH,
            spent = Duration.ofHours(1),
            estimate = Duration.ofMinutes(30)
        )
        val input2 = ScenarioDuration(
            time = Instant.EPOCH.plus(30, ChronoUnit.MINUTES),
            spent = Duration.ofMinutes(30),
            estimate = Duration.ofMinutes(15)
        )
        val output = ScenarioDuration(
            time = Instant.EPOCH,
            spent = Duration.ofHours(1),
            estimate = Duration.ofMinutes(30)
        )
        assertEquals(output, aggregateScenarioDurations(listOf(input1, input2)))
    }

    @Test
    fun `aggregateScenarioDurations - two sequential entries should be added together`() {
        val input1 = ScenarioDuration(
            time = Instant.EPOCH,
            spent = Duration.ofHours(1),
            estimate = null
        )
        val input2 = ScenarioDuration(
            time = Instant.EPOCH.plus(1, ChronoUnit.HOURS),
            spent = Duration.ofHours(1),
            estimate = Duration.ofMinutes(15)
        )
        val output = ScenarioDuration(
            time = Instant.EPOCH,
            spent = Duration.ofHours(2),
            estimate = Duration.ofMinutes(15)
        )
        assertEquals(output, aggregateScenarioDurations(listOf(input1, input2)))
    }

    @Test
    fun `aggregateScenarioDurations - two sequential entries with a gap in between should be added together`() {
        val input1 = ScenarioDuration(
            time = Instant.EPOCH,
            spent = Duration.ofHours(1),
            estimate = null
        )
        val input2 = ScenarioDuration(
            time = Instant.EPOCH.plus(2, ChronoUnit.HOURS),
            spent = Duration.ofHours(1),
            estimate = Duration.ofMinutes(15)
        )
        val output = ScenarioDuration(
            time = Instant.EPOCH,
            // there was one hour of waiting time in between, counting that as well
            spent = Duration.ofHours(3),
            estimate = Duration.ofMinutes(15)
        )
        assertEquals(output, aggregateScenarioDurations(listOf(input1, input2)))
    }
}
