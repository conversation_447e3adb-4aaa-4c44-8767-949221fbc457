package nl.teqplay.aisengine.reventsengine.common.service.event

import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipStatus
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.reventsengine.common.datasource.ActualEventsBaseDataSource
import nl.teqplay.aisengine.reventsengine.common.model.event.ActualEventWrapper
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestRelevantShip
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosInterestsBaseService
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioRequestEvent
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Instant
import java.time.temporal.ChronoUnit

class ActualEventsBaseServiceTest {

    private val actualEventsDataSource = mock<ActualEventsBaseDataSource>()
    private val scenariosInterestsService = mock<ScenariosInterestsBaseService>()
    private val actualEventsService = object : ActualEventsBaseService(
        actualEventsDataSource,
        scenariosInterestsService,
    ) {}

    private val scenario = "scenario"
    private val mmsi = 0
    private val window = TimeWindow(
        from = Instant.EPOCH.plusSeconds(1),
        to = Instant.EPOCH.plus(3, ChronoUnit.DAYS).minusSeconds(1)
    )
    private val types = setOf("type")
    private val event = AisStatusChangedEvent(
        _id = "id",
        ship = AisShipIdentifier(mmsi),
        location = Location(0.0, 0.0),
        oldValue = null,
        newValue = ShipStatus.AT_ANCHOR,
        actualTime = Instant.EPOCH.plusSeconds(1)
    )
    private val eventWrappers = listOf(
        ActualEventWrapper(
            event = event
        ),
        ActualEventWrapper(
            event = AisStatusChangedEvent(
                _id = "id",
                ship = AisShipIdentifier(mmsi),
                location = Location(0.0, 0.0),
                oldValue = null,
                newValue = ShipStatus.AT_ANCHOR,
                actualTime = Instant.EPOCH.plus(2, ChronoUnit.DAYS)
            )
        ),
    )

    private val interests = listOf(
        ScenarioInterestRelevantShip(
            mmsi = mmsi,
            imo = null,
            window = TimeWindow(
                from = Instant.EPOCH,
                to = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
            ),
            scenario = scenario
        ),
        ScenarioInterestRelevantShip(
            mmsi = mmsi,
            imo = null,
            window = TimeWindow(
                from = Instant.EPOCH.plus(1, ChronoUnit.DAYS),
                to = Instant.EPOCH.plus(2, ChronoUnit.DAYS)
            ),
            scenario = scenario
        ),
        ScenarioInterestRelevantShip(
            mmsi = mmsi,
            imo = null,
            window = TimeWindow(
                from = Instant.EPOCH.plus(2, ChronoUnit.DAYS),
                to = Instant.EPOCH.plus(3, ChronoUnit.DAYS)
            ),
            parent = "other",
            scenarios = setOf("other", scenario)
        )
    )

    private val requests = listOf(
        ScenarioRequestEvent(
            mmsi = mmsi,
            scenario = scenario,
            window = TimeWindow(
                from = Instant.EPOCH.plusSeconds(1),
                to = Instant.EPOCH.plus(2, ChronoUnit.DAYS)
            )
        ),
        ScenarioRequestEvent(
            mmsi = mmsi,
            scenario = "other",
            window = TimeWindow(
                from = Instant.EPOCH.plus(2, ChronoUnit.DAYS),
                to = Instant.EPOCH.plus(3, ChronoUnit.DAYS).minusSeconds(1)
            )
        )
    )

    @Test
    fun fetchByMmsi() {
        whenever(scenariosInterestsService.getInterestsByMmsi(any(), any(), any())).thenReturn(interests)
        whenever(actualEventsDataSource.fetch(any(), any())).thenReturn(eventWrappers)

        val events = actualEventsService.fetchEventsByMmsi(scenario, mmsi, window, types)
        assertEquals(eventWrappers.map { it.event }, events)

        verify(actualEventsDataSource).fetch(eq(requests), any())
    }

    @Test
    fun fetchByImo() {
        whenever(scenariosInterestsService.getInterestsByImo(any(), any(), any())).thenReturn(interests)
        whenever(actualEventsDataSource.fetch(any(), any())).thenReturn(eventWrappers)

        val events = actualEventsService.fetchEventsByImo(scenario, mmsi, window, types)
        assertEquals(eventWrappers.map { it.event }, events)

        verify(actualEventsDataSource).fetch(eq(requests), any())
    }

    @Test
    fun `fetchScenarioRequestsByImo - test correct merging of MMSI changes`() {
        val window = TimeWindow(
            from = Instant.EPOCH.plusSeconds(1),
            to = Instant.EPOCH.plus(3, ChronoUnit.DAYS).minusSeconds(1)
        )

        val windowPoint1 = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
        val windowPoint2 = Instant.EPOCH.plus(2, ChronoUnit.DAYS)

        whenever(scenariosInterestsService.getInterestsByImo(any(), any(), any())).thenReturn(
            listOf(
                // first MMSI exists two times, these should be merged
                ScenarioInterestRelevantShip(
                    mmsi = 0,
                    imo = null,
                    window = TimeWindow(
                        from = window.from,
                        to = windowPoint1
                    ),
                    scenario = scenario
                ),
                ScenarioInterestRelevantShip(
                    mmsi = 0,
                    imo = null,
                    window = TimeWindow(
                        from = windowPoint1,
                        to = windowPoint2
                    ),
                    scenario = scenario
                ),
                // second MMSI exists only one time, shouldn't be merged
                ScenarioInterestRelevantShip(
                    mmsi = 1,
                    imo = null,
                    window = TimeWindow(
                        from = windowPoint2,
                        to = window.to
                    ),
                    scenario = scenario
                )
            )
        )
        whenever(actualEventsDataSource.fetch(any(), any())).thenReturn(eventWrappers)

        val imo = 0
        val requests = actualEventsService.fetchScenarioRequestsByImo(scenario, imo, window)
        assertEquals(
            listOf(
                // should contain the first MMSI, merging should be applied here
                ScenarioRequestEvent(
                    mmsi = 0,
                    scenario = scenario,
                    window = TimeWindow(
                        from = window.from,
                        to = windowPoint2
                    )
                ),
                // should then continue into the next MMSI, without merging with the previous
                ScenarioRequestEvent(
                    mmsi = 1,
                    scenario = scenario,
                    window = TimeWindow(
                        from = windowPoint2,
                        to = window.to
                    )
                )
            ),
            requests
        )
    }

    @Test
    fun `fetchScenarioRequestsByImo - test correct merging of non-ordered MMSI changes`() {
        val window = TimeWindow(
            from = Instant.EPOCH.plusSeconds(1),
            to = Instant.EPOCH.plus(3, ChronoUnit.DAYS).minusSeconds(1)
        )

        val windowPoint1 = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
        val windowPoint2 = Instant.EPOCH.plus(2, ChronoUnit.DAYS)

        whenever(scenariosInterestsService.getInterestsByImo(any(), any(), any())).thenReturn(
            listOf(
                // first MMSI exists two times, these should be merged
                ScenarioInterestRelevantShip(
                    mmsi = 0,
                    imo = null,
                    window = TimeWindow(
                        from = window.from,
                        to = windowPoint1
                    ),
                    scenario = scenario
                ),
                // second MMSI exists only one time, shouldn't be merged
                ScenarioInterestRelevantShip(
                    mmsi = 1,
                    imo = null,
                    window = TimeWindow(
                        from = windowPoint2,
                        to = window.to
                    ),
                    scenario = scenario
                ),
                // first MMSI again, but not ordered
                ScenarioInterestRelevantShip(
                    mmsi = 0,
                    imo = null,
                    window = TimeWindow(
                        from = windowPoint1,
                        to = windowPoint2
                    ),
                    scenario = scenario
                )
            )
        )
        whenever(actualEventsDataSource.fetch(any(), any())).thenReturn(eventWrappers)

        val imo = 0
        val requests = actualEventsService.fetchScenarioRequestsByImo(scenario, imo, window)
        assertEquals(
            listOf(
                // should contain the first MMSI, merging should be applied here
                ScenarioRequestEvent(
                    mmsi = 0,
                    scenario = scenario,
                    window = TimeWindow(
                        from = window.from,
                        to = windowPoint2
                    )
                ),
                // should then continue into the next MMSI, without merging with the previous
                ScenarioRequestEvent(
                    mmsi = 1,
                    scenario = scenario,
                    window = TimeWindow(
                        from = windowPoint2,
                        to = window.to
                    )
                )
            ),
            requests
        )
    }

    @Test
    fun `fetchScenarioRequestsByImo - test correct merging of non-ordered AND overlapping MMSI changes`() {
        val window = TimeWindow(
            from = Instant.EPOCH.plusSeconds(1),
            to = Instant.EPOCH.plus(3, ChronoUnit.DAYS).minusSeconds(1)
        )

        val windowPoint1 = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
        val windowPoint2 = Instant.EPOCH.plus(2, ChronoUnit.DAYS)

        whenever(scenariosInterestsService.getInterestsByImo(any(), any(), any())).thenReturn(
            listOf(
                // first MMSI exists two times, these should be merged
                ScenarioInterestRelevantShip(
                    mmsi = 0,
                    imo = null,
                    window = TimeWindow(
                        from = window.from,
                        to = windowPoint1
                    ),
                    scenario = scenario
                ),
                // second MMSI exists only one time, shouldn't be merged
                ScenarioInterestRelevantShip(
                    mmsi = 1,
                    imo = null,
                    window = TimeWindow(
                        from = windowPoint1, // <-- importantly becomes active earlier
                        to = window.to
                    ),
                    scenario = scenario
                ),
                // first MMSI again, but not ordered
                ScenarioInterestRelevantShip(
                    mmsi = 0,
                    imo = null,
                    window = TimeWindow(
                        from = windowPoint1,
                        to = windowPoint2
                    ),
                    scenario = scenario
                )
            )
        )
        whenever(actualEventsDataSource.fetch(any(), any())).thenReturn(eventWrappers)

        val imo = 0
        val requests = actualEventsService.fetchScenarioRequestsByImo(scenario, imo, window)
        assertEquals(
            listOf(
                // should contain the first MMSI, merging should be applied here
                ScenarioRequestEvent(
                    mmsi = 0,
                    scenario = scenario,
                    window = TimeWindow(
                        from = window.from,
                        to = windowPoint2
                    )
                ),
                // should then continue into the next MMSI, without merging with the previous
                ScenarioRequestEvent(
                    mmsi = 1,
                    scenario = scenario,
                    window = TimeWindow(
                        from = windowPoint1,
                        to = window.to
                    )
                )
            ),
            requests
        )
    }

    @Test
    fun `fetchScenarioRequestsByImo - test correcting of all request windows`() {
        val window = TimeWindow(
            from = Instant.EPOCH,
            to = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
        )

        val largerWindow = TimeWindow(window.from.minusSeconds(1), window.to.plusSeconds(1))

        whenever(scenariosInterestsService.getInterestsByImo(any(), any(), any())).thenReturn(
            listOf(
                ScenarioInterestRelevantShip(
                    mmsi = 0,
                    imo = null,
                    window = largerWindow,
                    scenario = scenario
                ),
                ScenarioInterestRelevantShip(
                    mmsi = 1,
                    imo = null,
                    window = largerWindow,
                    scenario = scenario
                )
            )
        )
        whenever(actualEventsDataSource.fetch(any(), any())).thenReturn(eventWrappers)

        val imo = 0
        val requests = actualEventsService.fetchScenarioRequestsByImo(scenario, imo, window)
        assertEquals(
            listOf(
                ScenarioRequestEvent(
                    mmsi = 0,
                    scenario = scenario,
                    window = window
                ),
                ScenarioRequestEvent(
                    mmsi = 1,
                    scenario = scenario,
                    window = window
                )
            ),
            requests
        )
    }
}
