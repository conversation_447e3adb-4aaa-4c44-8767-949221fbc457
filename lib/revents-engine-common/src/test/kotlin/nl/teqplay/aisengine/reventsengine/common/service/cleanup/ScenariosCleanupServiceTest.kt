package nl.teqplay.aisengine.reventsengine.common.service.cleanup

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CRASHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FINISHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_ASSIGN_CONTEXT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_JOB_CREATE
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_QUEUED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_INIT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_TEARDOWN
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_JOB
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_STREAMING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_UNASSIGN_CONTEXT
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.REVENTS_API_RESTARTED
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import java.time.Instant

class ScenariosCleanupServiceTest {

    private val scenariosDataSource = mock<ScenariosDataSource>()
    private val scenariosCleanupTaskService = ScenariosCleanupTaskService(scenariosDataSource)

    private val scenarioCreateRequest = ScenarioCreateRequest(
        window = TimeWindow(Instant.EPOCH, Instant.EPOCH),
        interests = emptyList()
    )
    private val scenario = ScenarioState(scenarioCreateRequest).copy(phase = PROGRESSING)

    @Test
    fun `getTeardownScenario - if node is not yet assigned should jump straight to finished`() {
        val output = scenariosCleanupTaskService.createTeardownScenario(scenario.copy(phase = INITIALIZING_ASSIGN_CONTEXT))
        assertTrue(output.id.startsWith("TEARDOWN_"))
        assertEquals(FINISHED, output.phase)
    }

    @Test
    fun `getTeardownScenario - if node is assigned should jump to end of progressing`() {
        val output = scenariosCleanupTaskService.createTeardownScenario(scenario.copy(phase = INITIALIZING_JOB_CREATE))
        assertTrue(output.id.startsWith("TEARDOWN_"))
        assertEquals(PROGRESSING_END, output.phase)
    }

    @Test
    fun `getTeardownScenario - during progressing should jump to end of progressing`() {
        val output = scenariosCleanupTaskService.createTeardownScenario(scenario)
        assertTrue(output.id.startsWith("TEARDOWN_"))
        assertEquals(PROGRESSING_END, output.phase)
    }

    @Test
    fun `getTeardownScenario - during post-processing should jump to end of post-processing`() {
        val output = scenariosCleanupTaskService.createTeardownScenario(scenario.copy(phase = POST_PROCESSING_INIT))
        assertTrue(output.id.startsWith("TEARDOWN_"))
        assertEquals(POST_PROCESSING_TEARDOWN, output.phase)
    }

    @Test
    fun `getTeardownScenario - initializing doesn't require teardown should jump straight to finished`() {
        val output = scenariosCleanupTaskService.createTeardownScenario(scenario.copy(phase = INITIALIZING_QUEUED))
        assertTrue(output.id.startsWith("TEARDOWN_"))
        assertEquals(FINISHED, output.phase)
    }

    @Test
    fun `getTeardownScenario - teardown steps should not be skipped and stay the same`() {
        val teardownPhases = listOf(
            TEARDOWN_STREAMING,
            TEARDOWN_JOB,
            TEARDOWN_UNASSIGN_CONTEXT,
        )
        teardownPhases.forEach { teardownPhase ->
            val output = scenariosCleanupTaskService.createTeardownScenario(scenario.copy(phase = teardownPhase))
            assertTrue(output.id.startsWith("TEARDOWN_"))
            assertEquals(teardownPhase, output.phase)
        }
    }

    @Test
    fun saveTeardownScenario() {
        val output = scenariosCleanupTaskService.createAndSaveTeardownScenario(scenario)
        assertTrue(output.id.startsWith("TEARDOWN_"))
        assertEquals(PROGRESSING_END, output.phase)
        verify(scenariosDataSource, times(1)).save(eq(output))
    }

    @Test
    fun setToCrashedAfterRestart() {
        val match = scenario.copy(phase = CRASHED, crashed = REVENTS_API_RESTARTED)
        scenariosCleanupTaskService.setToCrashedAfterRestart(scenario)
        verify(scenariosDataSource, times(1)).save(eq(match))
    }
}
