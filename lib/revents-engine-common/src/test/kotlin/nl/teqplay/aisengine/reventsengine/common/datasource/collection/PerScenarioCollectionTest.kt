package nl.teqplay.aisengine.reventsengine.common.datasource.collection

import com.mongodb.kotlin.client.MongoCollection
import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.skeleton.common.exception.BadRequestException
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class PerScenarioCollectionTest {

    @Test
    fun `getCollection - pruned scenario should not allow collection creation`() {
        val scenariosDataSource = mock<ScenariosDataSource>()
        val perScenarioCollection = getPerScenarioCollection(scenariosDataSource, allowCreate = false)

        val state = mock<ScenarioState>().apply {
            whenever(phase).thenReturn(ScenarioState.InternalPhase.PRUNED)
        }
        whenever(scenariosDataSource.get(any())).thenReturn(state)

        assertThrows<BadRequestException> {
            perScenarioCollection.testGetCollection("")
        }
    }

    @Test
    fun `getCollection - non-pruned scenario should allow collection creation`() {
        val scenariosDataSource = mock<ScenariosDataSource>()
        val perScenarioCollection = getPerScenarioCollection(scenariosDataSource, allowCreate = true)
        whenever(scenariosDataSource.get(any())).thenReturn(null)

        val collection = perScenarioCollection.testGetCollection("")
        assertNotNull(collection)
    }

    private fun getPerScenarioCollection(
        scenariosDataSource: ScenariosDataSource,
        allowCreate: Boolean,
    ) = object : PerScenarioCollection<String>(scenariosDataSource) {
        override fun createCollection(scenario: String): MongoCollection<String> {
            if (allowCreate) return mock()
            TODO("Not yet implemented")
        }

        fun testGetCollection(scenario: String) = getCollection(scenario)
    }
}
