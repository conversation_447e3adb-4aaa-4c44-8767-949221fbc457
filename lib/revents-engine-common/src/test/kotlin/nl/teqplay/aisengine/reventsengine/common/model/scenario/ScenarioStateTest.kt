package nl.teqplay.aisengine.reventsengine.common.model.scenario

import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunProgress
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class ScenarioStateTest {

    private val id = "id"
    private val childId = "childId"
    private val window = TimeWindow(
        from = Instant.EPOCH,
        to = Instant.EPOCH.plus(10, ChronoUnit.DAYS)
    )
    private val cursor = Instant.EPOCH.plus(2, ChronoUnit.DAYS)

    @Test
    fun `simple scenario state returns simple status`() {
        val scenarioState = ScenarioState(
            id = id,
            phase = ScenarioState.InternalPhase.PROGRESSING,
            crashed = null,
            queued = Instant.EPOCH,
            pruned = null,
            cancelled = null,
            status = ScenarioState.InternalStatus(
                start = Instant.EPOCH,
                initialized = Instant.EPOCH,
                progress = InterestsRunProgress(
                    id = id,
                    window = window,
                    cursor = cursor
                )
            ),
            window = window,
            windowMargin = Scenario.WindowMargin(),
            interests = emptyList(),
            settings = null,
            teardown = false,
            parent = null,
            inherit = null,
            metaData = null
        )

        val childScenarios = emptyList<ScenarioState>()
        val response = scenarioState.toResponse(childScenarios, emptyList())

        assertEquals(ScenarioResponse.Phase.PROGRESSING, response.phase)
        assertEquals(20, response.status?.percentage)
        assertEquals(cursor, response.status?.progressingCursor)
    }

    @Test
    fun `forked scenario including children returns aggregated status`() {
        val scenarioState = ScenarioState(
            id = id,
            phase = ScenarioState.InternalPhase.FORKED,
            crashed = null,
            queued = Instant.EPOCH,
            pruned = null,
            cancelled = null,
            status = null,
            window = window,
            windowMargin = Scenario.WindowMargin(),
            interests = emptyList(),
            settings = null,
            teardown = false,
            parent = null,
            inherit = null,
            metaData = null
        )

        val childScenarios = listOf(
            ScenarioState(
                id = childId,
                phase = ScenarioState.InternalPhase.FINISHED,
                crashed = null,
                queued = Instant.EPOCH,
                pruned = null,
                cancelled = null,
                status = ScenarioState.InternalStatus(
                    start = Instant.EPOCH,
                    initialized = Instant.EPOCH,
                    progressed = Instant.EPOCH.plus(1, ChronoUnit.HOURS),
                    progress = InterestsRunProgress(
                        id = childId,
                        window = window,
                        cursor = window.to,
                        done = true
                    )
                ),
                window = window,
                windowMargin = Scenario.WindowMargin(),
                events = emptySet(),
                postProcessing = emptySet(),
                interests = emptyList(),
                settings = null,
                teardown = false,
                parent = id,
                inherit = null,
                metaData = null
            ),
            ScenarioState(
                id = childId,
                phase = ScenarioState.InternalPhase.PROGRESSING,
                crashed = null,
                queued = Instant.EPOCH,
                pruned = null,
                cancelled = null,
                status = ScenarioState.InternalStatus(
                    start = Instant.EPOCH.plus(1, ChronoUnit.HOURS),
                    initialized = Instant.EPOCH.plus(1, ChronoUnit.HOURS),
                    progress = InterestsRunProgress(
                        id = childId,
                        window = window,
                        cursor = cursor
                    )
                ),
                window = window,
                windowMargin = Scenario.WindowMargin(),
                events = emptySet(),
                postProcessing = emptySet(),
                interests = emptyList(),
                settings = null,
                teardown = false,
                parent = id,
                inherit = null,
                metaData = null
            ),
            ScenarioState(
                id = childId,
                phase = ScenarioState.InternalPhase.QUEUED,
                crashed = null,
                queued = Instant.EPOCH,
                pruned = null,
                cancelled = null,
                status = null,
                window = window,
                windowMargin = Scenario.WindowMargin(),
                events = emptySet(),
                postProcessing = emptySet(),
                interests = emptyList(),
                settings = null,
                teardown = false,
                parent = id,
                inherit = null,
                metaData = null
            )
        )

        val response = scenarioState.toResponse(childScenarios, emptyList())

        assertEquals(ScenarioResponse.Phase.PROGRESSING, response.phase)
        assertEquals(40, response.status?.percentage)
        // forked scenario shouldn't have a progressing cursor, the child processes should
        assertNull(response.status?.progressingCursor)

        assertEquals(2, response.status?.children?.size)

        val firstChildStatus = response.status?.children?.get(0)
        val secondChildStatus = response.status?.children?.get(1)
        assertEquals(100, firstChildStatus?.percentage)
        assertEquals(20, secondChildStatus?.percentage)
        assertEquals(cursor, secondChildStatus?.progressingCursor)
    }

    @Test
    fun `scenario state that got inherited returns inheritors`() {
        val scenarioState = ScenarioState(
            id = id,
            phase = ScenarioState.InternalPhase.PROGRESSING,
            crashed = null,
            queued = Instant.EPOCH,
            pruned = null,
            cancelled = null,
            status = ScenarioState.InternalStatus(
                start = Instant.EPOCH,
                initialized = Instant.EPOCH,
                progress = InterestsRunProgress(
                    id = id,
                    window = window,
                    cursor = cursor
                )
            ),
            window = window,
            windowMargin = Scenario.WindowMargin(),
            interests = emptyList(),
            settings = null,
            teardown = false,
            parent = null,
            inherit = null,
            metaData = null
        )

        val inheritedScenarios = listOf(scenarioState.copy(id = "inheritor", inherit = scenarioState.id))
        val response = scenarioState.toResponse(emptyList(), inheritedScenarios)

        assertEquals(ScenarioResponse.Phase.PROGRESSING, response.phase)
        assertEquals(20, response.status?.percentage)
        assertEquals(cursor, response.status?.progressingCursor)
    }
}
