package nl.teqplay.aisengine.reventsengine.common.service.scenario

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosMetadataDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioMetadata
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class ScenariosMetadataServiceTest {

    private val scenariosMetadataDataSource = mock<ScenariosMetadataDataSource>()
    private val scenariosMetadataService = ScenariosMetadataService(scenariosMetadataDataSource)

    private val metadata = ScenarioMetadata(
        id = "",
        unlocodes = emptySet(),
        areas = emptyList()
    )

    @Test
    fun getMetadataByScenario() {
        whenever(scenariosMetadataDataSource.getMetadataByScenario(any()))
            .thenReturn(null)
            .thenReturn(metadata)

        assertNull(scenariosMetadataService.getMetadataByScenario(""))
        assertEquals(metadata, scenariosMetadataService.getMetadataByScenario(""))
    }

    @Test
    fun save() {
        doNothing().whenever(scenariosMetadataDataSource).save(any())
        scenariosMetadataService.save(metadata)
        verify(scenariosMetadataDataSource, times(1)).save(any())
    }
}
