package nl.teqplay.aisengine.reventsengine.common.service.scenario

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosInterestsDataSource
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestRelevantShip
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ScenariosBaseInterestsServiceTest {

    private val scenariosInterestsDataSource = mock<ScenariosInterestsDataSource>()
    private val scenariosInterestsService = object : ScenariosInterestsBaseService(
        scenariosInterestsDataSource
    ) {}

    private val mmsi0 = 0
    private val mmsi1 = 1
    private val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
    private val scenario0 = "scenario0"
    private val scenario1 = "scenario1"

    data class GetInterestsByScenarioTestData(
        val message: String,
        val input: List<ScenarioInterestRelevantShip>,
        val output: List<ScenarioInterestRelevantShip>
    )

    @ParameterizedTest
    @MethodSource("getInterestsByScenarioTestData")
    fun getInterestsByScenario(data: GetInterestsByScenarioTestData) {
        whenever(scenariosInterestsDataSource.getInterestsByScenario(any())).thenReturn(data.input)

        // acting as if we want only one scenario, but in this test we actually return data
        // from multiple scenarios for coverage
        val scenario = scenario0
        assertEquals(data.output, scenariosInterestsService.getInterestsByScenario(scenario), data.message)
    }

    @ParameterizedTest
    @MethodSource("getUniqueInterestsByScenarioTestData")
    fun getUniqueInterestsByScenario(data: GetInterestsByScenarioTestData) {
        whenever(scenariosInterestsDataSource.getInterestsByScenario(any())).thenReturn(data.input)

        assertEquals(data.output, scenariosInterestsService.getUniqueInterestsByScenario(scenario0), data.message)
    }

    @Test
    fun bulkWrite() {
        doNothing().whenever(scenariosInterestsDataSource).bulkWrite(any())

        scenariosInterestsService.save(emptyList())

        verify(scenariosInterestsDataSource, times(1)).bulkWrite(any())
    }

    private fun getInterestsByScenarioTestData(): Stream<GetInterestsByScenarioTestData> = Stream.of(
        GetInterestsByScenarioTestData(
            message = "empty returns",
            input = emptyList(),
            output = emptyList()
        ),
        GetInterestsByScenarioTestData(
            message = "no simplification needed",
            input = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = window,
                    scenario = scenario0
                )
            ),
            output = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = window,
                    scenario = scenario0
                )
            )
        ),
        GetInterestsByScenarioTestData(
            message = "simplify by combining",
            input = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(4)),
                    scenario = scenario1
                )
            ),
            output = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = window,
                    parent = scenario0,
                    scenarios = setOf(scenario0, scenario1)
                )
            )
        ),
        GetInterestsByScenarioTestData(
            message = "don't simplify by combining if the IMO doesn't match",
            input = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = 0,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = 1,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(4)),
                    scenario = scenario1
                )
            ),
            output = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = 0,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = 1,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(4)),
                    scenario = scenario1
                )
            )
        ),
        GetInterestsByScenarioTestData(
            message = "simplify by combining, unordered",
            input = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(4)),
                    scenario = scenario1
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                )
            ),
            output = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = window,
                    parent = scenario0,
                    scenarios = setOf(scenario0, scenario1)
                )
            )
        ),
        GetInterestsByScenarioTestData(
            message = "simplify multiple ships",
            input = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi1,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(1, ChronoUnit.DAYS), Duration.ofDays(2)),
                    scenario = scenario1
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi1,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(2)),
                    scenario = scenario1
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(4)),
                    scenario = scenario1
                ),
            ),
            output = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = window,
                    parent = scenario0,
                    scenarios = setOf(scenario0, scenario1)
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi1,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(1, ChronoUnit.DAYS), Duration.ofDays(4)),
                    scenario = scenario1
                )
            )
        ),
    )

    private fun getUniqueInterestsByScenarioTestData(): Stream<GetInterestsByScenarioTestData> = Stream.of(
        GetInterestsByScenarioTestData(
            message = "empty returns",
            input = emptyList(),
            output = emptyList()
        ),
        GetInterestsByScenarioTestData(
            message = "no simplification needed",
            input = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = window,
                    scenario = scenario0
                )
            ),
            output = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = window,
                    scenario = scenario0
                )
            )
        ),
        GetInterestsByScenarioTestData(
            message = "simplify by combining, only returning unique part",
            input = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(4)),
                    parent = scenario1,
                    scenarios = setOf(scenario0)
                )
            ),
            output = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                )
            )
        ),
        GetInterestsByScenarioTestData(
            message = "simplify by combining, unordered, only returning unique part",
            input = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(4)),
                    parent = scenario1,
                    scenarios = setOf(scenario0)
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                )
            ),
            output = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                )
            )
        ),
        GetInterestsByScenarioTestData(
            message = "simplify multiple ships, only returning unique part for one mmsi",
            input = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi1,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(1, ChronoUnit.DAYS), Duration.ofDays(2)),
                    parent = scenario1,
                    scenarios = setOf(scenario0)
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi1,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(2)),
                    parent = scenario1,
                    scenarios = setOf(scenario0)
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(4)),
                    parent = scenario1,
                    scenarios = setOf(scenario0)
                ),
            ),
            output = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                ),
            )
        ),
        GetInterestsByScenarioTestData(
            message = "simplify by combining, only returning unique part, leaving the gap",
            input = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
                    scenario = scenario0
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(1, ChronoUnit.DAYS), Duration.ofDays(2)),
                    scenario = scenario0
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(3, ChronoUnit.DAYS), Duration.ofDays(4)),
                    parent = scenario1,
                    scenarios = setOf(scenario0)
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(4, ChronoUnit.DAYS), window.to),
                    scenario = scenario0
                ),
            ),
            output = listOf(
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                    scenario = scenario0
                ),
                ScenarioInterestRelevantShip(
                    mmsi = mmsi0,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(4, ChronoUnit.DAYS), window.to),
                    scenario = scenario0
                ),
            )
        ),
    )
}
