plugins {
    id("ais-engine.kotlin-lib-conventions")
}

dependencies {
    api("nl.teqplay.platform:api:$platformVersion") {
        // We need to exclude the api model here as otherwise it will take the version specified in the pom of platform
        exclude(group = "nl.teqplay.aisengine", module = "api")
    }

    testImplementation(project(":lib:testing-event"))
    testImplementation(project(":lib:testing-platform"))
}
