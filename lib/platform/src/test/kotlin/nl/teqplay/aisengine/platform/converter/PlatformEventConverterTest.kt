package nl.teqplay.aisengine.platform.converter

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.testing.event.createAisDestinationChangedEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusPortcallFinishEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusVisitsUpdateEvent
import nl.teqplay.aisengine.testing.event.createStopEndEvent
import nl.teqplay.aisengine.testing.event.createStopStartEvent
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PlatformEventConverterTest {
    private fun `ignored events`() = Stream.of(
        createAisDestinationChangedEvent(),
        createStopStartEvent(),
        createStopEndEvent(),
        createPortcallPlusVisitsUpdateEvent(),
        createPortcallPlusPortcallFinishEvent()
    )

    @ParameterizedTest
    @MethodSource("ignored events")
    fun `test ignored events`(event: Event) {
        assertNull(convertToPlatformEvent(event))
    }
}
