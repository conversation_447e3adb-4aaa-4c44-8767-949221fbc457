package nl.teqplay.aisengine.platform

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.platform.model.ShipInfo
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.util.stream.Stream
import nl.teqplay.platform.model.Location as PlatformLocation

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class AisMessageConverterUtilsKtTest {

    private fun convertData() = Stream.of(
        AisMessage(
            mmsi = 1,
            messageTime = Instant.EPOCH,
            sources = setOf("SPIRE"),
            location = Location(1.0, 2.0),
            heading = 1,
            positionAccuracy = AisMessage.PositionAccuracy.HIGH,
            speedOverGround = 1f,
            courseOverGround = 2f,
            status = AisMessage.ShipStatus.UNDER_WAY_SAILING,
            rateOfTurn = 0,
            specialManeuverStatus = AisMessage.SpecialManeuverStatus.NOT_ENGAGED_IN_SPECIAL_MANEUVER,

            imo = 2,
            name = "name",
            callSign = "callSign",
            shipType = AisMessage.ShipType.TUG,
            draught = 3f,
            eta = Instant.EPOCH,
            destination = "destination",
            transponderPosition = TransponderPosition(1, 2, 3, 4),
            positionSensorType = AisMessage.PositionSensorType.GPS,
            aisVersion = AisMessage.AisVersionIndicator.FUTURE,
            usingDataTerminal = true,

            eni = 3,
        ),
        AisMessage(
            mmsi = 1,
            messageTime = Instant.EPOCH,
            sources = emptySet(),
            location = Location(1.0, 2.0)
        )
    )

    @ParameterizedTest
    @MethodSource("convertData")
    fun convert(message: AisMessage) {
        val shipInfo = message.toLegacyShipInfo(false)
        val newMessage = shipInfo.toAisMessage()
        assertEquals(message, newMessage)
    }

    @Test
    fun `convert - ShipInfo - no mmsi`() {
        val shipInfo = ShipInfo()
        assertNull(shipInfo.toAisMessage(), "no mmsi, can't construct message")
    }

    @Test
    fun `convert - ShipInfo - with mmsi, no timeLastUpdate or location`() {
        val shipInfo = ShipInfo("1")
        assertNull(shipInfo.toAisMessage(), "no timeLastUpdate or location, can't construct message")
    }

    @Test
    fun `convert - ShipInfo - with mmsi & timeLastUpdate & location`() {
        val shipInfo = ShipInfo("1")
        shipInfo.location = PlatformLocation(1.0, 2.0)
        shipInfo.timeLastUpdate = Instant.EPOCH.toEpochMilli()
        shipInfo.trueHeading = 511
        val message = shipInfo.toAisMessage()
        val newShipInfo = message?.toLegacyShipInfo(false)
        // ShipInfo uses the uuid as hashCode() so we need to compare them as strings since it is more detailed
        assertEquals(shipInfo.toString(), newShipInfo.toString())
    }
}
