package nl.teqplay.aisengine.platform

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.SpeedEvent.SpeedType
import nl.teqplay.aisengine.event.model.encountermetadata.BoatmanEncounterMetadata
import nl.teqplay.aisengine.event.model.encountermetadata.TugEncounterMetadata
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType
import nl.teqplay.aisengine.platform.converter.convertToAisEngineEvent
import nl.teqplay.aisengine.platform.converter.convertToPlatformEvent
import nl.teqplay.aisengine.testing.event.createAisDraughtChangedEvent
import nl.teqplay.aisengine.testing.event.createAisEtaChangedEvent
import nl.teqplay.aisengine.testing.event.createAisLostEvent
import nl.teqplay.aisengine.testing.event.createAisRecoverEvent
import nl.teqplay.aisengine.testing.event.createAisStatusChangedEvent
import nl.teqplay.aisengine.testing.event.createAreaEndEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.aisengine.testing.event.createConfirmedBerthEndEvent
import nl.teqplay.aisengine.testing.event.createConfirmedBerthStartEvent
import nl.teqplay.aisengine.testing.event.createEncounterEndEvent
import nl.teqplay.aisengine.testing.event.createEncounterStartEvent
import nl.teqplay.aisengine.testing.event.createHamisAddPortcallVisitEvent
import nl.teqplay.aisengine.testing.event.createHamisAgentOrderEvent
import nl.teqplay.aisengine.testing.event.createHamisAgentReportsTugsEvent
import nl.teqplay.aisengine.testing.event.createHamisAtaEvent
import nl.teqplay.aisengine.testing.event.createHamisAtdEvent
import nl.teqplay.aisengine.testing.event.createHamisCancelPortcallVisitEvent
import nl.teqplay.aisengine.testing.event.createHamisEtaCancelEvent
import nl.teqplay.aisengine.testing.event.createHamisEtaEvent
import nl.teqplay.aisengine.testing.event.createHamisEtaRequestEvent
import nl.teqplay.aisengine.testing.event.createHamisEtdEvent
import nl.teqplay.aisengine.testing.event.createHamisEtdRequestEvent
import nl.teqplay.aisengine.testing.event.createHamisNauticalOrderEvent
import nl.teqplay.aisengine.testing.event.createHamisPilotBoardingEtaEvent
import nl.teqplay.aisengine.testing.event.createHamisPilotOnBoardEndEvent
import nl.teqplay.aisengine.testing.event.createHamisPilotOnBoardStartEvent
import nl.teqplay.aisengine.testing.event.createHamisUpdatePortcallVisitEvent
import nl.teqplay.aisengine.testing.event.createHamisVisitCancellationEvent
import nl.teqplay.aisengine.testing.event.createHamisVisitDeclarationEvent
import nl.teqplay.aisengine.testing.event.createLockEtaEvent
import nl.teqplay.aisengine.testing.event.createLockEtdEvent
import nl.teqplay.aisengine.testing.event.createPortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusAtaEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusAtdEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusEtaEvent
import nl.teqplay.aisengine.testing.event.createPortcallPlusEtdEvent
import nl.teqplay.aisengine.testing.event.createShipMovingEndEvent
import nl.teqplay.aisengine.testing.event.createShipMovingStartEvent
import nl.teqplay.aisengine.testing.event.createSpeedChangedEvent
import nl.teqplay.aisengine.testing.event.createTrueDestinationChangedEvent
import nl.teqplay.aisengine.testing.event.createUniqueBerthEndEvent
import nl.teqplay.aisengine.testing.event.createUniqueBerthStartEvent
import nl.teqplay.aisengine.testing.event.defaultBerthAreaIdentifier
import nl.teqplay.aisengine.testing.event.defaultBerthId
import nl.teqplay.aisengine.testing.event.defaultBerthIdentifier
import nl.teqplay.aisengine.testing.event.defaultBerthName
import nl.teqplay.aisengine.testing.event.defaultIsrsCode
import nl.teqplay.aisengine.testing.event.defaultOtherPort
import nl.teqplay.aisengine.testing.event.defaultPort
import nl.teqplay.aisengine.testing.event.defaultPortAreaIdentifier
import nl.teqplay.aisengine.testing.event.defaultStartEventIdentifier
import nl.teqplay.aisengine.testing.event.defaultTerminalName
import nl.teqplay.aisengine.testing.platform.createPlatformAddPortcallVisitEvent
import nl.teqplay.aisengine.testing.platform.createPlatformAgentOrderEvent
import nl.teqplay.aisengine.testing.platform.createPlatformAgentReportsTugsEvent
import nl.teqplay.aisengine.testing.platform.createPlatformAisEtaChangedEvent
import nl.teqplay.aisengine.testing.platform.createPlatformAisLostEvent
import nl.teqplay.aisengine.testing.platform.createPlatformAisRecoveredEvent
import nl.teqplay.aisengine.testing.platform.createPlatformAtaEvent
import nl.teqplay.aisengine.testing.platform.createPlatformAtdEvent
import nl.teqplay.aisengine.testing.platform.createPlatformAuthorityEvent
import nl.teqplay.aisengine.testing.platform.createPlatformBargeBunkerEvent
import nl.teqplay.aisengine.testing.platform.createPlatformBargeWaterEvent
import nl.teqplay.aisengine.testing.platform.createPlatformBerthEvent
import nl.teqplay.aisengine.testing.platform.createPlatformBoatmanEvent
import nl.teqplay.aisengine.testing.platform.createPlatformBunkerEvent
import nl.teqplay.aisengine.testing.platform.createPlatformCancelPortcallVisitEvent
import nl.teqplay.aisengine.testing.platform.createPlatformCargoBargeEvent
import nl.teqplay.aisengine.testing.platform.createPlatformConfirmedBerthEvent
import nl.teqplay.aisengine.testing.platform.createPlatformCraneEvent
import nl.teqplay.aisengine.testing.platform.createPlatformDestinationChangedEvent
import nl.teqplay.aisengine.testing.platform.createPlatformDraughtChangedEvent
import nl.teqplay.aisengine.testing.platform.createPlatformEndSeaPassageEvent
import nl.teqplay.aisengine.testing.platform.createPlatformEtaCancelEvent
import nl.teqplay.aisengine.testing.platform.createPlatformEtaEvent
import nl.teqplay.aisengine.testing.platform.createPlatformEtaRequestEvent
import nl.teqplay.aisengine.testing.platform.createPlatformEtdEvent
import nl.teqplay.aisengine.testing.platform.createPlatformEtdRequestEvent
import nl.teqplay.aisengine.testing.platform.createPlatformFenderEvent
import nl.teqplay.aisengine.testing.platform.createPlatformHamisPilotBoardingEtaEvent
import nl.teqplay.aisengine.testing.platform.createPlatformLockEtaEvent
import nl.teqplay.aisengine.testing.platform.createPlatformLockEtdEvent
import nl.teqplay.aisengine.testing.platform.createPlatformLubesEvent
import nl.teqplay.aisengine.testing.platform.createPlatformNauticalOrderEvent
import nl.teqplay.aisengine.testing.platform.createPlatformPilotEvent
import nl.teqplay.aisengine.testing.platform.createPlatformPilotOnBoardEvent
import nl.teqplay.aisengine.testing.platform.createPlatformPortAtaAtdEvent
import nl.teqplay.aisengine.testing.platform.createPlatformPortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.testing.platform.createPlatformPushBargeEvent
import nl.teqplay.aisengine.testing.platform.createPlatformShipInfraEncounterEvent
import nl.teqplay.aisengine.testing.platform.createPlatformShipMovingEvent
import nl.teqplay.aisengine.testing.platform.createPlatformSpeedChangedEvent
import nl.teqplay.aisengine.testing.platform.createPlatformStatusChangedEvent
import nl.teqplay.aisengine.testing.platform.createPlatformSupplyBargeEvent
import nl.teqplay.aisengine.testing.platform.createPlatformSwogEvent
import nl.teqplay.aisengine.testing.platform.createPlatformTankerBargeEvent
import nl.teqplay.aisengine.testing.platform.createPlatformTenderEvent
import nl.teqplay.aisengine.testing.platform.createPlatformTeqperimentEvent
import nl.teqplay.aisengine.testing.platform.createPlatformTerminalEvent
import nl.teqplay.aisengine.testing.platform.createPlatformTugEvent
import nl.teqplay.aisengine.testing.platform.createPlatformTugWaitingForDepartureEvent
import nl.teqplay.aisengine.testing.platform.createPlatformUniqueBerthEvent
import nl.teqplay.aisengine.testing.platform.createPlatformUpdatePortcallVisitEvent
import nl.teqplay.aisengine.testing.platform.createPlatformVhfEvent
import nl.teqplay.aisengine.testing.platform.createPlatformVisitCancellationEvent
import nl.teqplay.aisengine.testing.platform.createPlatformVisitDeclarationEvent
import nl.teqplay.aisengine.testing.platform.createPlatformWasteEvent
import nl.teqplay.aisengine.testing.platform.createPlatformWaterEvent
import nl.teqplay.aisengine.testing.platform.createTeqplayLocationBasedEvent
import nl.teqplay.aisengine.testing.platform.defaultIsrsId
import nl.teqplay.aisengine.testing.platform.defaultPlatformTime
import nl.teqplay.aisengine.testing.platform.defaultPlatformTime2
import nl.teqplay.aisengine.testing.platform.defaultPlatformTime2Long
import nl.teqplay.aisengine.testing.platform.defaultPlatformTime3
import nl.teqplay.aisengine.testing.platform.defaultPlatformTimeLong
import nl.teqplay.aisengine.testing.platform.defaultShipIdentifierWithoutImo
import nl.teqplay.platform.model.event.TeqplayEvent
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.test.context.TestConstructor
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class AisEngineEventConverterUtilsTest {

    private fun eventTestCases() = Stream.of(
        Arguments.of(
            createPlatformDestinationChangedEvent(
                description = "Destination changed (to NLRTM to NLRTM => NLAMS)",
                title = "Destination changed (to NLRTM to NLRTM => NLAMS)",
                summary = "Destination changed (to NLRTM to NLRTM => NLAMS)",
                imo = null
            ),
            // NOTE: Platform never sets the IMO correctly, only on imo not shipImo
            createTrueDestinationChangedEvent(ship = defaultShipIdentifierWithoutImo)
        ),
        Arguments.of(
            createPlatformDraughtChangedEvent(
                description = "Draught changed (10.0m to 15.0m)",
                summary = "Draught changed (10.0m to 15.0m)",
                title = "Draught changed (10.0m to 15.0m)"
            ),
            createAisDraughtChangedEvent()
        ),
        Arguments.of(
            createPlatformSpeedChangedEvent(
                description = "[111111111] is Accelerating",
                summary = "111111111 is Accelerating",
                title = "is Accelerating",
            ),
            createSpeedChangedEvent()
        ),
        Arguments.of(
            createPlatformSpeedChangedEvent(
                description = "[111111111] is Slowing down",
                summary = "111111111 is Slowing down",
                title = "is Slowing down",
            ),
            createSpeedChangedEvent(speedType = SpeedType.SLOWING_DOWN)
        ),
        Arguments.of(
            createPlatformAisEtaChangedEvent(
                description = "Eta changed in AIS (2023-05-02T00:00:00Z to 2023-05-07T00:00:00Z)",
                summary = "Eta changed in AIS (2023-05-02T00:00:00Z to 2023-05-07T00:00:00Z)",
                title = "Eta changed in AIS (2023-05-02T00:00:00Z to 2023-05-07T00:00:00Z)"
            ),
            createAisEtaChangedEvent(
                oldValue = defaultPlatformTime2,
                newValue = defaultPlatformTime3,
                ship = defaultShipIdentifierWithoutImo
            )
        ),
        Arguments.of(
            createPlatformStatusChangedEvent(
                description = "AIS status changed (AT_ANCHOR to MOORED)",
                summary = "AIS status changed (AT_ANCHOR to MOORED)",
                title = "AIS status changed (AT_ANCHOR to MOORED)"
            ),
            createAisStatusChangedEvent(
                oldValue = AisMessage.ShipStatus.AT_ANCHOR,
                newValue = AisMessage.ShipStatus.MOORED
            )
        ),
        Arguments.of(
            createPlatformPortAtaAtdEvent(
                type = "area.port.NLRTM.start",
                title = "enters NLRTM",
                description = "111111111 enters NLRTM",
                summary = "111111111 in NLRTM"
            ),
            createAreaStartEvent(area = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort), draught = 10.0f)
        ),
        Arguments.of(
            createPlatformPortAtaAtdEvent(
                type = "area.port.NLRTM.end",
                title = "exits NLRTM",
                description = "111111111 exits NLRTM",
                summary = "111111111 at NLRTM",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort), draught = 10.0f)
        ),
        Arguments.of(
            createPlatformVhfEvent(
                type = "area.vts.rtm.start",
                title = "enters vts.rtm",
                description = "111111111 enters vts.rtm",
                summary = "111111111 in vts.rtm"
            ),
            createAreaStartEvent(area = AreaIdentifier(null, AreaType.VHF, "vts.rtm"))
        ),
        Arguments.of(
            createPlatformVhfEvent(
                type = "area.vts.rtm.end",
                relatedEventId = defaultStartEventIdentifier,
                title = "exits vts.rtm",
                description = "111111111 exits vts.rtm",
                summary = "111111111 at vts.rtm"
            ),
            createAreaEndEvent(area = AreaIdentifier(null, AreaType.VHF, "vts.rtm"))
        ),
        Arguments.of(
            createPlatformTerminalEvent(
                description = "111111111 enters $defaultTerminalName",
                summary = "111111111 in $defaultTerminalName",
                title = "enters $defaultTerminalName"
            ),
            createAreaStartEvent(area = AreaIdentifier(null, AreaType.TERMINAL, defaultTerminalName))
        ),
        Arguments.of(
            createPlatformTerminalEvent(
                relatedEventId = defaultStartEventIdentifier,
                description = "111111111 exits $defaultTerminalName",
                summary = "111111111 at $defaultTerminalName",
                title = "exits $defaultTerminalName"
            ),
            createAreaEndEvent(area = AreaIdentifier(null, AreaType.TERMINAL, defaultTerminalName))
        ),
        Arguments.of(
            createPlatformEndSeaPassageEvent(
                type = "area.sgsin.eosp.start",
                title = "enters SGSIN EOSP",
                summary = "111111111 in SGSIN EOSP",
                description = "111111111 enters SGSIN EOSP"
            ),
            createAreaStartEvent(area = AreaIdentifier(null, AreaType.END_OF_SEA_PASSAGE, "SGSIN EOSP", "SGSIN"))
        ),
        Arguments.of(
            createPlatformEndSeaPassageEvent(
                type = "area.sgsin.eosp.end",
                relatedEventId = defaultStartEventIdentifier,
                title = "exits SGSIN EOSP",
                summary = "111111111 at SGSIN EOSP",
                description = "111111111 exits SGSIN EOSP"
            ),
            createAreaEndEvent(area = AreaIdentifier(null, AreaType.END_OF_SEA_PASSAGE, "SGSIN EOSP", "SGSIN"))
        ),
        Arguments.of(
            createPlatformShipInfraEncounterEvent(
                type = "area.lock.start",
                title = "enters TEST_ISRS_ID",
                summary = "111111111 in TEST_ISRS_ID",
                description = "111111111 enters TEST_ISRS_ID"
            ),
            createAreaStartEvent(area = AreaIdentifier(null, AreaType.LOCK, defaultIsrsId))
        ),
        Arguments.of(
            createPlatformShipInfraEncounterEvent(
                type = "area.lock.end",
                relatedEventId = defaultStartEventIdentifier,
                title = "exits TEST_ISRS_ID",
                summary = "111111111 at TEST_ISRS_ID",
                description = "111111111 exits TEST_ISRS_ID"
            ),
            createAreaEndEvent(area = AreaIdentifier(null, AreaType.LOCK, defaultIsrsId))
        ),
        Arguments.of(
            createPlatformBerthEvent(
                adjacentBerthIds = null,
                title = "enters ERNIE",
                summary = "111111111 in ERNIE",
                description = "111111111 enters ERNIE",
                harbour = defaultTerminalName,
                harbourName = defaultTerminalName,
                orientation = null
            ),
            createAreaStartEvent(
                area = AreaIdentifier(defaultBerthId, AreaType.BERTH, defaultBerthName, defaultPort),
                berth = defaultBerthIdentifier,
                draught = 10.0f,
                heading = 180
            )
        ),
        Arguments.of(
            createPlatformBerthEvent(
                relatedEventId = defaultStartEventIdentifier,
                adjacentBerthIds = null,
                title = "exits ERNIE",
                summary = "111111111 at ERNIE",
                description = "111111111 exits ERNIE",
                harbour = defaultTerminalName,
                harbourName = defaultTerminalName,
                orientation = null
            ),
            createAreaEndEvent(
                area = AreaIdentifier(defaultBerthId, AreaType.BERTH, defaultBerthName, defaultPort),
                berth = defaultBerthIdentifier,
                draught = 10.0f,
                heading = 180
            )
        ),
        Arguments.of(
            createPlatformConfirmedBerthEvent(
                adjacentBerthIds = null,
                title = "enters ERNIE",
                summary = "111111111 in ERNIE",
                description = "111111111 enters ERNIE",
                harbour = defaultTerminalName,
                harbourName = defaultTerminalName,
                orientation = null
            ),
            createConfirmedBerthStartEvent(
                berthEventId = "DEFAULT_EVENT_ID_BERTH",
                area = AreaIdentifier(defaultBerthId, AreaType.BERTH, defaultBerthName, defaultPort),
                berth = defaultBerthIdentifier,
                draught = 10.0f,
                heading = 180
            )
        ),
        Arguments.of(
            createPlatformConfirmedBerthEvent(
                relatedEventId = defaultStartEventIdentifier,
                adjacentBerthIds = null,
                title = "exits ERNIE",
                summary = "111111111 at ERNIE",
                description = "111111111 exits ERNIE",
                harbour = defaultTerminalName,
                harbourName = defaultTerminalName,
                orientation = null
            ),
            createConfirmedBerthEndEvent(
                // start event id is null here because platform doesn't have any reference to this like we do in the new model
                startEventId = null,
                berthEventId = "DEFAULT_EVENT_ID_BERTH",
                area = AreaIdentifier(defaultBerthId, AreaType.BERTH, defaultBerthName, defaultPort),
                berth = defaultBerthIdentifier,
                draught = 10.0f,
                heading = 180
            )
        ),
        Arguments.of(
            createPlatformUniqueBerthEvent(
                adjacentBerthIds = null,
                title = "enters ERNIE",
                summary = "111111111 in ERNIE",
                description = "111111111 enters ERNIE",
                harbour = defaultTerminalName,
                harbourName = defaultTerminalName,
                orientation = null
            ),
            createUniqueBerthStartEvent(
                berthEventId = "DEFAULT_EVENT_ID_CONFIRMED",
                area = AreaIdentifier(defaultBerthId, AreaType.BERTH, defaultBerthName, defaultPort),
                berth = defaultBerthIdentifier,
                draught = 10.0f,
                heading = 180
            )
        ),
        Arguments.of(
            createPlatformUniqueBerthEvent(
                relatedEventId = "DEFAULT_EVENT_ID_CONFIRMED",
                adjacentBerthIds = null,
                title = "exits ERNIE",
                summary = "111111111 at ERNIE",
                description = "111111111 exits ERNIE",
                harbour = defaultTerminalName,
                harbourName = defaultTerminalName,
                orientation = null
            ),
            createUniqueBerthEndEvent(
                // start event id is null here because platform doesn't have any reference to this like we do in the new model
                startEventId = null,
                berthEventId = "DEFAULT_EVENT_ID_CONFIRMED",
                area = AreaIdentifier(defaultBerthId, AreaType.BERTH, defaultBerthName, defaultPort),
                berth = defaultBerthIdentifier,
                draught = 10.0f,
                heading = 180
            )
        ),
        Arguments.of(
            createPlatformShipMovingEvent(
                title = "Started moving",
                summary = "moving",
                description = "[111111111] started moving"
            ),
            createShipMovingStartEvent()
        ),
        Arguments.of(
            createPlatformShipMovingEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Stopped moving",
                summary = "moving",
                description = "[111111111] stopped moving"
            ),
            createShipMovingEndEvent()
        ),
        Arguments.of(
            createPlatformTugEvent(
                title = "Tug 222222222 arrives",
                summary = "Tug 222222222",
                description = "Tug 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(
                encounterType = EncounterType.TUG,
                metadata = TugEncounterMetadata(true)
            )
        ),
        Arguments.of(
            createPlatformTugEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Tug 222222222 departs",
                summary = "Tug 222222222",
                description = "Tug 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(
                encounterType = EncounterType.TUG,
                metadata = TugEncounterMetadata(true)
            )
        ),
        Arguments.of(
            createPlatformPilotEvent(
                title = "Pilot 222222222 arrives",
                summary = "Pilot 222222222",
                description = "Pilot 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.PILOT)
        ),
        Arguments.of(
            createPlatformPilotEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Pilot 222222222 departs",
                summary = "Pilot 222222222",
                description = "Pilot 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.PILOT)
        ),
        Arguments.of(
            createPlatformBoatmanEvent(
                title = "Boatman 222222222 arrives",
                summary = "Boatman 222222222",
                description = "Boatman 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(
                encounterType = EncounterType.BOATMAN,
                metadata = BoatmanEncounterMetadata(true, 1, true)
            )
        ),
        Arguments.of(
            createPlatformBoatmanEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Boatman 222222222 departs",
                summary = "Boatman 222222222",
                description = "Boatman 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(
                encounterType = EncounterType.BOATMAN,
                metadata = BoatmanEncounterMetadata(true, 1, true)
            )
        ),
        Arguments.of(
            createPlatformBunkerEvent(
                title = "Bunker barge 222222222 arrives",
                summary = "Bunker barge 222222222",
                description = "Bunker barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.BUNKER)
        ),
        Arguments.of(
            createPlatformBunkerEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Bunker barge 222222222 departs",
                summary = "Bunker barge 222222222",
                description = "Bunker barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.BUNKER)
        ),
        Arguments.of(
            createPlatformAuthorityEvent(
                title = "Authority 222222222 arrives",
                summary = "Authority 222222222",
                description = "Authority 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.AUTHORITY)
        ),
        Arguments.of(
            createPlatformAuthorityEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Authority 222222222 departs",
                summary = "Authority 222222222",
                description = "Authority 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.AUTHORITY)
        ),
        Arguments.of(
            createPlatformWasteEvent(
                title = "Waste barge 222222222 arrives",
                summary = "Waste barge 222222222",
                description = "Waste barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.WASTE)
        ),
        Arguments.of(
            createPlatformWasteEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Waste barge 222222222 departs",
                summary = "Waste barge 222222222",
                description = "Waste barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.WASTE)
        ),
        Arguments.of(
            createPlatformSwogEvent(
                title = "Slops barge 222222222 arrives",
                summary = "Slops barge 222222222",
                description = "Slops barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.SWOG)
        ),
        Arguments.of(
            createPlatformSwogEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Slops barge 222222222 departs",
                summary = "Slops barge 222222222",
                description = "Slops barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.SWOG)
        ),
        Arguments.of(
            createPlatformWaterEvent(
                title = "Water barge 222222222 arrives",
                summary = "Water barge 222222222",
                description = "Water barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.WATER)
        ),
        Arguments.of(
            createPlatformWaterEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Water barge 222222222 departs",
                summary = "Water barge 222222222",
                description = "Water barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.WATER)
        ),
        Arguments.of(
            createPlatformTenderEvent(
                title = "Tender barge 222222222 arrives",
                summary = "Tender barge 222222222",
                description = "Tender barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.TENDER)
        ),
        Arguments.of(
            createPlatformTenderEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Tender barge 222222222 departs",
                summary = "Tender barge 222222222",
                description = "Tender barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.TENDER)
        ),
        Arguments.of(
            createPlatformFenderEvent(
                title = "Fender barge 222222222 arrives",
                summary = "Fender barge 222222222",
                description = "Fender barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.FENDER)
        ),
        Arguments.of(
            createPlatformFenderEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Fender barge 222222222 departs",
                summary = "Fender barge 222222222",
                description = "Fender barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.FENDER)
        ),
        Arguments.of(
            createPlatformCraneEvent(
                title = "Crane boat 222222222 arrives",
                summary = "Crane boat 222222222",
                description = "Crane boat 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.CRANE)
        ),
        Arguments.of(
            createPlatformCraneEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Crane boat 222222222 departs",
                summary = "Crane boat 222222222",
                description = "Crane boat 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.CRANE)
        ),
        Arguments.of(
            createPlatformLubesEvent(
                title = "Lubes barge 222222222 arrives",
                summary = "Lubes barge 222222222",
                description = "Lubes barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.LUBES)
        ),
        Arguments.of(
            createPlatformLubesEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Lubes barge 222222222 departs",
                summary = "Lubes barge 222222222",
                description = "Lubes barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.LUBES)
        ),
        Arguments.of(
            createPlatformTugWaitingForDepartureEvent(
                title = "Tug 222222222 arrives",
                summary = "Tug 222222222",
                description = "Tug 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.TUG_WAITING_DEPARTURE)
        ),
        Arguments.of(
            createPlatformTugWaitingForDepartureEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Tug 222222222 departs",
                summary = "Tug 222222222",
                description = "Tug 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.TUG_WAITING_DEPARTURE)
        ),
        Arguments.of(
            createPlatformSupplyBargeEvent(
                title = "Supply barge 222222222 arrives",
                summary = "Supply barge 222222222",
                description = "Supply barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.BARGE_SUPPLY)
        ),
        Arguments.of(
            createPlatformSupplyBargeEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Supply barge 222222222 departs",
                summary = "Supply barge 222222222",
                description = "Supply barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.BARGE_SUPPLY)
        ),
        Arguments.of(
            createPlatformCargoBargeEvent(
                title = "CargoOperations barge 222222222 arrives",
                summary = "CargoOperations barge 222222222",
                description = "CargoOperations barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.BARGE_CARGO)
        ),
        Arguments.of(
            createPlatformCargoBargeEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "CargoOperations barge 222222222 departs",
                summary = "CargoOperations barge 222222222",
                description = "CargoOperations barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.BARGE_CARGO)
        ),
        Arguments.of(
            createPlatformTankerBargeEvent(
                title = "Tanker barge 222222222 arrives",
                summary = "Tanker barge 222222222",
                description = "Tanker barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.BARGE_TANKER)
        ),
        Arguments.of(
            createPlatformTankerBargeEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Tanker barge 222222222 departs",
                summary = "Tanker barge 222222222",
                description = "Tanker barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.BARGE_TANKER)
        ),
        Arguments.of(
            createPlatformPushBargeEvent(
                title = "Push barge 222222222 arrives",
                summary = "Push barge 222222222",
                description = "Push barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.BARGE_PUSH)
        ),
        Arguments.of(
            createPlatformPushBargeEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Push barge 222222222 departs",
                summary = "Push barge 222222222",
                description = "Push barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.BARGE_PUSH)
        ),
        Arguments.of(
            createPlatformBargeWaterEvent(
                title = "Water barge 222222222 arrives",
                summary = "Water barge 222222222",
                description = "Water barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.BARGE_WATER)
        ),
        Arguments.of(
            createPlatformBargeWaterEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Water barge 222222222 departs",
                summary = "Water barge 222222222",
                description = "Water barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.BARGE_WATER)
        ),
        Arguments.of(
            createPlatformBargeBunkerEvent(
                title = "Bunker barge 222222222 arrives",
                summary = "Bunker barge 222222222",
                description = "Bunker barge 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.BARGE_BUNKER)
        ),
        Arguments.of(
            createPlatformBargeBunkerEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Bunker barge 222222222 departs",
                summary = "Bunker barge 222222222",
                description = "Bunker barge 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.BARGE_BUNKER)
        ),
        Arguments.of(
            createPlatformTeqperimentEvent(
                title = "Teqperiment 222222222 arrives",
                summary = "Teqperiment 222222222",
                description = "Teqperiment 222222222 arrives at 111111111"
            ),
            createEncounterStartEvent(encounterType = EncounterType.TEQPERIMENT)
        ),
        Arguments.of(
            createPlatformTeqperimentEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Teqperiment 222222222 departs",
                summary = "Teqperiment 222222222",
                description = "Teqperiment 222222222 departs from 111111111"
            ),
            createEncounterEndEvent(encounterType = EncounterType.TEQPERIMENT)
        ),
        Arguments.of(
            createPlatformAisRecoveredEvent(
                title = "AIS recovered",
                summary = "AIS Signal recovered",
                description = "AIS Signal recovered for 111111111"
            ),
            createAisRecoverEvent()
        ),
        Arguments.of(
            createPlatformAisLostEvent(
                title = "AIS lost",
                summary = "AIS Signal lost",
                description = "AIS Signal lost for 111111111"
            ),
            createAisLostEvent()
        ),
        Arguments.of(
            createPlatformAtaEvent(
                title = "ATA: 2023-05-01T00:00:00Z at berth ERNIE",
                description = "ATA 111111111 : 2023-05-01T00:00:00Z at ERNIE"
            ),
            createHamisAtaEvent(
                movementId = "1000",
                area = defaultBerthAreaIdentifier.copy(name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformAtaEvent(
                title = "ATA: 2023-05-01T00:00:00Z at berth ERNIE",
                description = "ATA 111111111 : 2023-05-01T00:00:00Z at ERNIE",
                externalVisitId = null,
                movementId = null
            ),
            createPortcallPlusAtaEvent(
                area = defaultBerthAreaIdentifier.copy(name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformAtdEvent(
                title = "ATD: 2023-05-01T00:00:00Z from berth ERNIE",
                description = "ATD 111111111 : 2023-05-01T00:00:00Z from ERNIE"
            ),
            createHamisAtdEvent(
                area = defaultBerthAreaIdentifier.copy(name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformAtdEvent(
                title = "ATD: 2023-05-01T00:00:00Z from berth ERNIE",
                description = "ATD 111111111 : 2023-05-01T00:00:00Z from ERNIE",
                externalVisitId = null,
                movementId = null
            ),
            createPortcallPlusAtdEvent(
                area = defaultBerthAreaIdentifier.copy(name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformPortcallPilotBoardingEtaEvent(
                distanceInNm = null,
                title = "ETA Pbp 2023-05-01T00:00:00Z for ship [IMO:1111111]",
                description = "ETA Pbp 2023-05-01T00:00:00Z for ship [IMO:1111111]"
            ),
            createPortcallPilotBoardingEtaEvent(area = AreaIdentifier("TEST_LOCATION_ID", AreaType.PILOT_BOARDING_PLACE, "TEST_LOCATION", defaultPort))
        ),
        Arguments.of(
            createPlatformAddPortcallVisitEvent(),
            createHamisAddPortcallVisitEvent(port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort))
        ),
        Arguments.of(
            createPlatformUpdatePortcallVisitEvent(),
            createHamisUpdatePortcallVisitEvent(port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort))
        ),
        Arguments.of(
            createPlatformCancelPortcallVisitEvent(),
            createHamisCancelPortcallVisitEvent(port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort))
        ),
        Arguments.of(
            createPlatformAgentOrderEvent(
                title = "Agent Order: DEFAULT_ORDER",
                summary = "Agent Order: DEFAULT_ORDER",
                description = "Agent Order DEFAULT_ORDER for 111111111 portcall DEFAULT_UCRN"
            ),
            createHamisAgentOrderEvent(port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort))
        ),
        Arguments.of(
            createPlatformAgentReportsTugsEvent(
                title = "AgentReportsTugs: 2023-05-01T00:00:00Z",
                description = "Agent declares tug: 111111111 from berth: ERNIE at time: 2023-05-01T00:00:00Z"
            ),
            createHamisAgentReportsTugsEvent(
                port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort),
                berth = AreaIdentifier(null, AreaType.BERTH, defaultBerthName, defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtaCancelEvent(
                title = "ETA Authority cancelled",
                description = "ETA authority cancelled for 111111111",
                source = "Port Authority"
            ),
            createHamisEtaCancelEvent(port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort))
        ),
        Arguments.of(
            createPlatformEtaRequestEvent(
                title = "ETA Berth Request: 2023-05-01T00:00:00Z",
                description = "ETA Berth request for 111111111 in Rotterdam at 2023-05-01T00:00:00Z"
            ),
            createHamisEtaRequestEvent(
                port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort),
                berth = AreaIdentifier(null, AreaType.BERTH, defaultBerthName, defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtdRequestEvent(
                title = "ETD Request: 2023-05-01T00:00:00Z",
                description = "ETD request for 111111111 in Rotterdam at 2023-05-01T00:00:00Z"
            ),
            createHamisEtdRequestEvent(
                port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort),
                berth = AreaIdentifier(null, AreaType.BERTH, defaultBerthName, defaultPort)
            )
        ),
        Arguments.of(
            createPlatformHamisPilotBoardingEtaEvent(
                title = "ETA Authority: 2023-05-01T00:00:00Z",
                description = "ETA autority for 111111111 in DEFAULT_PILOT_STATION at 2023-05-01T00:00:00Z"
            ),
            createHamisPilotBoardingEtaEvent(port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort))
        ),
        Arguments.of(
            createPlatformNauticalOrderEvent(
                title = "DEFAULT_REPORTER ordered",
                summary = "DEFAULT_REPORTER ordered",
                description = "DEFAULT_REPORTER ordered nautical services for 111111111 portcall DEFAULT_UCRN"
            ),
            createHamisNauticalOrderEvent(port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort))
        ),
        Arguments.of(
            createPlatformPilotOnBoardEvent(
                title = "Pilot on board 2023-05-01T00:00:00Z",
                summary = "Pilot on board",
                description = "Pilot on board of 111111111 at 2023-05-01T00:00:00Z",
            ),
            createHamisPilotOnBoardStartEvent(port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort))
        ),
        Arguments.of(
            createPlatformPilotOnBoardEvent(
                relatedEventId = defaultStartEventIdentifier,
                title = "Pilot left ship at 2023-05-01T00:00:00Z",
                summary = "Pilot left ship",
                description = "Pilot left ship 111111111 at 2023-05-01T00:00:00Z"
            ),
            createHamisPilotOnBoardEndEvent(port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort))
        ),
        Arguments.of(
            createPlatformVisitCancellationEvent(
                title = "DEFAULT_UCRN port call cancellation",
                description = "111111111 will cancel portcall to Rotterdam with UCRN DEFAULT_UCRN",
                movementId = null
            ),
            createHamisVisitCancellationEvent(
                port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort),
                shipName = null
            )
        ),
        Arguments.of(
            createPlatformVisitDeclarationEvent(
                title = "DEFAULT_UCRN port call declaration",
                description = "DEFAULT_SHIP_NAME will visit Rotterdam with UCRN DEFAULT_UCRN"
            ),
            createHamisVisitDeclarationEvent(
                port = AreaIdentifier(null, AreaType.PORT, defaultPort, defaultPort),
                previousPort = AreaIdentifier(null, AreaType.PORT, defaultOtherPort, defaultOtherPort)
            )
        ),
        Arguments.of(
            createPlatformLockEtaEvent(
                isrsId = defaultIsrsCode,
                description = "ETA: DEFAULT_LOCK_NAME",
                title = "Lock Opening 111111111 02:00",
                timestamp = defaultPlatformTimeLong,
                dateTimePlan = defaultPlatformTime2Long
            ),
            createLockEtaEvent(
                createdTime = defaultPlatformTime,
                predictedTime = defaultPlatformTime2
            )
        ),
        Arguments.of(
            createPlatformLockEtdEvent(
                isrsId = defaultIsrsCode,
                description = "ETD: DEFAULT_LOCK_NAME",
                title = "Lock Exit 111111111 02:00",
                timestamp = defaultPlatformTimeLong,
                dateTimePlan = defaultPlatformTime2Long
            ),
            createLockEtdEvent(
                createdTime = defaultPlatformTime,
                predictedTime = defaultPlatformTime2
            )
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.nlrtm.sea-entrance.start",
                title = "enters NLRTM_SEA_ENTRANCE",
                description = "111111111 enters NLRTM_SEA_ENTRANCE",
                summary = "111111111 in NLRTM_SEA_ENTRANCE"
            ),
            createAreaStartEvent(area = AreaIdentifier(null, AreaType.CUSTOM, "NLRTM_SEA_ENTRANCE", defaultPort))
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.nlrtm.sea-entrance.end",
                title = "exits NLRTM_SEA_ENTRANCE",
                description = "111111111 exits NLRTM_SEA_ENTRANCE",
                summary = "111111111 at NLRTM_SEA_ENTRANCE",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier(null, AreaType.CUSTOM, "NLRTM_SEA_ENTRANCE", defaultPort))
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.nlams.sea-entrance.start",
                title = "enters NLAMS_SEA_ENTRANCE",
                description = "111111111 enters NLAMS_SEA_ENTRANCE",
                summary = "111111111 in NLAMS_SEA_ENTRANCE"
            ),
            createAreaStartEvent(area = AreaIdentifier(null, AreaType.CUSTOM, "NLAMS_SEA_ENTRANCE", "NLAMS"))
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.nlams.sea-entrance.end",
                title = "exits NLAMS_SEA_ENTRANCE",
                description = "111111111 exits NLAMS_SEA_ENTRANCE",
                summary = "111111111 at NLAMS_SEA_ENTRANCE",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier(null, AreaType.CUSTOM, "NLAMS_SEA_ENTRANCE", "NLAMS"))
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.beanr.sea-entrance.start",
                title = "enters BEANR_SEA_ENTRANCE",
                description = "111111111 enters BEANR_SEA_ENTRANCE",
                summary = "111111111 in BEANR_SEA_ENTRANCE"
            ),
            createAreaStartEvent(area = AreaIdentifier(null, AreaType.CUSTOM, "BEANR_SEA_ENTRANCE", "BEANR"))
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.beanr.sea-entrance.end",
                title = "exits BEANR_SEA_ENTRANCE",
                description = "111111111 exits BEANR_SEA_ENTRANCE",
                summary = "111111111 at BEANR_SEA_ENTRANCE",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier(null, AreaType.CUSTOM, "BEANR_SEA_ENTRANCE", "BEANR"))
        )
    )

    private fun toPlatformOnlyEventTestCases() = Stream.of(
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.rtm12nm.start",
                title = "enters NLRTM 12 nm Area",
                description = "111111111 enters NLRTM 12 nm Area",
                summary = "111111111 in NLRTM 12 nm Area"
            ),
            createAreaStartEvent(area = AreaIdentifier("$defaultPort.12nm", AreaType.NAUTICAL_MILE, "$defaultPort 12 nm Area", defaultPort), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.rtm12nm.end",
                title = "exits NLRTM 12 nm Area",
                description = "111111111 exits NLRTM 12 nm Area",
                summary = "111111111 at NLRTM 12 nm Area",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier("$defaultPort.12nm", AreaType.NAUTICAL_MILE, "$defaultPort 12 nm Area", defaultPort), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.rtm60nm.start",
                title = "enters NLRTM 60 nm Area",
                description = "111111111 enters NLRTM 60 nm Area",
                summary = "111111111 in NLRTM 60 nm Area"
            ),
            createAreaStartEvent(area = AreaIdentifier("$defaultPort.60nm", AreaType.NAUTICAL_MILE, "$defaultPort 60 nm Area", defaultPort), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.rtm60nm.end",
                title = "exits NLRTM 60 nm Area",
                description = "111111111 exits NLRTM 60 nm Area",
                summary = "111111111 at NLRTM 60 nm Area",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier("$defaultPort.60nm", AreaType.NAUTICAL_MILE, "$defaultPort 60 nm Area", defaultPort), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.rtm80nm.start",
                title = "enters NLRTM 80 nm Area",
                description = "111111111 enters NLRTM 80 nm Area",
                summary = "111111111 in NLRTM 80 nm Area"
            ),
            createAreaStartEvent(area = AreaIdentifier("$defaultPort.80nm", AreaType.NAUTICAL_MILE, "$defaultPort 80 nm Area", defaultPort), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.rtm80nm.end",
                title = "exits NLRTM 80 nm Area",
                description = "111111111 exits NLRTM 80 nm Area",
                summary = "111111111 at NLRTM 80 nm Area",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier("$defaultPort.80nm", AreaType.NAUTICAL_MILE, "$defaultPort 80 nm Area", defaultPort), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.rtm120nm.start",
                title = "enters NLRTM 120 nm Area",
                description = "111111111 enters NLRTM 120 nm Area",
                summary = "111111111 in NLRTM 120 nm Area"
            ),
            createAreaStartEvent(area = AreaIdentifier("$defaultPort.120nm", AreaType.NAUTICAL_MILE, "$defaultPort 120 nm Area", defaultPort), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.rtm120nm.end",
                title = "exits NLRTM 120 nm Area",
                description = "111111111 exits NLRTM 120 nm Area",
                summary = "111111111 at NLRTM 120 nm Area",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier("$defaultPort.120nm", AreaType.NAUTICAL_MILE, "$defaultPort 120 nm Area", defaultPort), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.BEANR.12nm.start",
                title = "enters BEANR 12 nm Area",
                description = "111111111 enters BEANR 12 nm Area",
                summary = "111111111 in BEANR 12 nm Area"
            ),
            createAreaStartEvent(area = AreaIdentifier("$defaultOtherPort.12nm", AreaType.NAUTICAL_MILE, "$defaultOtherPort 12 nm Area", defaultOtherPort), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.BEANR.12nm.end",
                title = "exits BEANR 12 nm Area",
                description = "111111111 exits BEANR 12 nm Area",
                summary = "111111111 at BEANR 12 nm Area",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier("$defaultOtherPort.12nm", AreaType.NAUTICAL_MILE, "$defaultOtherPort 12 nm Area", defaultOtherPort), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.maptm.12nm.start",
                title = "enters MAPTM 12 nm Area",
                description = "111111111 enters MAPTM 12 nm Area",
                summary = "111111111 in MAPTM 12 nm Area"
            ),
            createAreaStartEvent(area = AreaIdentifier("MAPTM.12nm", AreaType.NAUTICAL_MILE, "MAPTM 12 nm Area", "MAPTM"), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.maptm.12nm.end",
                title = "exits MAPTM 12 nm Area",
                description = "111111111 exits MAPTM 12 nm Area",
                summary = "111111111 at MAPTM 12 nm Area",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier("MAPTM.12nm", AreaType.NAUTICAL_MILE, "MAPTM 12 nm Area", "MAPTM"), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.esalg.12nm.start",
                title = "enters ESALG 12 nm Area",
                description = "111111111 enters ESALG 12 nm Area",
                summary = "111111111 in ESALG 12 nm Area"
            ),
            createAreaStartEvent(area = AreaIdentifier("ESALG.12nm", AreaType.NAUTICAL_MILE, "ESALG 12 nm Area", "ESALG"), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.esalg.12nm.end",
                title = "exits ESALG 12 nm Area",
                description = "111111111 exits ESALG 12 nm Area",
                summary = "111111111 at ESALG 12 nm Area",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier("ESALG.12nm", AreaType.NAUTICAL_MILE, "ESALG 12 nm Area", "ESALG"), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.sgsin.12nm.start",
                title = "enters SGSIN 12 nm Area",
                description = "111111111 enters SGSIN 12 nm Area",
                summary = "111111111 in SGSIN 12 nm Area"
            ),
            createAreaStartEvent(area = AreaIdentifier("SGSIN.12nm", AreaType.NAUTICAL_MILE, "SGSIN 12 nm Area", "SGSIN"), draught = 10.0f)
        ),
        Arguments.of(
            createTeqplayLocationBasedEvent(
                type = "area.sgsin.12nm.end",
                title = "exits SGSIN 12 nm Area",
                description = "111111111 exits SGSIN 12 nm Area",
                summary = "111111111 at SGSIN 12 nm Area",
                relatedEventId = defaultStartEventIdentifier
            ),
            createAreaEndEvent(area = AreaIdentifier("SGSIN.12nm", AreaType.NAUTICAL_MILE, "SGSIN 12 nm Area", "SGSIN"), draught = 10.0f)
        ),
        Arguments.of(
            createPlatformEtaEvent(
                type = TeqplayEvent.ETA_NOMINATION,
                externalVisitId = null,
                movementId = null
            ),
            createPortcallPlusEtaEvent(
                nomination = true,
                area = defaultBerthAreaIdentifier.copy(id = defaultBerthId, type = AreaType.BERTH, name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtaEvent(
                type = TeqplayEvent.ETA_BERTH,
                externalVisitId = null,
                movementId = null
            ),
            createPortcallPlusEtaEvent(
                area = defaultBerthAreaIdentifier.copy(id = defaultBerthId, type = AreaType.BERTH, name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtaEvent(
                type = TeqplayEvent.ETA_PORT,
                externalVisitId = null,
                movementId = null
            ),
            createPortcallPlusEtaEvent(
                area = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtaEvent(
                type = TeqplayEvent.ETA_PBP,
                externalVisitId = null,
                movementId = null
            ),
            createPortcallPlusEtaEvent(
                area = defaultBerthAreaIdentifier.copy(id = defaultBerthId, type = AreaType.PILOT_BOARDING_PLACE, name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtaEvent(
                type = TeqplayEvent.ETA_PBP,
                externalVisitId = null,
                movementId = null
            ),
            createPortcallPlusEtaEvent(
                area = defaultBerthAreaIdentifier.copy(id = defaultBerthId, type = AreaType.ANCHOR, name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtdEvent(
                type = TeqplayEvent.ETD_NOMINATION,
                externalVisitId = null
            ),
            createPortcallPlusEtdEvent(
                nomination = true,
                area = defaultBerthAreaIdentifier.copy(id = defaultBerthId, type = AreaType.BERTH, name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtdEvent(
                type = TeqplayEvent.ETD_BERTH,
                externalVisitId = null
            ),
            createPortcallPlusEtdEvent(
                area = defaultBerthAreaIdentifier.copy(id = defaultBerthId, type = AreaType.BERTH, name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtdEvent(
                type = TeqplayEvent.ETA_PBP,
                externalVisitId = null
            ),
            createPortcallPlusEtdEvent(
                area = defaultBerthAreaIdentifier.copy(id = defaultBerthId, type = AreaType.PILOT_BOARDING_PLACE, name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtdEvent(
                type = TeqplayEvent.ETA_PBP,
                externalVisitId = null
            ),
            createPortcallPlusEtdEvent(
                area = defaultBerthAreaIdentifier.copy(id = defaultBerthId, type = AreaType.ANCHOR, name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
    )

    private fun toAisEngineOnlyEventTestCases() = Stream.of(
        Arguments.of(
            createPlatformEtaEvent(type = TeqplayEvent.ETA),
            createHamisEtaEvent(
                area = defaultBerthAreaIdentifier.copy(id = defaultBerthId, type = AreaType.UNKNOWN, name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        ),
        Arguments.of(
            createPlatformEtdEvent(type = TeqplayEvent.ETD),
            createHamisEtdEvent(
                area = defaultBerthAreaIdentifier.copy(id = defaultBerthId, type = AreaType.UNKNOWN, name = defaultBerthName, unlocode = defaultPort),
                port = defaultPortAreaIdentifier.copy(id = null, name = defaultPort, unlocode = defaultPort)
            )
        )
    )

    @ParameterizedTest
    @MethodSource("eventTestCases", "toAisEngineOnlyEventTestCases")
    fun `should convert Platform event to AisEngine event`(platformEvent: TeqplayEvent, expected: Event) {
        val result = convertToAisEngineEvent(platformEvent)

        assertEquals(expected, result)
    }

    @ParameterizedTest
    @MethodSource("eventTestCases", "toPlatformOnlyEventTestCases")
    fun `should convert AisEngine event to Platform event`(expected: TeqplayEvent, aisEngineEvent: Event) {
        val result = convertToPlatformEvent(aisEngineEvent)

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expected)
    }
}
