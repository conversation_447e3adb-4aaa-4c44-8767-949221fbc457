package nl.teqplay.aisengine.platform.historic

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.aisstream.model.TypeNames
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.util.stream.Stream
import nl.teqplay.platform.model.Location as PlatformLocation

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class AisHistoricMessageConverterUtilsKtTest {

    private fun convertData() = Stream.of(
        AisHistoricMessage(
            mmsi = 0,
            messageTime = Instant.EPOCH,
            receptionTime = Instant.EPOCH,
            historic = false,
            source = "SPIRE",
            subSource = "t",
            messageType = "position",
            location = Location(1.0, 2.0),
            heading = 1,
            speedOverGround = 1f,
            courseOverGround = 2f,
            status = AisMessage.ShipStatus.UNDER_WAY_SAILING,
            imo = null, // not contained in legacy
            shipType = null, // not contained in legacy
            draught = 3f,
            eta = Instant.EPOCH,
            destination = "destination",
            transponderPosition = TransponderPosition(0, 1, 2, 3)
        ),
        AisHistoricMessage(
            mmsi = 0,
            messageTime = Instant.EPOCH,
            receptionTime = Instant.EPOCH,
            historic = false,
            source = "SPIRE",
            subSource = null,
            messageType = null,
            location = Location(1.0, 2.0)
        )
    )

    @ParameterizedTest
    @MethodSource("convertData")
    fun convert(message: AisHistoricMessage) {
        val shipInfo = message.toLegacyHistoricShipInfo()
        val newMessage = shipInfo.toAisHistoricMessage()

        // not all fields exist within the platform model
        val lossyMessage = message.copy(
            receptionTime = null,
            transponderPosition = null
        )

        assertEquals(lossyMessage, newMessage)
    }

    @Test
    fun parseAisSource() {
        val shipInfo = LegacyHistoricShipInfo(
            mmsi = "0",
            timeLastUpdate = 0,
            location = PlatformLocation(0.0, 0.0),
            source = "AIS:AISHUB-T:LONG_RANGE"
        )
        val aisHistoricMessage = shipInfo.toAisHistoricMessage()
        assertEquals("AISHUB", aisHistoricMessage.source)
        assertEquals("t", aisHistoricMessage.subSource)
        assertEquals(TypeNames.LONGRANGE, aisHistoricMessage.messageType)
    }
}
