package nl.teqplay.aisengine.platform

import nl.teqplay.aisengine.testing.platform.createPlatformBunkerEvent
import nl.teqplay.platform.model.event.ComposedEventView
import nl.teqplay.platform.model.event.ComposedTeqplayEvent
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class PlatformComposedEventsUtilsTest {

    @Test
    fun shouldCreateComposedTeqplayEventFromRelatedEvents() {
        val startEvent = createPlatformBunkerEvent("START_EVENT")
        val endEvent = createPlatformBunkerEvent("END_EVENT", relatedEventId = "START_EVENT")
        val input = sequenceOf(startEvent, endEvent)

        val result = composeEvents(input).toList()
        val expected = sequenceOf(ComposedTeqplayEvent(startEvent, endEvent)).toList()

        assertEquals(expected, result)
    }

    @Test
    fun shouldCreateComposedEventViewFromComposedEvent() {
        val startEvent = createPlatformBunkerEvent("START_EVENT")
        val endEvent = createPlatformBunkerEvent("END_EVENT", relatedEventId = "START_EVENT")
        val composedEvent = ComposedTeqplayEvent(startEvent, endEvent)
        val input = sequenceOf(composedEvent)

        val title = determineTitle(composedEvent)
        val result = input.getAllAsComposedEventView().toList()

        val expectedEventView = ComposedEventView(
            composedEvent.eventType,
            startEvent.datetime,
            endEvent.datetime,
            startEvent.shipMmsi,
            startEvent.otherMmsi,
            startEvent.uuid,
            endEvent.uuid,
            title,
            ComposedEventView.ComposedEventStatus.FINISHED,
            null
        )
        val expected = listOf(expectedEventView)

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expected)
    }

    @Test
    fun shouldCreateTitleFromComposedEvent() {
        val startEvent = createPlatformBunkerEvent("START_EVENT")
        val endEvent = createPlatformBunkerEvent("END_EVENT", relatedEventId = "START_EVENT")
        val composedEvent = ComposedTeqplayEvent(startEvent, endEvent)

        assertEquals("bunker 222222222 is alongside 111111111", determineTitle(composedEvent))
    }
}
