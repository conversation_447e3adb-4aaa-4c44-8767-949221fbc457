# ais-stream-history-consumer

Use as a dependency by adding:
```groovy
implementation project(':lib:ais-stream-history-consumer')
implementation "nl.teqplay.skeleton:datasource:$skeleton_version"
```

See `:lib:ship-state`'s `README.md` for its pre-requisites.

1. Provide your own implementation of `MinimalStateService`

```kotlin
@Component
class ShipStateService(
    stateDataSource: MinimalStateDataSource
) : MinimalStateService(
    stateDataSource = stateDataSource,
    syncInterval = Duration.ofMinutes(5)
) {

    override fun process(wrapper: AisWrapper<out BaseAisMessage>, previousState: MinimalState?) {
        // TODO:
    }
}
```

2. Create a manager for the `ConsumerSerivce`

By default, this service starts the consumer upon startup. You can adapt it to allow for starting/stopping on-demand.

```kotlin
@Component
class ConsumerManagementService(
    private val consumerService: ConsumerService
) {

    @PostConstruct
    fun initialize() {
        start()
    }

    fun start() = consumerService.start()
    fun stop() = consumerService.stop()
}
```

3. Initialize a data source for the `MinimalStateDataSource` as a bean

```kotlin
@Configuration
class MinimalStateDataSourceConfiguration {

    @Bean
    fun minimalStateDataSource(mongoDatabase: MongoDatabase) = MinimalStateDataSource(mongoDatabase)
}
```