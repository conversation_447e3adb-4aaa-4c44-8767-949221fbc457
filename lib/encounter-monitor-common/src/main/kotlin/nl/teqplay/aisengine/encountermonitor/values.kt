package nl.teqplay.aisengine.encountermonitor

/**
 * Encounter detection distance. This should be configurable in the future via a config setting.
 */
const val distanceInMeters = 450.0

/**
 * Distance for long-range encounter detection. This is used for detecting encounters of the [longrangePilotVessels].
 */
const val longrangeDistanceInMeters = 3 * distanceInMeters

/**
 * MMSIs of long-range pilot vessels. These vessels are handled differently in terms of encounters, as they use a
 * (non-AIS-equipped) joll to bring the pilots on board at sea, so their encounter distance is much larger.
 */
val longrangePilotVessels = setOf(
    // NLRTM
    244820146, 245142000, 244790810, 211464260, 246380000, 205592000, 205110000, 205593000, 205594000,

    // USNYC
    368138010
)
