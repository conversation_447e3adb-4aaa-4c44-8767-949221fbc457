package nl.teqplay.aisengine.platform.auth.config

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.platform.auth.datasource.PlatformUserDatasource
import nl.teqplay.aisengine.platform.auth.properties.PlatformAuthProperties
import nl.teqplay.aisengine.platform.auth.service.PlatformKeycloakTokenVerifier
import nl.teqplay.aisengine.platform.auth.service.PlatformTokenRoleVerificationService
import nl.teqplay.aisengine.platform.auth.service.PlatformTokenVerificationService
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@EnableConfigurationProperties(PlatformAuthProperties::class)
class PlatformRoleAuthAutoConfiguration {
    @Bean
    fun service(
        database: MongoDatabase,
        properties: PlatformAuthProperties
    ): PlatformTokenVerificationService {
        val datasource = PlatformUserDatasource(database)
        val tokenVerifier = PlatformKeycloakTokenVerifier(
            realm = properties.keycloakRealm,
            url = properties.keycloakUrl
        )

        return PlatformTokenRoleVerificationService(tokenVerifier, datasource)
    }
}
