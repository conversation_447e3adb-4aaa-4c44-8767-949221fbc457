package nl.teqplay.aisengine.platform.auth.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.platform.auth.model.PlatformUser
import nl.teqplay.skeleton.datasource.kmongo.ensureUniqueIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOne

class PlatformUserDatasource(
    database: MongoDatabase
) {
    private val collection = database.getCollection<PlatformUser>("keycloak_platform_users").apply {
        ensureUniqueIndex(PlatformUser::username)
    }

    fun find(username: String): PlatformUser? {
        return collection.findOne(PlatformUser::username eq username)
    }
}
