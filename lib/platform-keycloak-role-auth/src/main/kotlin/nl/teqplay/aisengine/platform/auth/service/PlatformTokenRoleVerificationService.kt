package nl.teqplay.aisengine.platform.auth.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.platform.auth.datasource.PlatformUserDatasource
import nl.teqplay.skeleton.auth.credentials.services.TokenVerificationService.TokenVerificationException
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.Authentication
import org.springframework.security.core.authority.SimpleGrantedAuthority

class PlatformTokenRoleVerificationService(
    tokenVerifier: PlatformKeycloakTokenVerifier,
    private val datasource: PlatformUserDatasource
) : PlatformTokenVerificationService(tokenVerifier) {
    private val LOG = KotlinLogging.logger {}

    override fun authenticate(token: String): Authentication {
        val username = verifyToken(token)
        val user = username?.let(datasource::find)

        if (user != null) {
            LOG.debug { "Authenticated $user" }
            return UsernamePasswordAuthenticationToken(user, token, user.roles.map(::SimpleGrantedAuthority))
        } else {
            throw TokenVerificationException("Error: username $username not found")
        }
    }
}
