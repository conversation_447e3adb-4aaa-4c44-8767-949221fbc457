plugins {
    id("ais-engine.kotlin-lib-conventions")
    id("ais-engine.kotlin-kapt-conventions")
}

dependencies {
    implementation("nl.teqplay.skeleton:metrics:$skeletonVersion")

    api("nl.teqplay.skeleton:datasource2:$skeletonVersion")
    api("nl.teqplay.skeleton:models:$skeletonVersion")
    api("nl.teqplay.skeleton:util-location2:$skeletonVersion")

    api("com.amazonaws:aws-java-sdk-s3:$awsVersion")
    api("javax.xml.bind:jaxb-api:$jaxbVersion")
    implementation("com.google.guava:guava:$guavaVersion")
    implementation("com.antkorwin:xsync:$xsyncVersion")

    testImplementation("org.awaitility:awaitility:$awaitilityVersion")
    testImplementation("org.awaitility:awaitility-kotlin:$awaitilityVersion")
}
