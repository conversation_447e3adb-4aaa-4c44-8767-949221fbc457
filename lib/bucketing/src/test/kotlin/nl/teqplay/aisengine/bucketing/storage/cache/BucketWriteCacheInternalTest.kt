package nl.teqplay.aisengine.bucketing.storage.cache

import nl.teqplay.aisengine.bucketing.B
import nl.teqplay.aisengine.bucketing.R
import nl.teqplay.aisengine.bucketing.T
import nl.teqplay.aisengine.bucketing.bucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveWriteStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketWriteStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketWriteStorage
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

internal class BucketWriteCacheInternalTest {

    private val mongoStorage = mock<MongoBucketWriteStorage<T, T>>()
    private val unorderedStorage = mock<UnorderedMongoBucketWriteStorage<T, T, T>>()
    private val archiveStorage = mock<ArchiveWriteStorage<T>>()
    private val formatter = DefaultBucketFormatter<T>()
    private val factory = mock<BucketFactory<T, Int, BucketId<Int>>>().apply {
        whenever(resolver).thenReturn(R)
        whenever(bucketIdentifier).thenReturn(B)
        whenever(bucketFormatter).thenReturn(formatter)
    }

    private val bucketWriteCache = object : BucketWriteCacheInternal<T, T, T, T, Int, BucketId<Int>>(
        collectionName = "int",
        bucket = bucketProperties,
        factory = factory,
        mongoStorage = mongoStorage,
        unorderedStorage = unorderedStorage,
        archiveStorage = archiveStorage,
        overwriteMaxAge = null,
        bucketingMetricRegistry = mock(),
    ) {}

    @Test
    fun insert() {
        doNothing().whenever(unorderedStorage).insert(any(), any())
        bucketWriteCache.insert(T(0))
        verify(unorderedStorage, times(1)).insert(any(), any())
    }

    @Test
    fun flushUnorderedBucketsFromMemory() {
        doNothing().whenever(unorderedStorage).writeUnorderedBuckets()
        bucketWriteCache.flushUnorderedBucketsFromMemory()
        verify(unorderedStorage, times(1)).writeUnorderedBuckets()
    }

    @Test
    fun shutdown() {
        whenever(unorderedStorage.findAllArchiveBucketIds(any()))
            .thenReturn(listOf("1970-01-01,0"))
        whenever(unorderedStorage.lock(any(), any()))
            .thenAnswer { (it.arguments[1] as Runnable).run() }
        doNothing().whenever(unorderedStorage).shutdown()

        bucketWriteCache.startup()
        bucketWriteCache.shutdown()
        verify(unorderedStorage, times(1)).shutdown()
    }
}
