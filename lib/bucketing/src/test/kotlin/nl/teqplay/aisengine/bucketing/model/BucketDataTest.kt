package nl.teqplay.aisengine.bucketing.model

import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.model.bucket.UnorderedBucket
import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant

class BucketDataTest {

    private fun createOrderedBucket() = OrderedBucket<Timestamp>("id")
    private fun createUnorderedBucket() = UnorderedBucket<Timestamp>("id", "id")

    data class Timestamp(
        val timestamp: Instant
    )

    object TimestampResolver : BucketItemResolver<Timestamp> {
        override fun toTimestamp(item: Timestamp) = item.timestamp
        override fun toId(item: Timestamp) = ""
    }

    private val unsorted = listOf<Long>(0, 2, 1, 5, 4, 3)

    @Test
    fun addOrdered() {
        val bucket = createOrderedBucket()
        unsorted.forEach { bucket.add(Timestamp(Instant.ofEpochMilli(it)), TimestampResolver) }

        assertEquals(
            unsorted.sorted(),
            bucket.getAll().map { it.timestamp.toEpochMilli() }
        )
        assertEquals(unsorted.size, bucket.size())
    }

    @Test
    fun addAllOrdered() {
        val bucket = createOrderedBucket()
        bucket.addAll(unsorted.map { Timestamp(Instant.ofEpochMilli(it)) }, TimestampResolver)

        assertEquals(
            unsorted.sorted(),
            bucket.getAll().map { it.timestamp.toEpochMilli() }
        )
        assertEquals(unsorted.size, bucket.size())
    }

    @Test
    fun addAllOrderedWithDuplicates() {
        val bucket = createOrderedBucket()
        repeat(3) {
            bucket.addAll(unsorted.map { Timestamp(Instant.ofEpochMilli(it)) }, TimestampResolver)
        }

        assertEquals(
            unsorted.sorted(),
            bucket.getAll().map { it.timestamp.toEpochMilli() }
        )
        assertEquals(unsorted.size, bucket.size())
    }

    @Test
    fun addUnordered() {
        val bucket = createUnorderedBucket()
        unsorted.forEach { bucket.add(Timestamp(Instant.ofEpochMilli(it)), TimestampResolver) }

        assertEquals(
            unsorted,
            bucket.getAll().map { it.timestamp.toEpochMilli() }
        )
        assertEquals(unsorted.size, bucket.size())
    }

    @Test
    fun addAllUnordered() {
        val bucket = createUnorderedBucket()
        bucket.addAll(unsorted.map { Timestamp(Instant.ofEpochMilli(it)) }, TimestampResolver)

        assertEquals(
            unsorted,
            bucket.getAll().map { it.timestamp.toEpochMilli() }
        )
        assertEquals(unsorted.size, bucket.size())
    }
}
