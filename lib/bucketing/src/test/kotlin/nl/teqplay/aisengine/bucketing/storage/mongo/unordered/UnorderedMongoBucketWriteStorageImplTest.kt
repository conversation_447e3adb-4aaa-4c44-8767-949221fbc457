package nl.teqplay.aisengine.bucketing.storage.mongo.unordered

import com.mongodb.client.result.DeleteResult
import com.mongodb.client.result.InsertManyResult
import nl.teqplay.aisengine.bucketing.T
import nl.teqplay.aisengine.bucketing.TimestampedInt
import nl.teqplay.aisengine.bucketing.bucketFactory
import nl.teqplay.aisengine.bucketing.bucketProperties
import nl.teqplay.aisengine.bucketing.model.bucket.BufferData
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.model.bucket.UnorderedBucket
import nl.teqplay.aisengine.bucketing.mongoDatabase
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketStorageQuery
import org.awaitility.kotlin.await
import org.awaitility.kotlin.until
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.LocalDate
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.ScheduledFuture

class UnorderedMongoBucketWriteStorageImplTest {

    private val mongoBucketStorageQuery = mock<MongoBucketStorageQuery.Unordered>()
    private val scheduledExecutorService = mock<ScheduledExecutorService>().apply {
        whenever(scheduleAtFixedRate(any(), any(), any(), any())).thenReturn(mock())
    }

    private val mongoBucketWriteStorage = UnorderedMongoBucketWriteStorageImpl(
        mongoBucketStorageQuery = mongoBucketStorageQuery,
        mongoDatabase = mongoDatabase,
        collectionName = "collectionName",
        collectionReadOnly = false,
        unorderedCollectionSuffix = "suffix",
        factory = bucketFactory,
        scheduledExecutorService = scheduledExecutorService,
        flushInitialDelay = Duration.ZERO,
        unordered = bucketProperties.mongo.unordered
    )

    @Test
    fun findAllArchiveBucketIds() {
        val output = listOf("bucketId")
        whenever(mongoBucketStorageQuery.findAllArchiveBucketIdsByDistinct<T, T>(any(), any()))
            .thenReturn(output)
            .thenThrow(Exception())
        whenever(mongoBucketStorageQuery.findAllArchiveBucketIdsByProjection<T, T>(any(), any()))
            .thenReturn(output)

        // calls by distinct
        assertEquals(output, mongoBucketWriteStorage.findAllArchiveBucketIds(LocalDate.EPOCH))
        verify(mongoBucketStorageQuery, times(1)).findAllArchiveBucketIdsByDistinct<T, T>(any(), any())
        verify(mongoBucketStorageQuery, never()).findAllArchiveBucketIdsByProjection<T, T>(any(), any())

        // calls by projection
        assertEquals(output, mongoBucketWriteStorage.findAllArchiveBucketIds(LocalDate.EPOCH))
        verify(mongoBucketStorageQuery, times(2)).findAllArchiveBucketIdsByDistinct<T, T>(any(), any())
        verify(mongoBucketStorageQuery, times(1)).findAllArchiveBucketIdsByProjection<T, T>(any(), any())
    }

    @Test
    fun findBucketIdsForArchive() {
        val output = setOf("bucketId")
        whenever(mongoBucketStorageQuery.findBucketIdsForArchive<T, T>(any(), any())).thenReturn(output)
        assertEquals(output, mongoBucketWriteStorage.findBucketIdsForArchive("archiveBucketId"))
    }

    @Test
    fun `load - no result`() {
        whenever(mongoBucketStorageQuery.getUnorderedBucketsByBucketIds<T, T>(any(), any()))
            .thenReturn(emptyList<UnorderedBucket<T>>().iterator())
        whenever(mongoBucketStorageQuery.getByBucketIdsInBufferStage<T, T>(any(), any()))
            .thenReturn(emptyList<BufferData<T>>().iterator())
        assertNull(mongoBucketWriteStorage.load("bucketId"))
    }

    @Test
    fun load() {
        whenever(mongoBucketStorageQuery.getUnorderedBucketsByBucketIds<T, T>(any(), any())).thenReturn(
            listOf(
                UnorderedBucket("bucketId", "archiveBucketId", mutableListOf(TimestampedInt(0))),
                UnorderedBucket("bucketId", "archiveBucketId", mutableListOf(TimestampedInt(1))),
            ).iterator()
        )
        whenever(mongoBucketStorageQuery.getByBucketIdsInBufferStage<T, T>(any(), any()))
            .thenReturn(
                listOf(
                    BufferData("bucketId", 0, "archiveBucketId", mutableListOf(TimestampedInt(2)))
                ).iterator()
            )

        assertEquals(
            OrderedBucket(
                _id = "bucketId",
                data = mutableListOf(
                    TimestampedInt(1),
                    TimestampedInt(2),
                    TimestampedInt(0)
                )
            ),
            mongoBucketWriteStorage.load("bucketId")
        )
    }

    @Test
    fun `load list - empty`() {
        assertEquals(emptyList<OrderedBucket<T>>().iterator(), mongoBucketWriteStorage.load(emptyList()))
        verify(mongoBucketStorageQuery, never()).getUnorderedBucketsByBucketIds<T, T>(any(), any())
    }

    @Test
    fun insert() {
        whenever(scheduledExecutorService.schedule(any<Runnable>(), any(), any())).thenAnswer {
            it.getArgument<Runnable>(0).run()
            mock<ScheduledFuture<*>>()
        }

        var bulkWrite = false
        whenever(mongoBucketStorageQuery.insertMany<T, T>(any(), any())).thenAnswer {
            bulkWrite = true
            return@thenAnswer InsertManyResult.acknowledged(emptyMap())
        }

        repeat(bucketProperties.mongo.unordered.flush.maxItemsThreshold + 1) {
            mongoBucketWriteStorage.insert("bucketId", TimestampedInt(it))
        }

        await until { bulkWrite }

        verify(mongoBucketStorageQuery, times(1)).insertMany<T, T>(any(), any())
    }

    @Test
    fun `delete - empty`() {
        whenever(mongoBucketStorageQuery.deleteMany<T, T>(any(), any()))
            .thenReturn(DeleteResult.acknowledged(0))
        mongoBucketWriteStorage.delete(emptySet())
        verify(mongoBucketStorageQuery, never()).deleteMany<T, T>(any(), any())
    }

    @Test
    fun delete() {
        whenever(mongoBucketStorageQuery.deleteMany<T, T>(any(), any()))
            .thenReturn(DeleteResult.acknowledged(0))
        mongoBucketWriteStorage.delete(setOf(""))
        verify(mongoBucketStorageQuery, times(1)).deleteMany<T, T>(any(), any())
    }

    @Test
    fun lock() {
        var invoked = false
        val callback = Runnable { invoked = true }

        mongoBucketWriteStorage.lock(emptySet(), callback)
        assertFalse(invoked)

        mongoBucketWriteStorage.lock(setOf(""), callback)
        assertTrue(invoked)
    }

    @Test
    fun shutdown() {
        mongoBucketWriteStorage.shutdown()
    }
}
