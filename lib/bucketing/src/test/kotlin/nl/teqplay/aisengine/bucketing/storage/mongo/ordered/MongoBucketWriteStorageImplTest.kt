package nl.teqplay.aisengine.bucketing.storage.mongo.ordered

import com.mongodb.WriteConcernException
import nl.teqplay.aisengine.bucketing.B
import nl.teqplay.aisengine.bucketing.R
import nl.teqplay.aisengine.bucketing.T
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.mongoDatabase
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketStorageQuery
import nl.teqplay.skeleton.model.TimeWindow
import org.bson.BsonDocument
import org.bson.BsonInt32
import org.bson.BsonMaximumSizeExceededException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.ArgumentMatchers.anyList
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.mock
import org.mockito.kotlin.spy
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Instant
import java.time.LocalDate

class MongoBucketWriteStorageImplTest {

    private val mongoBucketStorageQuery = mock<MongoBucketStorageQuery.Ordered>().apply {
        whenever(getOldestBucketId<T, T>(any())).thenReturn("2000-01-01,0")
    }

    private val spyBucketFormatter = spy(DefaultBucketFormatter<T>())
    private val bucketFactory = object : BucketFactory<T, Int, BucketId<Int>> {
        override val dataClass = T::class.java

        override val resolver = R
        override val bucketIdentifier = B
        override val bucketFormatter = spyBucketFormatter
    }

    private val mongoBucketWriteStorage = MongoBucketWriteStorageImpl(
        mongoBucketStorageQuery = mongoBucketStorageQuery,
        mongoDatabase = mongoDatabase,
        collectionName = "collectionName",
        factory = bucketFactory
    )

    @Test
    fun getOldestDataPoint() {
        assertEquals(
            LocalDate.of(2000, 1, 1),
            mongoBucketWriteStorage.oldestDataPoint
        )
    }

    @Test
    fun findExistingBucketIds() {
        val output = listOf("bucketId").iterator()
        whenever(mongoBucketStorageQuery.findExistingBucketIds<T, T>(any(), any())).thenReturn(output)
        assertEquals(
            output,
            mongoBucketWriteStorage.findExistingBucketIds(TimeWindow(Instant.EPOCH, Instant.EPOCH))
        )
    }

    @Test
    fun load() {
        val output = OrderedBucket<T>("_id")
        whenever(mongoBucketStorageQuery.load<T, T>(any(), anyString())).thenReturn(output)
        assertEquals(
            output,
            mongoBucketWriteStorage.load("bucketId")
        )
    }

    @Test
    fun `load list - empty`() {
        assertEquals(
            emptyList<OrderedBucket<T>>().iterator(),
            mongoBucketWriteStorage.load(emptyList())
        )
    }

    @Test
    fun `load list`() {
        val output = listOf(OrderedBucket<T>("_id")).iterator()
        whenever(mongoBucketStorageQuery.load<T, T>(any(), anyList())).thenReturn(output)
        assertEquals(
            output,
            mongoBucketWriteStorage.load(listOf("bucketId"))
        )
    }

    @Test
    fun save() {
        doNothing().whenever(mongoBucketStorageQuery).save<T, T>(any(), any())
        mongoBucketWriteStorage.save(OrderedBucket("_id"))
        verify(mongoBucketStorageQuery, times(1)).save<T, T>(any(), any())
    }

    @Test
    fun `save - thinned out`() {
        whenever(mongoBucketStorageQuery.save<T, T>(any(), any()))
            .thenThrow(BsonMaximumSizeExceededException("test"))
            .then { }

        mongoBucketWriteStorage.save(OrderedBucket("_id"))

        verify(spyBucketFormatter, times(1)).unzip(any())
        verify(spyBucketFormatter, times(1)).zip(any())

        verify(mongoBucketStorageQuery, times(2)).save<T, T>(any(), any())
    }

    @Test
    fun `save - thinning out exceeded`() {
        whenever(mongoBucketStorageQuery.save<T, T>(any(), any()))
            .thenThrow(BsonMaximumSizeExceededException("test"))

        assertThrows<Exception> {
            mongoBucketWriteStorage.save(OrderedBucket("_id"))
        }
    }

    @Test
    fun `save - thinning out errors`() {
        whenever(mongoBucketStorageQuery.save<T, T>(any(), any()))
            .thenThrow(BsonMaximumSizeExceededException("test"))
            .thenThrow(WriteConcernException(BsonDocument("code", BsonInt32(17420)), mock(), mock()))
            .thenThrow(WriteConcernException(BsonDocument("code", BsonInt32(-1)), mock(), mock()))

        assertThrows<WriteConcernException> {
            mongoBucketWriteStorage.save(OrderedBucket("_id"))
        }
    }

    @Test
    fun deleteBucketsBefore() {
        doNothing().whenever(mongoBucketStorageQuery).deleteBucketsBefore<T, T>(any(), any())
        mongoBucketWriteStorage.deleteBucketsBefore(LocalDate.EPOCH)
        verify(mongoBucketStorageQuery, times(1)).deleteBucketsBefore<T, T>(any(), any())
    }
}
