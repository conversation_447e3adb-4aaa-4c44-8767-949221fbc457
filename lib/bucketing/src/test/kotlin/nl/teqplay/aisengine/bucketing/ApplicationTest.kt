package nl.teqplay.aisengine.bucketing

import com.mongodb.kotlin.client.MongoClient
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.common.BaseTest
import org.junit.jupiter.api.Test
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.bean.override.mockito.MockitoBean

@SpringBootApplication
class Application

@SpringBootTest
@MockitoBean(
    types = [MongoClient::class, MongoDatabase::class]
)
class ApplicationTest : BaseTest() {

    @Test
    fun contextLoads() {
    }
}
