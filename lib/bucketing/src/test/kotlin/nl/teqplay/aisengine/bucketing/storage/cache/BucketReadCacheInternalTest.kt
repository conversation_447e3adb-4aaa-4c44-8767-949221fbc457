package nl.teqplay.aisengine.bucketing.storage.cache

import nl.teqplay.aisengine.bucketing.B
import nl.teqplay.aisengine.bucketing.R
import nl.teqplay.aisengine.bucketing.T
import nl.teqplay.aisengine.bucketing.bucketProperties
import nl.teqplay.aisengine.bucketing.model.bucket.DatedOrderedBucket
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketReadStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketReadStorage
import nl.teqplay.aisengine.util.floorToLocalDate
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyList
import org.mockito.kotlin.any
import org.mockito.kotlin.clearInvocations
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.spy
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

internal class BucketReadCacheInternalTest {

    private val mongoStorage = mock<MongoBucketReadStorage<T, T>>()
    private val unorderedStorage = mock<UnorderedMongoBucketReadStorage<T, T, T>>()
    private val archiveStorage = mock<ArchiveReadStorage<T>>()
    private val formatter = spy(DefaultBucketFormatter<T>())
    private val factory = mock<BucketFactory<T, Int, BucketId<Int>>>().apply {
        whenever(resolver).thenReturn(R)
        whenever(bucketIdentifier).thenReturn(B)
        whenever(bucketFormatter).thenReturn(formatter)
    }
    private val clock = mock<Clock>()

    private val bucketReadCache = object : BucketReadCacheInternal<T, T, T, T, Int, BucketId<Int>>(
        collectionName = "int",
        bucket = bucketProperties,
        factory = factory,
        mongoStorage = mongoStorage,
        unorderedStorage = unorderedStorage,
        archiveStorage = archiveStorage,
        clock = clock,
        overwriteMaxAge = null
    ) {}

    private val value1 = T(0, Instant.EPOCH)
    private val value2 = T(0, Instant.EPOCH.plusSeconds(30))

    @Test
    fun testBucketTimeInDaysEquals1() {
        assertEquals(1, BucketReadCacheInternal.BUCKET_TIME_SIZE_IN_DAYS)
    }

    @Test
    fun `fetchHistoryInTimeWindow - multiple keys`() {
        clearInvocations(formatter)
        whenever(mongoStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value1))).iterator())
        whenever(unorderedStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value2))).iterator())

        val result = bucketReadCache.fetchHistoryInTimeWindow(
            window = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            extendToLongTerm = false,
            sort = false,
            keys = setOf(0)
        ).toList()

        assertEquals(listOf(value1, value2), result)
        verify(formatter, times(1)).unzip(any())
        verify(formatter, never()).unzipAsSequence(any())
    }

    @Test
    fun `fetchHistoryInTimeWindow - multiple keys - sorted`() {
        clearInvocations(formatter)
        whenever(mongoStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value1))).iterator())
        whenever(unorderedStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-02,0", mutableListOf(value2))).iterator())

        val result = bucketReadCache.fetchHistoryInTimeWindow(
            window = TimeWindow(Instant.EPOCH, Duration.ofDays(2)),
            extendToLongTerm = false,
            sort = true,
            keys = setOf(0)
        ).toList()

        assertEquals(listOf(value1, value2), result)
        verify(formatter, never()).unzip(any())
        verify(formatter, times(1)).unzipAsSequence(any())
    }

    @Test
    fun `fetchHistoryInTimeWindow - use history edge`() {
        clearInvocations(formatter)
        whenever(mongoStorage.oldestDataPoint).thenReturn(Instant.EPOCH.floorToLocalDate())
        whenever(mongoStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value1))).iterator())
        whenever(unorderedStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value2))).iterator())
        whenever(clock.instant()).thenReturn(Instant.EPOCH)

        val result = bucketReadCache.fetchHistoryInTimeWindow(
            window = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            key = 0,
            extendToLongTerm = true
        ).toList()

        assertEquals(listOf(value1, value2), result)
        verify(formatter, times(1)).unzip(any())
        verify(formatter, never()).unzipAsSequence(any())
    }

    @Test
    fun `fetchHistoryInTimeWindow - only short-term`() {
        clearInvocations(formatter)
        whenever(mongoStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value1))).iterator())
        whenever(unorderedStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value2))).iterator())

        val result = bucketReadCache.fetchHistoryInTimeWindow(
            window = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            key = 0,
            extendToLongTerm = false
        ).toList()

        assertEquals(listOf(value1, value2), result)
        verify(formatter, times(1)).unzip(any())
        verify(formatter, never()).unzipAsSequence(any())
    }

    @Test
    fun `fetchHistoryInTimeWindow - only short-term - extendToLongTerm=true`() {
        clearInvocations(formatter)
        whenever(mongoStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value1))).iterator())
        whenever(unorderedStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value2))).iterator())
        whenever(clock.instant()).thenReturn(Instant.EPOCH)

        val result = bucketReadCache.fetchHistoryInTimeWindow(
            window = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            key = 0,
            extendToLongTerm = true
        ).toList()

        assertEquals(listOf(value1, value2), result)
        verify(formatter, times(1)).unzip(any())
        verify(formatter, never()).unzipAsSequence(any())
    }

    @Test
    fun `fetchHistoryInTimeWindow - only long-term`() {
        clearInvocations(formatter)
        whenever(archiveStorage.getArchiveKey(any())).thenReturn("")
        whenever(archiveStorage.get(any(), any()))
            .thenReturn(sequenceOf(DatedOrderedBucket("", value1.timestamp.floorToLocalDate(), mutableListOf(value1))))
        whenever(mongoStorage.load(anyList())).thenReturn(emptyList<OrderedBucket<T>>().iterator())
        whenever(unorderedStorage.load(anyList())).thenReturn(emptyList<OrderedBucket<T>>().iterator())

        whenever(clock.instant()).thenReturn(Instant.EPOCH.plus(8, ChronoUnit.DAYS))

        val result = bucketReadCache.fetchHistoryInTimeWindow(
            window = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            key = 0,
            extendToLongTerm = true
        ).toList()

        assertEquals(listOf(value1), result)
        verify(formatter, times(1)).unzip(any())
        verify(formatter, never()).unzipAsSequence(any())
    }

    @Test
    fun `fetchHistoryInTimeWindow - both short-term and long-term`() {
        clearInvocations(formatter)
        val value2Copy = value2.copy(timestamp = Instant.EPOCH.plus(1, ChronoUnit.DAYS))

        whenever(archiveStorage.getArchiveKey(any())).thenReturn("")
        whenever(archiveStorage.get(any(), any()))
            .thenReturn(sequenceOf(DatedOrderedBucket("", value1.timestamp.floorToLocalDate(), mutableListOf(value1))))
        whenever(mongoStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-02,0", mutableListOf(value2Copy))).iterator())
        whenever(unorderedStorage.load(anyList())).thenReturn(emptyList<OrderedBucket<T>>().iterator())

        whenever(clock.instant()).thenReturn(Instant.EPOCH.plus(8, ChronoUnit.DAYS))

        val result = bucketReadCache.fetchHistoryInTimeWindow(
            window = TimeWindow(Instant.EPOCH, Duration.ofDays(2)),
            key = 0,
            extendToLongTerm = true
        ).toList()

        assertEquals(listOf(value1, value2Copy), result)
        verify(formatter, times(2)).unzip(any())
        verify(formatter, never()).unzipAsSequence(any())
    }
}
