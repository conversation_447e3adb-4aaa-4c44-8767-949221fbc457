package nl.teqplay.aisengine.bucketing.storage.archive

import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.AmazonS3Exception
import com.amazonaws.services.s3.model.S3Object
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import nl.teqplay.aisengine.bucketing.T
import nl.teqplay.aisengine.bucketing.bucketFactory
import nl.teqplay.aisengine.bucketing.model.bucket.DatedOrderedBucket
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

internal class ArchiveStorageTest {

    private val amazonS3 = mock<AmazonS3>()
    private val archiveProperties = BucketArchiveProperties()
    private val archiveClientService = mock<ArchiveClientService>().apply {
        whenever(archive).thenReturn(archiveProperties)
        whenever(s3Client).thenReturn(amazonS3)
    }

    private val objectMapper = jacksonObjectMapper()
        .registerKotlinModule()
        .registerModule(JavaTimeModule())

    private val archiveBucketId = "${LocalDate.EPOCH},archiveBucketId"
    private val archiveKey = "string,1970-01-01,archiveBucketId.zip"
    private val data = listOf(1, 2, 3, 4, 5).map { T(it) }

    private val archiveReadStorage = ArchiveReadStorageImpl(
        dataClass = T::class.java,
        archive = archiveProperties,
        objectMapper = objectMapper,
        archiveClientService = archiveClientService
    )

    private val archiveWriteStorage = ArchiveWriteStorageImpl(
        archive = archiveProperties,
        objectMapper = objectMapper,
        factory = bucketFactory,
        archiveClientService = archiveClientService
    )

    private val window = TimeWindow(
        from = Instant.EPOCH,
        to = Instant.EPOCH.plus(3, ChronoUnit.DAYS)
    )

    private fun createInputStream(): ByteArrayInputStream {
        val outputStream = ByteArrayOutputStream()
        val zipOutputStream = ZipOutputStream(outputStream)
        zipOutputStream.putNextEntry(ZipEntry(ArchiveStorage.ARCHIVE_FILE_NAME))
        objectMapper.writeValue(zipOutputStream, data)

        val bytes = outputStream.toByteArray()
        return ByteArrayInputStream(bytes)
    }

    @Test
    fun getArchiveKey() {
        assertEquals(archiveKey, archiveReadStorage.getArchiveKey(archiveBucketId))
    }

    @Test
    fun getArchiveDate() {
        assertEquals(LocalDate.EPOCH, archiveReadStorage.getArchiveDate(archiveBucketId))
    }

    @Test
    fun get() {
        val s3Object = S3Object()
        s3Object.setObjectContent(createInputStream())
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenReturn(s3Object)

        val result = archiveReadStorage.get(window, listOf(archiveBucketId).iterator())

        assertEquals(
            listOf(
                DatedOrderedBucket(
                    _id = "${LocalDate.EPOCH},archiveBucketId",
                    date = LocalDate.EPOCH,
                    data = data.toMutableList()
                ),
            ),
            result.toList()
        )
    }

    @Test
    fun `get - throws NoSuchKey`() {
        val exception = AmazonS3Exception("NoSuchKey")
        exception.errorCode = "NoSuchKey"
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenThrow(exception)

        val result = archiveReadStorage.get(window, listOf(archiveBucketId).iterator()).toList()

        assertTrue(result.isEmpty())
    }

    @Test
    fun `get - throws error`() {
        val exception = AmazonS3Exception("other")
        exception.errorCode = "other"
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenThrow(exception)

        assertThrows<AmazonS3Exception> { archiveReadStorage.get(window, listOf(archiveBucketId).iterator()).toList() }
    }

    @Test
    fun `addAll - no pre-existing data`() {
        val exception = AmazonS3Exception("NoSuchKey")
        exception.errorCode = "NoSuchKey"
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenThrow(exception)

        val archiveKey = archiveReadStorage.getArchiveKey(archiveBucketId)
        val result = archiveWriteStorage.addAll(archiveKey, LocalDate.EPOCH, data)
        assertTrue(result)
        verify(archiveClientService.s3Client, times(1)).putObject(any(), any(), any(), any())
    }

    @Test
    fun `addAll - has pre-existing data`() {
        val s3Object = S3Object()
        s3Object.setObjectContent(createInputStream())
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenReturn(s3Object)

        val archiveKey = archiveReadStorage.getArchiveKey(archiveBucketId)
        val result = archiveWriteStorage.addAll(archiveKey, LocalDate.EPOCH, data)
        assertTrue(result)
        verify(archiveClientService.s3Client, times(1)).putObject(any(), any(), any(), any())
    }

    @Test
    fun `addAll - throws error`() {
        val s3Object = S3Object()
        s3Object.setObjectContent(createInputStream())
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenReturn(s3Object)

        whenever(archiveClientService.s3Client.putObject(any(), any(), any(), any())).thenThrow(AmazonS3Exception(""))

        val archiveKey = archiveReadStorage.getArchiveKey(archiveBucketId)
        val result = archiveWriteStorage.addAll(archiveKey, LocalDate.EPOCH, data)
        assertFalse(result)
    }
}
