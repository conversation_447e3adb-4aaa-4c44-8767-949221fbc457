package nl.teqplay.aisengine.bucketing.storage.cache

import nl.teqplay.aisengine.bucketing.B
import nl.teqplay.aisengine.bucketing.R
import nl.teqplay.aisengine.bucketing.T
import nl.teqplay.aisengine.bucketing.bucketPropertiesScheduleImmediately
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveWriteStorage
import nl.teqplay.aisengine.bucketing.storage.archive.BucketArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketWriteStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketWriteStorage
import org.awaitility.kotlin.await
import org.awaitility.kotlin.until
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.any
import org.mockito.kotlin.clearInvocations
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.LocalDate
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class BucketWriteCacheInternalSchedulingTest {

    private val mongoStorage = mock<MongoBucketWriteStorage<T, T>>()
    private val unorderedStorage = mock<UnorderedMongoBucketWriteStorage<T, T, T>>()
    private val archiveStorage = mock<ArchiveWriteStorage<T>>().apply {
        whenever(archive).thenReturn(BucketArchiveProperties(writeNotBefore = LocalDate.EPOCH))
    }
    private val formatter = DefaultBucketFormatter<T>()
    private val factory = mock<BucketFactory<T, Int, BucketId<Int>>>().apply {
        whenever(resolver).thenReturn(R)
        whenever(bucketIdentifier).thenReturn(B)
        whenever(bucketFormatter).thenReturn(formatter)
    }

    @Test
    fun `startup - nothing to flush`() {
        clearInvocations(mongoStorage, unorderedStorage)

        var sleep = false

        doNothing().whenever(mongoStorage).deleteBucketsBefore(any())
        whenever(unorderedStorage.findAllArchiveBucketIds(any())).thenAnswer {
            sleep = true
            return@thenAnswer emptyList<String>()
        }

        // initialize object, which will immediately call the scheduler
        val bucketWriteCache = object : BucketWriteCacheInternal<T, T, T, T, Int, BucketId<Int>>(
            collectionName = "int",
            bucket = bucketPropertiesScheduleImmediately,
            factory = factory,
            mongoStorage = mongoStorage,
            unorderedStorage = unorderedStorage,
            archiveStorage = archiveStorage,
            overwriteMaxAge = null,
            bucketingMetricRegistry = mock(),
        ) {}
        bucketWriteCache.startup()

        await until { sleep }

        verify(mongoStorage, times(1)).deleteBucketsBefore(any())
        verify(unorderedStorage, times(1)).findAllArchiveBucketIds(any())
    }

    @Test
    fun `startup - nothing to flush - don't delete`() {
        clearInvocations(mongoStorage, unorderedStorage)

        var sleep = false

        doNothing().whenever(mongoStorage).deleteBucketsBefore(any())
        whenever(unorderedStorage.findAllArchiveBucketIds(any())).thenAnswer {
            sleep = true
            return@thenAnswer emptyList<String>()
        }

        // initialize object, which will immediately call the scheduler
        val bucketWriteCache = object : BucketWriteCacheInternal<T, T, T, T, Int, BucketId<Int>>(
            collectionName = "int",
            bucket = bucketPropertiesScheduleImmediately.copy(
                sweeper = BucketProperties.Sweeper(maxAge = null)
            ),
            factory = factory,
            mongoStorage = mongoStorage,
            unorderedStorage = unorderedStorage,
            archiveStorage = archiveStorage,
            overwriteMaxAge = null,
            bucketingMetricRegistry = mock(),
        ) {}
        bucketWriteCache.startup()

        await until { sleep }

        verify(mongoStorage, never()).deleteBucketsBefore(any())
        verify(unorderedStorage, times(1)).findAllArchiveBucketIds(any())
    }

    private fun archiveTestData() = Stream.of(
        Arguments.of(LocalDate.EPOCH, true),
        Arguments.of(LocalDate.EPOCH.minusDays(1), false),
        Arguments.of(LocalDate.EPOCH.plusDays(1), true),
    )

    @ParameterizedTest
    @MethodSource("archiveTestData")
    fun startup(date: LocalDate, writeToArchive: Boolean) {
        clearInvocations(mongoStorage, unorderedStorage, archiveStorage)

        var sleep = false

        whenever(archiveStorage.getArchiveDate(any())).thenReturn(date)
        doNothing().whenever(mongoStorage).deleteBucketsBefore(any())
        whenever(unorderedStorage.findAllArchiveBucketIds(any()))
            .thenReturn(listOf("archiveBucketId"))
            .thenAnswer {
                sleep = true
                return@thenAnswer emptyList<String>()
            }

        whenever(unorderedStorage.findBucketIdsForArchive(any())).thenReturn(setOf("bucketId"))
        whenever(unorderedStorage.lock(any(), any())).then {
            it.getArgument<Runnable>(1).run()
        }

        whenever(unorderedStorage.load(anyString())).thenReturn(OrderedBucket("bucketId"))
        whenever(mongoStorage.load(anyString())).thenReturn(OrderedBucket("bucketId"))
        doNothing().whenever(mongoStorage).save(any())
        doNothing().whenever(unorderedStorage).delete(any())

        whenever(archiveStorage.getArchiveKey(any())).thenReturn("archiveKey")
        whenever(archiveStorage.addAll(any(), any(), any())).thenReturn(true)

        // initialize object, which will immediately call the scheduler
        val bucketWriteCache = object : BucketWriteCacheInternal<T, T, T, T, Int, BucketId<Int>>(
            collectionName = "int",
            bucket = bucketPropertiesScheduleImmediately,
            factory = factory,
            mongoStorage = mongoStorage,
            unorderedStorage = unorderedStorage,
            archiveStorage = archiveStorage,
            overwriteMaxAge = null,
            bucketingMetricRegistry = mock(),
        ) {}
        bucketWriteCache.startup()

        await until { sleep }

        verify(mongoStorage, times(1)).save(any())
        verify(unorderedStorage, times(1)).delete(any())

        val verification = if (writeToArchive) times(1) else never()
        verify(archiveStorage, verification).addAll(any(), any(), any())
    }

    @ParameterizedTest
    @MethodSource("archiveTestData")
    fun schedule(date: LocalDate, writeToArchive: Boolean) {
        clearInvocations(mongoStorage, unorderedStorage, archiveStorage)

        val bucketIds = listOf("$date,0")

        whenever(archiveStorage.getArchiveDate(any())).thenReturn(date)
        whenever(unorderedStorage.findAllArchiveBucketIds(any()))
            .thenReturn(bucketIds)
        whenever(unorderedStorage.findBucketIdsForArchive(any()))
            .thenReturn(bucketIds.toSet())
        whenever(unorderedStorage.lock(any(), any()))
            .thenAnswer { (it.arguments[1] as Runnable).run() }

        val bucket = OrderedBucket(_id = "", data = mutableListOf(T(0)))

        whenever(unorderedStorage.load(anyString())).thenReturn(bucket)
        whenever(mongoStorage.load(anyString())).thenReturn(bucket)

        var deleted = false
        doNothing().whenever(mongoStorage).save(any())
        whenever(unorderedStorage.delete(any())).thenAnswer {
            deleted = true
            return@thenAnswer Unit
        }

        whenever(archiveStorage.getArchiveKey(any())).thenReturn("archiveKey")
        whenever(archiveStorage.addAll(any(), any(), any())).thenReturn(true)

        // initialize object, which will immediately call the scheduler
        val bucketWriteCache = object : BucketWriteCacheInternal<T, T, T, T, Int, BucketId<Int>>(
            collectionName = "int",
            bucket = bucketPropertiesScheduleImmediately,
            factory = factory,
            mongoStorage = mongoStorage,
            unorderedStorage = unorderedStorage,
            archiveStorage = archiveStorage,
            overwriteMaxAge = null,
            bucketingMetricRegistry = mock(),
        ) {}
        bucketWriteCache.flushArchiveBuckets(bucketIds, Duration.ZERO)

        await until { deleted }

        verify(unorderedStorage, times(1)).load(anyString())
        verify(mongoStorage, times(1)).load(anyString())
        verify(mongoStorage, times(1)).save(any())
        verify(unorderedStorage, times(1)).delete(any())

        val verification = if (writeToArchive) times(1) else never()
        verify(archiveStorage, verification).addAll(any(), any(), any())
    }
}
