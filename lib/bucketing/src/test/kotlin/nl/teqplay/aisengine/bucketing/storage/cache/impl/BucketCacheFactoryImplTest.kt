package nl.teqplay.aisengine.bucketing.storage.cache.impl

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import nl.teqplay.aisengine.bucketing.T
import nl.teqplay.aisengine.bucketing.archiveGlobalProperties
import nl.teqplay.aisengine.bucketing.archiveProperties
import nl.teqplay.aisengine.bucketing.bucketFactory
import nl.teqplay.aisengine.bucketing.bucketProperties
import nl.teqplay.aisengine.bucketing.mongoDatabase
import nl.teqplay.aisengine.bucketing.scheduledExecutorService
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Duration

class BucketCacheFactoryImplTest {

    @Test
    fun bucketReadCacheFactory() {
        val bucketReadCacheFactory = BucketReadCacheFactoryImpl(
            collectionName = "collectionName",
            mongoDatabase = mongoDatabase,
            objectMapper = jacksonObjectMapper(),
            bucket = bucketProperties,
            archive = archiveProperties,
            archiveGlobal = archiveGlobalProperties,
            factory = bucketFactory,
            unorderedCollectionSuffix = "unordered",
            createArchiveReadStorage = mock<CreateArchiveReadStorage<T, Int, BucketId<Int>>>().apply {
                whenever(invoke(any(), any(), any(), any())).thenReturn(mock())
            }
        )

        assertNotNull(bucketReadCacheFactory)

        assertNotNull(bucketReadCacheFactory.mongoStorage())
        assertNotNull(bucketReadCacheFactory.unorderedStorage())
        assertNotNull(bucketReadCacheFactory.archiveStorage())
    }

    @Test
    fun bucketWriteCacheFactory() {
        val bucketWriteCacheFactory = BucketWriteCacheFactoryImpl(
            collectionName = "collectionName",
            mongoDatabase = mongoDatabase,
            objectMapper = jacksonObjectMapper(),
            bucket = bucketProperties,
            archive = archiveProperties,
            archiveGlobal = archiveGlobalProperties,
            scheduledExecutorService = scheduledExecutorService,
            flushInitialDelay = Duration.ZERO,
            factory = bucketFactory,
            unorderedCollectionSuffix = "unordered",
            createArchiveWriteStorage = mock<CreateArchiveWriteStorage<T, Int, BucketId<Int>>>().apply {
                whenever(invoke(any(), any(), any(), any())).thenReturn(mock())
            }
        )

        assertNotNull(bucketWriteCacheFactory)

        assertNotNull(bucketWriteCacheFactory.mongoStorage())
        assertNotNull(bucketWriteCacheFactory.unorderedStorage())
        assertNotNull(bucketWriteCacheFactory.archiveStorage())
    }
}
