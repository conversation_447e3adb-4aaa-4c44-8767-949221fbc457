package nl.teqplay.aisengine.bucketing.storage.mongo

import com.fasterxml.jackson.databind.type.TypeFactory
import com.mongodb.MongoClientSettings
import nl.teqplay.aisengine.bucketing.T
import nl.teqplay.aisengine.bucketing.TimestampedInt
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.model.bucket.UnorderedBucket
import org.bson.BsonBinaryReader
import org.bson.BsonBinaryWriter
import org.bson.codecs.DecoderContext
import org.bson.codecs.EncoderContext
import org.bson.io.BasicOutputBuffer
import org.bson.io.ByteBufferBsonInput
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class MongoBucketCollectionImplTest {

    @Test
    fun `ordered - encode,decode`() {
        val orderedBucketCodec = BucketCodec(
            bucketClass = OrderedBucket::class.java,
            javaType = TypeFactory.defaultInstance()
                .constructParametricType(OrderedBucket::class.java, T::class.java),
            codecRegistry = MongoClientSettings.getDefaultCodecRegistry()
        )

        val buffer = BasicOutputBuffer(128)
        val writer = BsonBinaryWriter(buffer)

        val bucket = OrderedBucket(
            _id = "_id",
            data = mutableListOf(TimestampedInt(0), TimestampedInt(1), TimestampedInt(2))
        )
        orderedBucketCodec.encode(writer, bucket, EncoderContext.builder().build())

        val reader = BsonBinaryReader(ByteBufferBsonInput(buffer.byteBuffers.first()))
        val res = orderedBucketCodec.decode(reader, DecoderContext.builder().build())

        assertEquals(res, bucket)
        assertEquals(OrderedBucket::class.java, orderedBucketCodec.encoderClass)
    }

    @Test
    fun `unordered - encode,decode`() {
        val unorderedBucketCodec = BucketCodec(
            bucketClass = UnorderedBucket::class.java,
            javaType = TypeFactory.defaultInstance()
                .constructParametricType(UnorderedBucket::class.java, T::class.java),
            codecRegistry = MongoClientSettings.getDefaultCodecRegistry()
        )

        val buffer = BasicOutputBuffer(128)
        val writer = BsonBinaryWriter(buffer)

        val bucket = UnorderedBucket(
            bucketId = "bucketId",
            archiveBucketId = "archiveBucketId",
            data = mutableListOf(TimestampedInt(0), TimestampedInt(1), TimestampedInt(2))
        )
        unorderedBucketCodec.encode(writer, bucket, EncoderContext.builder().build())

        val reader = BsonBinaryReader(ByteBufferBsonInput(buffer.byteBuffers.first()))
        val res = unorderedBucketCodec.decode(reader, DecoderContext.builder().build())

        assertEquals(res, bucket)
        assertEquals(UnorderedBucket::class.java, unorderedBucketCodec.encoderClass)
    }
}
