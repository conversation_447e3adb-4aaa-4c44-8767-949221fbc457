package nl.teqplay.aisengine.bucketing.storage.archive

import com.amazonaws.regions.Regions
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import java.time.LocalDate

data class BucketArchiveProperties(
    override val enabled: Boolean = true,
    override val writeNotBefore: LocalDate? = null,
    override val prefix: String = "string",
    override val name: String = "test",
    override val region: Regions = Regions.EU_WEST_1
) : BucketArchiveConfiguration
