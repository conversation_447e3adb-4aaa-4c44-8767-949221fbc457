package nl.teqplay.aisengine.bucketing.storage.archive

import com.amazonaws.regions.Regions
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties.Credentials
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import java.time.LocalDate

class ArchiveClientServiceTest {

    private val archive = object : BucketArchiveConfiguration {
        override val enabled: Boolean = true
        override val writeNotBefore: LocalDate? = null
        override val prefix: String = "prefix"
        override val name: String = "name"
        override val region: Regions = Regions.EU_WEST_1
    }

    @Test
    fun getS3Client() {
        val service = ArchiveClientService(
            archive = archive,
            archiveGlobal = BucketArchiveGlobalProperties(
                credentials = Credentials(
                    accessKeyId = "access-key-id",
                    secretKey = "secret-key"
                )
            )
        )
        assertNotNull(service.s3Client)
    }

    @Test
    fun `getS3Client - no credentials`() {
        val service = ArchiveClientService(
            archive = archive,
            archiveGlobal = BucketArchiveGlobalProperties()
        )
        assertNotNull(service.s3Client)
    }
}
