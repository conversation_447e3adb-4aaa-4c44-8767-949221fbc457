package nl.teqplay.aisengine.bucketing.storage.bucket.shared

import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant
import java.time.LocalDate

class BucketIdByAreaTest {

    private val date = LocalDate.EPOCH

    @Test
    fun getBucketId() {
        val bucketId = "1970-01-01,4.12,52.34"
        assertEquals(
            bucketId,
            BucketIdByArea.getBucketId(date, Location(52.34, 4.12))
        )

        assertEquals(
            "1970-01-01,4.1,52.3",
            BucketIdByArea.getArchiveBucketId(bucketId)
        )
    }

    private val timeWindow = TimeWindow(Instant.EPOCH, Duration.ofDays(1))

    private val boundingBox = BoundingBox(
        Location(1.20, 3.40),
        Location(1.22, 3.42),
    )

    @Test
    fun getBucketIds() {
        assertEquals(
            listOf(
                "1970-01-01,3.40,1.20",
                "1970-01-01,3.40,1.21",
                "1970-01-01,3.41,1.20",
                "1970-01-01,3.41,1.21",
            ),
            BucketIdByArea.getBucketIds(timeWindow, boundingBox)
                .asSequence().toList()
        )
    }

    @Test
    fun getArchiveBucketIds() {
        val testBoundingBox = BoundingBox(
            Location(1.20, 3.40),
            Location(1.42, 3.62),
        )
        assertEquals(
            listOf(
                "1970-01-01,3.4,1.2",
                "1970-01-01,3.4,1.3",
                "1970-01-01,3.4,1.4",
                "1970-01-01,3.5,1.2",
                "1970-01-01,3.5,1.3",
                "1970-01-01,3.5,1.4",
                "1970-01-01,3.6,1.2",
                "1970-01-01,3.6,1.3",
                "1970-01-01,3.6,1.4",
            ),
            BucketIdByArea.getArchiveBucketIds(timeWindow, testBoundingBox)
                .asSequence().toList()
        )
    }

    @Test
    fun floorToBucketSize() {
        assertEquals(
            Location(3.41, 1.21),
            BucketIdByArea.floorToBucketSize(Location(3.41, 1.21))
        )
        assertEquals(
            Location(3.41, 1.21),
            BucketIdByArea.floorToBucketSize(Location(3.419, 1.219))
        )
        assertEquals(
            Location(0.0, 0.0),
            BucketIdByArea.floorToBucketSize(Location(-0.0, -0.0))
        )
        assertEquals(
            Location(0.0, -180.0),
            BucketIdByArea.floorToBucketSize(Location(0.0, -180.0))
        )
        assertEquals(
            Location(0.0, -179.9),
            BucketIdByArea.floorToBucketSize(Location(0.0, 180.1))
        )
        assertEquals(
            Location(0.0, -180.0),
            BucketIdByArea.floorToBucketSize(Location(0.0, 180.0))
        )
        assertEquals(
            Location(0.0, 179.9),
            BucketIdByArea.floorToBucketSize(Location(0.0, -180.1))
        )
    }

    @Test
    fun ceilToBucketSize() {
        assertEquals(
            Location(3.41, 1.21),
            BucketIdByArea.ceilToBucketSize(Location(3.4, 1.2))
        )
        assertEquals(
            Location(3.42, 1.22),
            BucketIdByArea.ceilToBucketSize(Location(3.41, 1.21))
        )
    }
}
