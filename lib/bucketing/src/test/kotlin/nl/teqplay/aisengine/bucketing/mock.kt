package nl.teqplay.aisengine.bucketing

import com.mongodb.client.model.IndexOptions
import com.mongodb.kotlin.client.AggregateIterable
import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoCursor
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.model.MongoIdAggregate
import org.bson.codecs.configuration.CodecRegistry
import org.bson.conversions.Bson
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.util.concurrent.ScheduledExecutorService

private val codecRegistryMock = mock<CodecRegistry>().apply {
    whenever(get(any<Class<*>>())).thenReturn(mock())
}

private val findIterableMock = mock<FindIterable<*>>().apply {
    whenever(cursor()).thenReturn(mock<MongoCursor<Any>>())
}

val collection = mock<MongoCollection<out Any>>().apply {
    whenever(codecRegistry).thenReturn(codecRegistryMock)
    whenever(withCodecRegistry(any())).thenReturn(this)
    whenever(aggregate(any(), any<Class<MongoIdAggregate>>()))
        .thenReturn(mock<AggregateIterable<MongoIdAggregate>>())
    whenever(find()).thenReturn(findIterableMock)
    whenever(createIndex(any<Bson>(), any<IndexOptions>())).thenReturn("")
}

val mongoDatabase = mock<MongoDatabase>().apply {
    whenever(getCollection(any(), any<Class<*>>())).thenReturn(collection)
}

val scheduledExecutorService = mock<ScheduledExecutorService>().apply {
    whenever(scheduleAtFixedRate(any(), any(), any(), any())).thenReturn(mock())
}
