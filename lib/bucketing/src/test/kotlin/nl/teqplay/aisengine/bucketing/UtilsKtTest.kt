package nl.teqplay.aisengine.bucketing

import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant

internal class UtilsKtTest {

    private val size = 1000
    private val items = (1..size).toList()
    private val factor = 0.1

    private val output = (1..size).filter { it % 2 != 0 || it > size * factor * 2 }

    @Test
    fun `thinOut - same ID`() {
        val resolver = object : BucketItemResolver<Int> {
            override fun toTimestamp(item: Int) = Instant.ofEpochMilli(item.toLong())
            override fun toId(item: Int) = "0"
        }

        val thinnedOutItems = thinOut(items, factor, resolver)
        assertEquals(output, thinnedOutItems)
    }

    @Test
    fun `thinOut - different ID`() {
        val resolver = object : BucketItemResolver<Int> {
            override fun toTimestamp(item: Int) = Instant.ofEpochMilli(item.toLong())
            override fun toId(item: Int) = item.toString()
        }

        val thinnedOutItems = thinOut(items, factor, resolver)
        assertEquals(output, thinnedOutItems)
    }
}
