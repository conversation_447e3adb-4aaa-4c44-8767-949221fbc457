package nl.teqplay.aisengine.bucketing.storage.bucket

import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId.Companion.ARCHIVE_BUCKET_LOCATION_DECIMALS
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId.Companion.BUCKET_LOCATION_DECIMALS
import nl.teqplay.aisengine.util.floorLatitudeToDecimals
import nl.teqplay.aisengine.util.floorLongitudeToDecimals
import nl.teqplay.aisengine.util.roundToDecimalString
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant
import java.time.LocalDate

internal class AreaBucketIdTest {

    object AreaBucketIdImpl : AreaBucketId<Location> {
        override fun getBucketId(date: LocalDate, key: Location, decimals: Int): String = "$date,${key.lon},${key.lat}"
        override fun getBucketId(date: LocalDate, key: Location): String = TODO()
        override fun getBucketIds(window: TimeWindow, boundingBox: BoundingBox): Iterator<String> = TODO()
        override fun getArchiveBucketIds(window: TimeWindow, boundingBox: BoundingBox): Iterator<String> = TODO()
    }

    @Test
    fun bucketLocationDecimals() {
        assertEquals(2, BUCKET_LOCATION_DECIMALS)
    }

    @Test
    fun archiveBucketLocationDecimals() {
        assertEquals(1, ARCHIVE_BUCKET_LOCATION_DECIMALS)
    }

    @Test
    fun getBucketIds() {
        val bucketIds = AreaBucketIdImpl.getBucketIds(
            window = TimeWindow(Instant.EPOCH, Duration.ofDays(2)),
            boundingBox = BoundingBox(Location(52.0, 4.0), Location(52.2, 4.2)),
            decimals = 1,
            keys = { iterator { yield(it) } }
        ).asSequence().toList()
        assertEquals(
            listOf(
                "1970-01-01,4.0,52.0",
                "1970-01-01,4.0,52.1",
                "1970-01-01,4.1,52.0",
                "1970-01-01,4.1,52.1",
                "1970-01-02,4.0,52.0",
                "1970-01-02,4.0,52.1",
                "1970-01-02,4.1,52.0",
                "1970-01-02,4.1,52.1"
            ),
            bucketIds
        )
    }

    @Test
    fun `getBucketIds - 2,5 days`() {
        val bucketIds = AreaBucketIdImpl.getBucketIds(
            window = TimeWindow(Instant.EPOCH, Duration.ofDays(2).plusHours(12)),
            boundingBox = BoundingBox(Location(52.0, 4.0), Location(52.2, 4.2)),
            decimals = 1,
            keys = { iterator { yield(it) } }
        ).asSequence().toList()
        assertEquals(
            listOf(
                "1970-01-01,4.0,52.0",
                "1970-01-01,4.0,52.1",
                "1970-01-01,4.1,52.0",
                "1970-01-01,4.1,52.1",
                "1970-01-02,4.0,52.0",
                "1970-01-02,4.0,52.1",
                "1970-01-02,4.1,52.0",
                "1970-01-02,4.1,52.1",
                "1970-01-03,4.0,52.0",
                "1970-01-03,4.0,52.1",
                "1970-01-03,4.1,52.0",
                "1970-01-03,4.1,52.1"
            ),
            bucketIds
        )
    }

    @Test
    fun nextDegree() {
        val nextDegree = AreaBucketIdImpl.nextDegree(
            1.0,
            BUCKET_LOCATION_DECIMALS,
            AreaBucketIdImpl.getBucketSize(BUCKET_LOCATION_DECIMALS)
        )
        assertEquals(1.01, nextDegree)
    }

    @Test
    fun testFloorLatitudeToDecimals() {
        val floored = floorLatitudeToDecimals(1.1111, BUCKET_LOCATION_DECIMALS)
        assertEquals(1.11, floored)
    }

    @Test
    fun testFloorLongitudeToDecimals() {
        val floored = floorLongitudeToDecimals(1.1111, BUCKET_LOCATION_DECIMALS)
        assertEquals(1.11, floored)
    }

    @Test
    fun testRoundToString() {
        val roundString = roundToDecimalString(1.1151, BUCKET_LOCATION_DECIMALS)
        assertEquals("1.12", roundString)
    }

    @Test
    fun testRoundToStringTrailingZero() {
        val roundString = roundToDecimalString(1.1, BUCKET_LOCATION_DECIMALS)
        assertEquals("1.10", roundString)
    }
}
