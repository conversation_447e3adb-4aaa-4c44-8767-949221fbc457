package nl.teqplay.aisengine.bucketing.storage.archive

import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.AmazonS3Exception
import com.amazonaws.services.s3.model.PutObjectResult
import com.amazonaws.services.s3.model.S3Object
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import nl.teqplay.aisengine.bucketing.T
import nl.teqplay.aisengine.bucketing.bucketFactory
import nl.teqplay.aisengine.bucketing.model.bucket.DatedOrderedBucket
import nl.teqplay.aisengine.event.interfaces.TeqplayEvent
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import java.util.zip.ZipOutputStream

internal class ArchiveByMonthStorageTest {

    private val amazonS3 = mock<AmazonS3>()
    private val archiveProperties = BucketArchiveProperties()
    private val archiveClientService = mock<ArchiveClientService>().apply {
        whenever(archive).thenReturn(archiveProperties)
        whenever(s3Client).thenReturn(amazonS3)
    }

    private val objectMapper = jacksonObjectMapper()
        .registerKotlinModule()
        .registerModule(JavaTimeModule())
        .disable(JsonGenerator.Feature.AUTO_CLOSE_TARGET)

    private val archiveBucketId = "${LocalDate.EPOCH},archiveBucketId"
    private val archiveKey = "string,1970-01,archiveBucketId.zip"
    private val data1 = listOf(1, 2, 3, 4, 5).map { T(it) }
    private val data2 = data1.map { it.copy(value = it.value + data1.size) }
    private val data3 = data1.map { it.copy(value = it.value + data1.size + data2.size) }

    private val archiveReadStorage = ArchiveReadByMonthStorageImpl(
        dataClass = T::class.java,
        archive = archiveProperties,
        objectMapper = objectMapper,
        archiveClientService = archiveClientService
    )

    private val archiveWriteStorage = ArchiveWriteByMonthStorageImpl(
        archive = archiveProperties,
        objectMapper = objectMapper,
        factory = bucketFactory,
        archiveClientService = archiveClientService
    )

    private val fullWindow = TimeWindow(
        from = Instant.EPOCH,
        to = Instant.EPOCH.plus(3, ChronoUnit.DAYS)
    )

    private val partialWindow = TimeWindow(
        from = Instant.EPOCH.plus(1, ChronoUnit.DAYS),
        to = Instant.EPOCH.plus(2, ChronoUnit.DAYS)
    )

    private fun createInputStream(): ByteArrayInputStream {
        val outputStream = ByteArrayOutputStream()
        val zipOutputStream = ZipOutputStream(outputStream)

        zipOutputStream.putNextEntry(ZipEntry("${LocalDate.EPOCH}.json"))
        objectMapper.writeValue(zipOutputStream, data1)
        zipOutputStream.closeEntry()

        zipOutputStream.putNextEntry(ZipEntry("${LocalDate.EPOCH.plusDays(1)}.json"))
        objectMapper.writeValue(zipOutputStream, data2)
        zipOutputStream.closeEntry()

        zipOutputStream.putNextEntry(ZipEntry("${LocalDate.EPOCH.plusDays(2)}.json"))
        objectMapper.writeValue(zipOutputStream, data3)
        zipOutputStream.closeEntry()

        val bytes = outputStream.toByteArray()
        return ByteArrayInputStream(bytes)
    }

    private fun createInputStreamFromResource(path: String, date: LocalDate): ByteArrayInputStream {
        val outputStream = ByteArrayOutputStream()
        val zipOutputStream = ZipOutputStream(outputStream)

        zipOutputStream.putNextEntry(ZipEntry("$date.json"))
        zipOutputStream.write(javaClass.classLoader.getResource(path)!!.readBytes())
        zipOutputStream.closeEntry()

        val bytes = outputStream.toByteArray()
        return ByteArrayInputStream(bytes)
    }

    @Test
    fun getArchiveKey() {
        assertEquals(archiveKey, archiveReadStorage.getArchiveKey(archiveBucketId))
    }

    @Test
    fun getArchiveDate() {
        assertEquals(LocalDate.EPOCH, archiveReadStorage.getArchiveDate(archiveBucketId))
    }

    @Test
    fun get() {
        val s3Object = S3Object()
        s3Object.setObjectContent(createInputStream())
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenReturn(s3Object)

        val result = archiveReadStorage.get(fullWindow, listOf(archiveBucketId).iterator())

        assertEquals(
            listOf(
                DatedOrderedBucket(
                    _id = "${LocalDate.EPOCH},archiveBucketId",
                    date = LocalDate.EPOCH,
                    data = data1.toMutableList()
                ),
                DatedOrderedBucket(
                    _id = "${LocalDate.EPOCH.plusDays(1)},archiveBucketId",
                    date = LocalDate.EPOCH.plusDays(1),
                    data = data2.toMutableList()
                ),
                DatedOrderedBucket(
                    _id = "${LocalDate.EPOCH.plusDays(2)},archiveBucketId",
                    date = LocalDate.EPOCH.plusDays(2),
                    data = data3.toMutableList()
                ),
            ),
            result.toList()
        )
    }

    @Test
    fun `get - result within window`() {
        val s3Object = S3Object()
        s3Object.setObjectContent(createInputStream())
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenReturn(s3Object)

        val result = archiveReadStorage.get(partialWindow, listOf(archiveBucketId).iterator())

        val output = result.flatMap { it.data }.toList()
        assertEquals(data2, output)
        assertNotEquals(data1, output)
        assertNotEquals(data3, output)
    }

    @Test
    fun `get - throws NoSuchKey`() {
        val exception = AmazonS3Exception("NoSuchKey")
        exception.errorCode = "NoSuchKey"
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenThrow(exception)

        val result = archiveReadStorage.get(fullWindow, listOf(archiveBucketId).iterator()).toList()

        assertTrue(result.isEmpty())
    }

    @Test
    fun `get - throws error`() {
        val exception = AmazonS3Exception("other")
        exception.errorCode = "other"
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenThrow(exception)

        assertThrows<AmazonS3Exception> {
            archiveReadStorage.get(fullWindow, listOf(archiveBucketId).iterator()).toList()
        }
    }

    @Test
    fun `get - filters duplicate ids`() {
        val s3Object = S3Object()
        s3Object.setObjectContent(createInputStreamFromResource("./duplicate-events-bucket.json", LocalDate.of(2024, 11, 9)))
        whenever(amazonS3.getObject(anyString(), any())).thenReturn(s3Object)

        val archiveEventReadStorage = ArchiveReadByMonthStorageImpl(
            dataClass = TeqplayEvent::class.java,
            archive = archiveProperties,
            objectMapper = objectMapper,
            archiveClientService = archiveClientService
        )

        val result = archiveEventReadStorage.get(
            TimeWindow(
                Instant.parse("2024-11-09T00:00:00Z"),
                Instant.parse("2024-11-10T00:00:00Z"),
            ),
            listOf(
                "2024-11-09,archiveBucketId"
            ).iterator()
        )

        val output = result.flatMap { it.data }.toList()
        val ids = output.map { it._id }
        val uniqueIds = ids.toSet()
        assertEquals(uniqueIds.size, ids.size, "Duplicate ids")
    }

    @Test
    fun `addAll - no pre-existing data`() {
        val exception = AmazonS3Exception("NoSuchKey")
        exception.errorCode = "NoSuchKey"
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenThrow(exception)

        val archiveKey = archiveReadStorage.getArchiveKey(archiveBucketId)
        val result = archiveWriteStorage.addAll(archiveKey, LocalDate.EPOCH, data1)
        assertTrue(result)
        verify(archiveClientService.s3Client, times(1)).putObject(any(), any(), any(), any())
    }

    @Test
    fun `addAll - has pre-existing data`() {
        var inputStream: ByteArrayInputStream? = null

        val s3Object = S3Object()
        s3Object.setObjectContent(createInputStream())
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenReturn(s3Object)
        whenever(archiveClientService.s3Client.putObject(any(), any(), any<ByteArrayInputStream>(), any()))
            .thenAnswer {
                inputStream = it.getArgument(2)
                return@thenAnswer mock<PutObjectResult>()
            }

        val testItem = T(-10, timestamp = Instant.EPOCH.plusSeconds(1))
        val archiveKey = archiveReadStorage.getArchiveKey(archiveBucketId)
        val result = archiveWriteStorage.addAll(archiveKey, LocalDate.EPOCH.plusDays(1), listOf(testItem))
        assertTrue(result)
        verify(archiveClientService.s3Client, times(1)).putObject(any(), any(), any(), any())

        // from here, assert that the ZIP actually contains the correct data
        assertNotNull(inputStream)

        val actualDataInZip = mutableMapOf<LocalDate, MutableList<T>>()
        val zipInputStream = ZipInputStream(inputStream!!)
        do {
            val entry = zipInputStream.nextEntry
            if (entry != null) {
                val date = LocalDate.parse(entry.name.removeSuffix(".json"))
                val content = zipInputStream.readAllBytes()
                val iterator = objectMapper.readerFor(T::class.java).readValues<T>(content)
                actualDataInZip.getOrPut(date, ::mutableListOf).addAll(iterator.readAll())
            }
        } while (entry != null)
        zipInputStream.close()

        assertEquals(
            mapOf(
                LocalDate.EPOCH to data1,
                LocalDate.EPOCH.plusDays(1) to data2 + testItem,
                LocalDate.EPOCH.plusDays(2) to data3
            ),
            actualDataInZip
        )
    }

    @Test
    fun `addAll - throws error`() {
        val s3Object = S3Object()
        s3Object.setObjectContent(createInputStream())
        whenever(amazonS3.getObject(anyString(), eq(archiveKey))).thenReturn(s3Object)

        whenever(archiveClientService.s3Client.putObject(any(), any(), any(), any())).thenThrow(AmazonS3Exception(""))

        val archiveKey = archiveReadStorage.getArchiveKey(archiveBucketId)
        val result = archiveWriteStorage.addAll(archiveKey, LocalDate.EPOCH, data1)
        assertFalse(result)
    }
}
