package nl.teqplay.aisengine.bucketing.storage.cache

import nl.teqplay.aisengine.bucketing.bucketProperties
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.BucketFormatter
import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifier
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketKey
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketReadStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketReadStorage
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyList
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.LocalDate

internal class BucketReadCacheInternalFormatterTest {

    data class TimestampedInt(
        val value: Int,
        val timestamp: Instant = Instant.EPOCH,

        val source: String?,
    )

    object TimestampedIntResolver : BucketItemResolver<TimestampedInt> {
        override fun toTimestamp(item: TimestampedInt) = item.timestamp
        override fun toId(item: TimestampedInt) = item.value.toString()
    }

    object TimestampedIntBucketIdentifier : BucketIdentifier<TimestampedInt, Int, BucketId<Int>> {
        override val id = object : BucketId<Int> {
            override fun getBucketId(date: LocalDate, key: Int): String = "$date,$key"
        }
        override val key = object : BucketKey<TimestampedInt, Int> {
            override fun getBucketKey(item: TimestampedInt): Int = item.value
        }
        override val archive = defaultArchive()
    }

    object TimestampedIntBucketFormatter : BucketFormatter<TimestampedInt, TimestampedInt, TimestampedInt> {
        override fun convertToOutput(item: TimestampedInt): TimestampedInt = item

        override fun initForZip(data: List<TimestampedInt>): List<TimestampedInt> {
            return data.map {
                it.copy(source = it.source ?: "")
            }
        }

        override fun zip(data: List<TimestampedInt>): List<TimestampedInt> {
            throw Exception("Must not be called during this test")
        }

        override fun unzip(data: List<TimestampedInt>): List<TimestampedInt> {
            var previous: TimestampedInt? = null
            return data.map {
                var res = it
                if (it.source == null) res = res.copy(source = previous?.source)
                else if (it.source == "") res = res.copy(source = null)
                previous = it
                return@map res
            }
        }
    }

    private val mongoStorage = mock<MongoBucketReadStorage<TimestampedInt, TimestampedInt>>()
    private val unorderedStorage =
        mock<UnorderedMongoBucketReadStorage<TimestampedInt, TimestampedInt, TimestampedInt>>()
    private val archiveStorage = mock<ArchiveReadStorage<TimestampedInt>>()
    private val factory = mock<BucketFactory<TimestampedInt, Int, BucketId<Int>>>().apply {
        whenever(resolver).thenReturn(TimestampedIntResolver)
        whenever(bucketIdentifier).thenReturn(TimestampedIntBucketIdentifier)
        whenever(bucketFormatter).thenReturn(TimestampedIntBucketFormatter)
    }
    private val clock = mock<Clock>()

    private val bucketReadCache = object :
        BucketReadCacheInternal<TimestampedInt, TimestampedInt, TimestampedInt, TimestampedInt, Int, BucketId<Int>>(
            collectionName = "int",
            bucket = bucketProperties,
            factory = factory,
            mongoStorage = mongoStorage,
            unorderedStorage = unorderedStorage,
            archiveStorage = archiveStorage,
            clock = clock,
            overwriteMaxAge = null
        ) {}

    private val value1 = TimestampedInt(
        value = 0,
        timestamp = Instant.EPOCH,
        source = "source"
    )
    private val value2 = TimestampedInt(
        value = 0,
        timestamp = Instant.EPOCH.plusSeconds(30),
        source = null
    )

    @Test
    fun `fetchHistoryInTimeWindow - only unzip ordered data`() {
        whenever(mongoStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value1))).iterator())
        whenever(unorderedStorage.load(anyList()))
            .thenReturn(listOf(OrderedBucket("1970-01-01,0", mutableListOf(value2))).iterator())

        val result = bucketReadCache.fetchHistoryInTimeWindow(
            window = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            key = 0,
            extendToLongTerm = false
        ).toList()

        assertEquals(listOf(value1, value2), result)
    }
}
