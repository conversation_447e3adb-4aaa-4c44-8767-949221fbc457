package nl.teqplay.aisengine.bucketing

import com.amazonaws.regions.Regions
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties.Mongo
import nl.teqplay.aisengine.bucketing.properties.BucketProperties.Mongo.Unordered
import nl.teqplay.aisengine.bucketing.properties.BucketProperties.Mongo.Unordered.BulkWrite
import nl.teqplay.aisengine.bucketing.properties.BucketProperties.Mongo.Unordered.Flush
import nl.teqplay.aisengine.bucketing.properties.BucketProperties.Mongo.Unordered.Schedule
import nl.teqplay.aisengine.bucketing.properties.BucketProperties.Sweeper
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifier
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketKey
import java.time.Duration
import java.time.Instant
import java.time.LocalDate

typealias T = TimestampedInt
typealias R = TimestampedIntResolver

val B = object : BucketIdentifier<T, Int, BucketId<Int>> {
    override val id = object : BucketId<Int> {
        override fun getBucketId(date: LocalDate, key: Int): String = "$date,$key"
    }
    override val key = object : BucketKey<T, Int> {
        override fun getBucketKey(item: T): Int = item.value
    }
    override val archive = defaultArchive()
}

data class TimestampedInt(
    val value: Int,
    val timestamp: Instant = Instant.EPOCH
)

object TimestampedIntResolver : BucketItemResolver<T> {
    override fun toTimestamp(item: T) = item.timestamp
    override fun toId(item: T) = item.value.toString()
}

val bucketFactory = object : BucketFactory<T, Int, BucketId<Int>> {
    override val dataClass = T::class.java

    override val resolver = R
    override val bucketIdentifier = B
    override val bucketFormatter = DefaultBucketFormatter<T>()
}

val archiveProperties = object : BucketArchiveConfiguration {
    override val enabled: Boolean = true
    override val writeNotBefore: LocalDate? = null
    override val prefix: String = "prefix"
    override val name: String = "name"
    override val region: Regions = Regions.EU_WEST_1
}

val archiveGlobalProperties = BucketArchiveGlobalProperties()

val bucketProperties = BucketProperties(
    sweeper = Sweeper(
        maxAge = Duration.ofDays(7)
    ),
    mongo = Mongo(
        fetchBatchSize = 10,
        unordered = Unordered(
            schedule = Schedule(
                afterStartup = Duration.ofMinutes(5)
            ),
            flush = Flush(
                minAge = Duration.ofMinutes(5),
                interval = Duration.ofMinutes(5),
                maxItemsThreshold = 10
            ),
            bulkWrite = BulkWrite(
                maxCount = 10,
                sleep = Duration.ZERO
            )
        )
    )
)

val bucketPropertiesScheduleImmediately = bucketProperties.copy(
    mongo = bucketProperties.mongo.copy(
        unordered = bucketProperties.mongo.unordered.copy(
            schedule = bucketProperties.mongo.unordered.schedule.copy(
                afterStartup = Duration.ZERO
            )
        )
    )
)
