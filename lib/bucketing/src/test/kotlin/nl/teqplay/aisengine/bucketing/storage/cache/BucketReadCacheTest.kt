package nl.teqplay.aisengine.bucketing.storage.cache

import nl.teqplay.aisengine.bucketing.T
import nl.teqplay.aisengine.bucketing.archiveGlobalProperties
import nl.teqplay.aisengine.bucketing.archiveProperties
import nl.teqplay.aisengine.bucketing.bucketFactory
import nl.teqplay.aisengine.bucketing.bucketProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketReadCacheFactoryImpl
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class BucketReadCacheTest {

    @Test
    fun init() {
        val cacheFactory = mock<BucketReadCacheFactoryImpl<T, Int, BucketId<Int>>>().apply {
            whenever(collectionName).thenReturn("collectionName")
            whenever(bucket).thenReturn(bucketProperties)
            whenever(archive).thenReturn(archiveProperties)
            whenever(archiveGlobal).thenReturn(archiveGlobalProperties)
            whenever(factory).thenReturn(bucketFactory)
            whenever(mongoStorage()).thenReturn(mock())
            whenever(unorderedStorage()).thenReturn(mock())
            whenever(archiveStorage()).thenReturn(mock())
        }

        val bucketReadCache = object : BucketReadCache<T, Int, BucketId<Int>>(cacheFactory) {}
        assertNotNull(bucketReadCache)
    }
}
