package nl.teqplay.aisengine.bucketing.storage.bucket

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class BucketKeyTest {

    @Test
    fun getOutputBucketKey() {
        val bucketKey = object : BucketKey<String, String> {
            override fun getBucketKey(item: String): String = item
        }
        val input = "input"
        assertEquals(input, bucketKey.getOutputBucketKey(input))
    }
}
