package nl.teqplay.aisengine.bucketing.storage.cache.impl

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import java.time.Duration
import java.util.concurrent.ScheduledExecutorService

class BucketCacheFactoryTest {

    private val collectionName = "collectionName"
    private val mongoDatabase = mock<MongoDatabase>()
    private val objectMapper = mock<ObjectMapper>()
    private val bucket = mock<BucketProperties>()
    private val archive = mock<BucketArchiveConfiguration>()
    private val archiveGlobal = mock<BucketArchiveGlobalProperties>()
    private val scheduledExecutorService = mock<ScheduledExecutorService>()
    private val factory = mock<BucketFactory<Int, Int, BucketId<Int>>>()
    private val unorderedCollectionSuffix = "suffix"

    @Test
    fun bucketReadCacheFactory() {
        val cache = BucketCacheFactory.bucketReadCacheFactory(
            collectionName,
            mongoDatabase,
            objectMapper,
            bucket,
            archive,
            archiveGlobal,
            factory,
            mock(),
            unorderedCollectionSuffix
        )
        assertEquals(collectionName, cache.collectionName)
        assertEquals(bucket, cache.bucket)
        assertEquals(archive, cache.archive)
        assertEquals(factory, cache.factory)
    }

    @Test
    fun bucketReadCacheFactoryDual() {
        val factory = mock<BucketFactory<Int, Int, BucketId<Int>>>()
        val cache = BucketCacheFactory.bucketReadCacheFactoryDual(
            collectionName,
            mongoDatabase,
            objectMapper,
            bucket,
            archive,
            archiveGlobal,
            factory,
            mock(),
            unorderedCollectionSuffix
        )
        assertEquals(collectionName, cache.collectionName)
        assertEquals(bucket, cache.bucket)
        assertEquals(archive, cache.archive)
        assertEquals(factory, cache.factory)
    }

    @Test
    fun bucketWriteCacheFactory() {
        val factory = mock<BucketFactory<Int, Int, BucketId<Int>>>()
        val cache = BucketCacheFactory.bucketWriteCacheFactory(
            collectionName,
            mongoDatabase,
            objectMapper,
            bucket,
            archive,
            archiveGlobal,
            scheduledExecutorService,
            Duration.ZERO,
            factory,
            mock(),
            unorderedCollectionSuffix
        )
        assertEquals(collectionName, cache.collectionName)
        assertEquals(bucket, cache.bucket)
        assertEquals(archive, cache.archive)
        assertEquals(factory, cache.factory)
    }

    @Test
    fun bucketWriteCacheFactoryDual() {
        val factory = mock<BucketFactory<Int, Int, BucketId<Int>>>()
        val cache = BucketCacheFactory.bucketWriteCacheFactoryDual(
            collectionName,
            mongoDatabase,
            objectMapper,
            bucket,
            archive,
            archiveGlobal,
            scheduledExecutorService,
            Duration.ZERO,
            factory,
            mock(),
            unorderedCollectionSuffix
        )
        assertEquals(collectionName, cache.collectionName)
        assertEquals(bucket, cache.bucket)
        assertEquals(archive, cache.archive)
        assertEquals(factory, cache.factory)
    }
}
