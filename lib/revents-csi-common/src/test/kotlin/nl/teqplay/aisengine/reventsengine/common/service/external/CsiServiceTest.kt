package nl.teqplay.aisengine.reventsengine.common.service.external

import nl.teqplay.aisengine.reventsengine.global.model.InterestRelevantShipInternal
import nl.teqplay.csi.model.ship.mapping.ImoMmsiMapping
import nl.teqplay.csi.model.ship.mapping.ShipRegisterMapping
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.csi.client.CsiShipClient
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.TestConstructor
import java.time.Duration
import java.time.Instant

@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class CsiServiceTest(
    private val csiService: CsiService
) : BaseTest() {

    companion object {
        private val window = TimeWindow(
            from = Instant.EPOCH,
            to = Instant.EPOCH.plusSeconds(10)
        )
        private val windowNoMargin = TimeWindow(
            from = window.from.plusSeconds(1),
            to = window.to.minusSeconds(1)
        )
    }

    private val interestComparator = compareBy<InterestRelevantShipInternal>({ it.mmsi }, { it.imo }, { it.window.from })

    @TestConfiguration
    class Config {

        @Bean
        fun csiShipClient() = mock<CsiShipClient> {
            whenever(it.listShipRegisterMapping()).thenReturn(
                listOf(
                    ShipRegisterMapping(
                        imo = "0",
                        mapping = listOf(
                            ImoMmsiMapping(
                                mmsi = "3",
                                from = Instant.EPOCH.plusSeconds(10).toEpochMilli(),
                                margin = null,
                                to = null,
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                            ImoMmsiMapping(
                                mmsi = "2",
                                from = Instant.EPOCH.plusSeconds(5).toEpochMilli(),
                                margin = null,
                                to = null,
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                            ImoMmsiMapping(
                                mmsi = "0",
                                from = null,
                                margin = null,
                                to = Instant.EPOCH.toEpochMilli(),
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                            ImoMmsiMapping(
                                mmsi = "1",
                                from = null,
                                margin = null,
                                to = Instant.EPOCH.plusSeconds(5).toEpochMilli(),
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                        ),
                        ignored = emptySet(),
                        state = "initial"
                    ),
                    ShipRegisterMapping(
                        imo = "2",
                        mapping = listOf(
                            ImoMmsiMapping(
                                mmsi = "1",
                                from = null,
                                margin = null,
                                to = Instant.EPOCH.plusSeconds(7).toEpochMilli(),
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                            ImoMmsiMapping(
                                mmsi = "2",
                                from = Instant.EPOCH.plusSeconds(3).toEpochMilli(),
                                margin = null,
                                to = null,
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                        ),
                        ignored = emptySet(),
                        state = "initial"
                    ),
                    ShipRegisterMapping(
                        imo = "100",
                        mapping = listOf(
                            ImoMmsiMapping(
                                mmsi = "200",
                                from = Instant.EPOCH.plusSeconds(3).toEpochMilli(),
                                margin = null,
                                to = Instant.EPOCH.plusSeconds(7).toEpochMilli(),
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                            ImoMmsiMapping(
                                mmsi = "300",
                                from = null,
                                margin = null,
                                to = null,
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                        ),
                        ignored = emptySet(),
                        state = "initial"
                    ),
                    ShipRegisterMapping(
                        imo = "invalid",
                        mapping = listOf(
                            ImoMmsiMapping(
                                mmsi = "1000",
                                from = null,
                                margin = null,
                                to = null,
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                        ),
                        ignored = emptySet(),
                        state = "initial"
                    ),
                    ShipRegisterMapping(
                        imo = "1000",
                        mapping = listOf(
                            ImoMmsiMapping(
                                mmsi = "1000",
                                from = null,
                                margin = null,
                                to = null,
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                        ),
                        ignored = emptySet(),
                        state = "initial"
                    ),
                    ShipRegisterMapping(
                        imo = "9377054",
                        mapping = listOf(
                            ImoMmsiMapping(
                                mmsi = "228330900",
                                from = null,
                                margin = null,
                                to = null,
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                            ImoMmsiMapping(
                                mmsi = "229933000",
                                from = null,
                                margin = null,
                                to = null,
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            ),
                        ),
                        ignored = emptySet(),
                        state = "initial"
                    ),
                    ShipRegisterMapping(
                        imo = "9261487",
                        mapping = listOf(
                            ImoMmsiMapping(
                                mmsi = "205340000",
                                from = null,
                                margin = null,
                                to = null,
                                createdTime = Instant.EPOCH.toEpochMilli(),
                                updatedTime = null
                            )
                        ),
                        ignored = emptySet(),
                        state = "initial"
                    )
                )
            )
        }

        @Bean
        fun csiService(csiShipClient: CsiShipClient) = CsiService(csiShipClient)
    }

    @Test
    fun `getImoAtTime - none for non-existing mmsi`() {
        assertNull(csiService.getImoAtTime(-1, window.from))
    }

    @Test
    fun getImoAtTime() {
        assertNull(csiService.getImoAtTime(0, window.from))
        assertNull(csiService.getImoAtTime(0, window.to))

        assertEquals(0, csiService.getImoAtTime(1, window.from))
        assertNull(csiService.getImoAtTime(1, window.to))

        assertNull(csiService.getImoAtTime(2, window.from))
        assertEquals(0, csiService.getImoAtTime(2, window.to))

        assertNull(csiService.getImoAtTime(3, window.from))
        assertEquals(0, csiService.getImoAtTime(3, window.to))
    }

    @Test
    fun `getMappingByImo - empty for non-existing imo`() {
        assertEquals(emptyList<InterestRelevantShipInternal>(), csiService.getInterestsByImo(-1, window, window))
    }

    @Test
    fun `getInterestsByImo - no overlap`() {
        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 1,
                    imo = 0,
                    window = TimeWindow(
                        from = Instant.EPOCH,
                        to = Instant.EPOCH.plusSeconds(5)
                    )
                ),
                InterestRelevantShipInternal(
                    mmsi = 2,
                    imo = 0,
                    window = TimeWindow(
                        from = Instant.EPOCH.plusSeconds(5),
                        to = Instant.EPOCH.plusSeconds(10)
                    )
                ),
            ),
            csiService.getInterestsByImo(0, window, window)
        )
    }

    @Test
    fun `getInterestsByImo - with overlap`() {
        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 1,
                    imo = 2,
                    window = TimeWindow(
                        from = Instant.EPOCH,
                        to = Instant.EPOCH.plusSeconds(7)
                    )
                ),
                InterestRelevantShipInternal(
                    mmsi = 2,
                    imo = 2,
                    window = TimeWindow(
                        from = Instant.EPOCH.plusSeconds(3),
                        to = Instant.EPOCH.plusSeconds(10)
                    )
                ),
            ),
            csiService.getInterestsByImo(2, window, window)
        )
    }

    @Test
    fun `getInterestsByImo - with overlap and windowNoMargin`() {
        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 1,
                    imo = 2,
                    window = TimeWindow(
                        from = Instant.EPOCH,
                        to = Instant.EPOCH.plusSeconds(7)
                    ),
                    windowNoMargin = TimeWindow(
                        from = windowNoMargin.from,
                        to = Instant.EPOCH.plusSeconds(7)
                    )
                ),
                InterestRelevantShipInternal(
                    mmsi = 2,
                    imo = 2,
                    window = TimeWindow(
                        from = Instant.EPOCH.plusSeconds(3),
                        to = Instant.EPOCH.plusSeconds(10)
                    ),
                    windowNoMargin = TimeWindow(
                        from = Instant.EPOCH.plusSeconds(3),
                        to = windowNoMargin.to
                    )
                ),
            ),
            csiService.getInterestsByImo(2, window, windowNoMargin)
        )
    }

    @Test
    fun `resolveInterestsByMmsi - invalid IMO`() {
        val input = listOf(
            // Will resolve into IMO=invalid and IMO=1000, should ignore invalid IMO
            InterestRelevantShipInternal(mmsi = 1000, imo = null, window = window),
        )
        val output = listOf(
            // IMO: 1000
            InterestRelevantShipInternal(
                mmsi = 1000,
                imo = 1000,
                window = window
            ),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }

    @Test
    fun `resolveInterestsByMmsi - a MMSI doesn't exist, drop and continue`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofSeconds(15))
        val input = listOf(
            // MMSI: -1 doesn't exist, so should skip
            InterestRelevantShipInternal(mmsi = -1, imo = null, window = window),
            InterestRelevantShipInternal(mmsi = 1, imo = null, window = window),
        )
        val output = listOf(
            // IMO: 0
            // MMSI: 0 is missing since it gets disabled right on the window from
            InterestRelevantShipInternal(
                mmsi = 1,
                imo = 0,
                window = TimeWindow(window.from, Duration.ofSeconds(5))
            ),

            // IMO: 2
            InterestRelevantShipInternal(
                mmsi = 1,
                imo = 2,
                window = TimeWindow(window.from, Duration.ofSeconds(7))
            ),
            InterestRelevantShipInternal(
                mmsi = 2,
                imo = 2,
                window = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(7)
                )
            ),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }

    @Test
    fun `resolveInterestsByMmsi - fully aligned`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofSeconds(15))
        val input = listOf(
            InterestRelevantShipInternal(mmsi = 0, imo = null, window = window),
            InterestRelevantShipInternal(mmsi = 1, imo = null, window = window),
            InterestRelevantShipInternal(mmsi = 2, imo = null, window = window),
            InterestRelevantShipInternal(mmsi = 3, imo = null, window = window),
        )
        val output = listOf(
            // IMO: 0
            // MMSI: 0 is missing since it gets disabled right on the window from
            InterestRelevantShipInternal(
                mmsi = 1,
                imo = 0,
                window = TimeWindow(window.from, Duration.ofSeconds(5))
            ),
            InterestRelevantShipInternal(
                mmsi = 2,
                imo = 0,
                window = TimeWindow(Instant.EPOCH.plusSeconds(5), window.to)
            ),
            InterestRelevantShipInternal(
                mmsi = 3,
                imo = 0,
                window = TimeWindow(Instant.EPOCH.plusSeconds(10), window.to)
            ),

            // IMO: 2
            InterestRelevantShipInternal(
                mmsi = 1,
                imo = 2,
                window = TimeWindow(window.from, Duration.ofSeconds(7))
            ),
            InterestRelevantShipInternal(
                mmsi = 2,
                imo = 2,
                window = TimeWindow(Instant.EPOCH.plusSeconds(3), window.to)
            ),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }

    @Test
    fun `resolveInterestsByMmsi - fully aligned and windowNoMargin`() {
        val input = listOf(
            InterestRelevantShipInternal(mmsi = 0, imo = null, window = window, windowNoMargin = windowNoMargin),
            InterestRelevantShipInternal(mmsi = 1, imo = null, window = window, windowNoMargin = windowNoMargin),
            InterestRelevantShipInternal(mmsi = 2, imo = null, window = window, windowNoMargin = windowNoMargin),
            InterestRelevantShipInternal(mmsi = 3, imo = null, window = window, windowNoMargin = windowNoMargin),
        )
        val output = listOf(
            // IMO: 0
            // MMSI: 0 is missing since it gets disabled right on the window from
            InterestRelevantShipInternal(
                mmsi = 1,
                imo = 0,
                window = TimeWindow(
                    from = window.from,
                    to = window.from.plusSeconds(5)
                ),
                windowNoMargin = TimeWindow(
                    from = windowNoMargin.from,
                    to = window.from.plusSeconds(5)
                ),
            ),
            InterestRelevantShipInternal(
                mmsi = 2,
                imo = 0,
                window = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(5),
                    to = window.to
                ),
                windowNoMargin = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(5),
                    to = windowNoMargin.to
                )
            ),

            // IMO: 2
            InterestRelevantShipInternal(
                mmsi = 1,
                imo = 2,
                window = TimeWindow(
                    time = window.from,
                    duration = Duration.ofSeconds(7)
                ),
                windowNoMargin = TimeWindow(
                    time = windowNoMargin.from,
                    duration = Duration.ofSeconds(6)
                )
            ),
            InterestRelevantShipInternal(
                mmsi = 2,
                imo = 2,
                window = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = window.to
                ),
                windowNoMargin = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = windowNoMargin.to
                )
            ),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }

    @Test
    fun `resolveInterestsByMmsi - not aligned, active MMSI does not mean IMO is active for the same time`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofSeconds(15))
        val input = listOf(
            InterestRelevantShipInternal(mmsi = 200, imo = null, window = window),
        )
        val output = listOf(
            // IMO: 100
            InterestRelevantShipInternal(
                mmsi = 200,
                imo = 100,
                window = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(7)
                )
            ),
            InterestRelevantShipInternal(
                mmsi = 300,
                imo = 100,
                window = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(7)
                )
            ),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }

    @Test
    fun `resolveInterestsByMmsi - not aligned and windowNoMargin (only mmsi=200)`() {
        val input = listOf(
            InterestRelevantShipInternal(mmsi = 200, imo = null, window = window, windowNoMargin = windowNoMargin),
        )
        val output = listOf(
            // IMO: 100
            InterestRelevantShipInternal(
                mmsi = 200,
                imo = 100,
                window = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(7)
                ),
                windowNoMargin = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(7)
                )
            ),
            InterestRelevantShipInternal(
                mmsi = 300,
                imo = 100,
                window = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(7)
                ),
                windowNoMargin = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(7)
                )
            ),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }

    @Test
    fun `resolveInterestsByMmsi - not aligned and windowNoMargin`() {
        val input = listOf(
            InterestRelevantShipInternal(mmsi = 200, imo = null, window = window, windowNoMargin = windowNoMargin),
            InterestRelevantShipInternal(mmsi = 300, imo = null, window = window, windowNoMargin = windowNoMargin),
        )
        val output = listOf(
            // IMO: 100
            InterestRelevantShipInternal(
                mmsi = 200,
                imo = 100,
                window = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(7)
                ),
                windowNoMargin = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(7)
                )
            ),
            InterestRelevantShipInternal(
                mmsi = 300,
                imo = 100,
                window = window,
                windowNoMargin = windowNoMargin
            ),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }

    @Test
    fun `resolveInterestsByMmsi - not aligned, multiple active MMSIs - overlapping`() {
        // Simulate:
        // - MMSI: 200 is seen for the full window (but is only active partially)
        // - MMSI: 300 is only seen partially
        val fullWindow = TimeWindow(Instant.EPOCH, Duration.ofSeconds(15))
        val partialWindow = TimeWindow(Instant.EPOCH, Duration.ofSeconds(3))
        val input = listOf(
            InterestRelevantShipInternal(mmsi = 200, imo = null, window = fullWindow),
            InterestRelevantShipInternal(mmsi = 300, imo = null, window = partialWindow),
        )
        val output = listOf(
            // IMO: 100
            InterestRelevantShipInternal(
                mmsi = 200,
                imo = 100,
                window = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(7)
                )
            ),
            InterestRelevantShipInternal(
                mmsi = 300,
                imo = 100,
                window = TimeWindow(
                    from = Instant.EPOCH,
                    // Capped, since it's not seen after.
                    to = Instant.EPOCH.plusSeconds(7)
                )
            ),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }

    @Test
    fun `resolveInterestsByMmsi - not aligned, multiple active MMSIs - not overlapping`() {
        // Simulate:
        // - MMSI: 200 and MMSI: 300 are both partially used
        val partialWindow1 = TimeWindow(
            from = Instant.EPOCH.plusSeconds(3),
            to = Instant.EPOCH.plusSeconds(7)
        )
        val partialWindow2 = TimeWindow(
            from = Instant.EPOCH.plusSeconds(10),
            to = Instant.EPOCH.plusSeconds(15)
        )
        val input = listOf(
            InterestRelevantShipInternal(mmsi = 200, imo = null, window = partialWindow1),
            InterestRelevantShipInternal(mmsi = 300, imo = null, window = partialWindow2),
        )
        val output = listOf(
            // IMO: 100
            InterestRelevantShipInternal(mmsi = 200, imo = 100, window = partialWindow1),
            InterestRelevantShipInternal(mmsi = 300, imo = 100, window = partialWindow1),
            InterestRelevantShipInternal(mmsi = 300, imo = 100, window = partialWindow2),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }

    @Test
    fun `resolveInterestsByMmsi - partial interest by MMSI must preserve flag after resolving`() {
        val window = TimeWindow(
            time = Instant.EPOCH,
            duration = Duration.ofDays(1)
        )
        val input = listOf(
            InterestRelevantShipInternal(mmsi = 228330900, imo = null, window = window, partial = true),
        )
        val output = listOf(
            InterestRelevantShipInternal(mmsi = 228330900, imo = 9377054, window = window, partial = true),
            InterestRelevantShipInternal(mmsi = 229933000, imo = 9377054, window = window, partial = true),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }

    @Test
    fun `resolveInterestsByMmsi - non-overlapping windowNoMargin should not be merged`() {
        val window1 = TimeWindow(
            from = Instant.parse("2024-03-26T00:00:00Z"),
            to = Instant.parse("2024-04-05T00:00:00Z")
        )
        val windowNoMargin1 = TimeWindow(
            from = Instant.parse("2024-03-30T00:00:00Z"),
            to = Instant.parse("2024-04-01T00:00:00Z")
        )
        val window2 = TimeWindow(
            from = Instant.parse("2024-03-29T00:00:00Z"),
            to = Instant.parse("2024-04-10T00:00:00Z")
        )
        val windowNoMargin2 = TimeWindow(
            from = Instant.parse("2024-04-02T00:00:00Z"),
            to = Instant.parse("2024-04-06T00:00:00Z")
        )
        val input = listOf(
            InterestRelevantShipInternal(
                mmsi = 205340000,
                imo = null,
                window = window1,
                windowNoMargin = windowNoMargin1,
                partial = true
            ),
            InterestRelevantShipInternal(
                mmsi = 205340000,
                imo = null,
                window = window2,
                windowNoMargin = windowNoMargin2,
                partial = false
            ),
        )
        val output = listOf(
            InterestRelevantShipInternal(
                mmsi = 205340000,
                imo = 9261487,
                window = window1,
                windowNoMargin = windowNoMargin1,
                partial = true
            ),
            InterestRelevantShipInternal(
                mmsi = 205340000,
                imo = 9261487,
                window = window2,
                windowNoMargin = windowNoMargin2,
                partial = false
            ),
        )

        assertEquals(
            output.toSortedSet(interestComparator).toList(),
            csiService.resolveInterestsByMmsi(input).toSortedSet(interestComparator).toList()
        )
    }
}
