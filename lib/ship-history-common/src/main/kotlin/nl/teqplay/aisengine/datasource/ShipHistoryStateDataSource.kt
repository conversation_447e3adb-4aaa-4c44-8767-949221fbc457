package nl.teqplay.aisengine.datasource

import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.model.ShipHistoryState
import nl.teqplay.aisengine.util.createLocationKey
import nl.teqplay.aisengine.util.getAisMessage
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.`in`
import java.time.Instant

class ShipHistoryStateDataSource(
    mongoDatabase: MongoDatabase
) : MinimalStateDataSource<ShipHistoryState, ShipHistoryStateWrapper>(
    mongoDatabase = mongoDatabase,
    stateClass = ShipHistoryStateWrapper::class.java
) {
    override fun applyIndexes(collection: MongoCollection<ShipHistoryStateWrapper>) {
        super.applyIndexes(collection)
        // Also add an index on the location key as we use this to lazy load ship state
        collection.ensureIndex(ShipHistoryStateWrapper::locationKey)
        collection.ensureIndex(ShipHistoryStateWrapper::state / ShipHistoryState::static / AisStaticWrapper::message / AisStaticMessage::imo)
    }

    override fun toStateWrapper(entry: Map.Entry<Int, ShipHistoryState>): ShipHistoryStateWrapper {
        val (mmsi, state) = entry

        // Take the location like we would via the ShipStateService
        val message = getAisMessage(
            position = state.position,
            static = state.static,
            longRange = state.longrange
        )
        val locationKey = createLocationKey(location = message?.location)

        return ShipHistoryStateWrapper(
            _id = mmsi,
            state = state,
            updatedAt = Instant.now(),
            locationKey = locationKey
        )
    }

    fun findStateByImo(imo: Int): List<ShipHistoryStateWrapper> {
        val filterByImo = ShipHistoryStateWrapper::state / ShipHistoryState::static / AisStaticWrapper::message / AisStaticMessage::imo eq imo
        return collection.find(filterByImo)
            .toList()
    }

    fun findStateByLocationKeys(locationKeys: List<String>): Iterable<ShipHistoryStateWrapper> {
        val filterByLocationKeys = ShipHistoryStateWrapper::locationKey `in` locationKeys
        return collection.find(filterByLocationKeys).toList()
    }
}

data class ShipHistoryStateWrapper(
    override val _id: Int,
    override val state: ShipHistoryState,
    override val updatedAt: Instant = Instant.now(),
    val locationKey: String = ""
) : StateWrapper<ShipHistoryState>
