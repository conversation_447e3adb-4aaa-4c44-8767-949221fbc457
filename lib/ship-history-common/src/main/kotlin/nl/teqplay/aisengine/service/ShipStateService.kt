package nl.teqplay.aisengine.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import jakarta.annotation.PreDestroy
import nl.teqplay.aisengine.datasource.ShipHistoryStateDataSource
import nl.teqplay.aisengine.model.ShipHistoryState
import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage
import nl.teqplay.aisengine.util.QueueWrapper
import nl.teqplay.aisengine.util.getAisMessage
import nl.teqplay.skeleton.common.config.NatsProperties
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.skeleton.nats.NatsClientBuilder
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicLong
import kotlin.concurrent.thread

private val LOG = KotlinLogging.logger {}

@Component
class ShipStateService(
    private val shipStateSubscriberService: ShipStateSubscriberService?,
    private val shipHistoryStateDataSource: ShipHistoryStateDataSource,
    private val objectMapper: ObjectMapper,
    private val natsClientBuilder: NatsClientBuilder,
    private val nats: NatsProperties,
    private val pod: PodProperties,
    meterRegistry: MeterRegistry
) : InitializingBean {

    companion object {
        const val STREAM = "ship-history:updates"
        const val STREAM_SUBJECT_PREFIX = "ship-history.update"

        /**
         * The sweet spot on how big a batch should be when loading in the current ship state from the database.
         * - Using a smaller number will result in more network round trips, which we want to avoid as they take additional time.
         * - Using a bigger number will slow down loading in of the state because when the amount of data exceeds 100mb it will automatically use allowDiskUse in MongoDB 6.0 or higher.
         * @see com.mongodb.client.FindIterable.allowDiskUse
         */
        const val RESTORE_BATCH_SIZE = 20_000
    }

    /**
     * Boolean to stop the publisher thread when the application shuts down.
     */
    @Volatile
    protected var running = false

    private val shipStateStartupTime = Instant.now()

    private val publisherThread: Thread = thread(start = false, name = "stream-publisher", block = ::publisher)

    private val updates = LinkedBlockingQueue<QueueWrapper<AisCurrentMessage>>(5000)
    private val registry = MetricRegistry(ShipStateService::class, meterRegistry, listOf("stream"))
    private val pendingUpdates = registry.createGauge(Metric.MESSAGE_COUNT_PENDING, AtomicLong(), "updates")
    private val publishedUpdates = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong(), "updates")

    override fun afterPropertiesSet() {
        if (shipStateSubscriberService != null) {
            watchUpdates()
            restoreState()
        } else {
            running = true
            publisherThread.start()
        }
    }

    @PreDestroy
    fun shutdown() {
        updates.put(QueueWrapper.poisonPill())
        publisherThread.join(Duration.ofMinutes(1).toMillis())
    }

    private fun publisher() {
        var running = true

        val producerStream = requireNotNull(
            natsClientBuilder.producerStream<AisCurrentMessage>(
                config = nats,
                stream = STREAM,
                subjects = listOf("$STREAM_SUBJECT_PREFIX.>"),
                serializer = objectMapper::writeValueAsBytes,
                maxAge = Duration.ofHours(1),
                storeOnDisk = true
            )
        )

        while (running || updates.isNotEmpty()) {
            val lastElement = producerStream.publishAsync(
                queue = updates,
                pending = pendingUpdates,
                published = publishedUpdates,
                subject = { _, it -> "$STREAM_SUBJECT_PREFIX.${it.mmsi}" },
                convert = { it.data },
                isPoisonPill = { it.lastElement }
            )
            if (lastElement) {
                running = false
            }
        }
    }

    fun update(message: AisCurrentMessage) {
        updates.put(QueueWrapper(message))
    }

    private fun watchUpdates() {
        if (!nats.enabled) return

        LOG.info { "Listening for updates..." }
        val consumerStream = requireNotNull(
            natsClientBuilder.consumerStream<AisCurrentMessage>(
                config = nats,
                stream = STREAM,
                deserializer = objectMapper::readValue
            )
        )

        val suffix = pod.name // e.g. 'ship-history-dev-6c445bc6f6-j94pj'
            .removePrefix(nats.username) // e.g. nats.username=ship-history, then '-dev-6c445bc6f6-j94pj'
            .removePrefix("-") // e.g. 'dev-6c445bc6f6-j94pj'

        consumerStream.consume(
            suffix = suffix,
            ephemeral = true,
            subjects = listOf(">")
        ) { aisCurrentMessage, message ->
            try {
                shipStateSubscriberService?.update(aisCurrentMessage)
                message.ack()
            } catch (e: Exception) {
                LOG.error(e) { "Failed updating state service: $e" }
            }
        }
    }

    private fun restoreState() {
        LOG.info { "Prepare loading ship state from database" }
        val startTime = Instant.now()
        val totalShips = shipHistoryStateDataSource.size
        var totalLoadedShips = 0
        var totalSkipped = 0
        var batchStartTime = Instant.now()

        LOG.info { "Started loading ship state from database" }
        shipHistoryStateDataSource.getInitialState(shipStateStartupTime).batchSize(RESTORE_BATCH_SIZE).forEach { (mmsi, state) ->
            totalLoadedShips += 1
            if (totalLoadedShips % RESTORE_BATCH_SIZE == 0) {
                batchStartTime = logLoadingStateAndGetNewBatchStartTime(batchStartTime, totalLoadedShips, totalShips)
            }

            val loaded = loadStateBatchItem(state, mmsi)
            if (!loaded) {
                totalSkipped += 1
            }
        }
        val duration = Duration.between(startTime, Instant.now()).toMillis()
        LOG.info { "Restored ship state ($duration ms) skipped $totalSkipped vessels because already loaded out of the $totalLoadedShips" }

        // start background loading
        thread(name = "state-background-loader", block = { lazyLoadOlderData(totalLoadedShips) })
    }

    private fun lazyLoadOlderData(totalAlreadyLoadedShips: Int) {
        LOG.info { "Prepare lazy loading old ship state from database" }
        val startTime = Instant.now()
        val totalShips = shipHistoryStateDataSource.size
        var totalLoadedShips = totalAlreadyLoadedShips
        var totalSkipped = 0
        var batchStartTime = Instant.now()

        LOG.info { "Started lazy loading old ship state in the background" }
        shipHistoryStateDataSource.getOlderState(shipStateStartupTime).batchSize(RESTORE_BATCH_SIZE).forEach { (mmsi, state) ->
            totalLoadedShips += 1
            if (totalLoadedShips % RESTORE_BATCH_SIZE == 0) {
                batchStartTime = logLoadingStateAndGetNewBatchStartTime(batchStartTime, totalLoadedShips, totalShips)
            }

            val loaded = loadStateBatchItem(state, mmsi)
            if (!loaded) {
                totalSkipped += 1
            }
        }
        val duration = Duration.between(startTime, Instant.now()).toMillis()
        LOG.info { "Restored old ship state ($duration ms) skipped $totalSkipped vessels because already loaded out of the $totalLoadedShips" }
        shipStateSubscriberService?.isStateFullyLoaded = true
    }

    private fun loadStateBatchItem(state: ShipHistoryState, mmsi: Int): Boolean {
        if (shipStateSubscriberService?.containsMmsiInState(mmsi) == true) {
            return false
        }

        val message = getAisCurrentMessage(state)
        if (message != null) {
            shipStateSubscriberService?.update(message)
        }

        return true
    }

    private fun logLoadingStateAndGetNewBatchStartTime(batchStartTime: Instant, totalLoadedShips: Int, totalShips: Long): Instant {
        val now = Instant.now()
        val batchDuration = Duration.between(batchStartTime, now).toMillis()
        LOG.info { "Loaded batch in $batchDuration ms, rate of ${"%.1f".format(RESTORE_BATCH_SIZE.toDouble() / batchDuration)}/ms ($totalLoadedShips/$totalShips)" }

        return now
    }

    fun getAisCurrentMessage(state: ShipHistoryState): AisCurrentMessage? {
        val message = getAisMessage(state.position, state.static, state.longrange)
            ?: return null
        return AisCurrentMessage(message, state.derived)
    }
}
