package nl.teqplay.aisengine.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoClient
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.common.HEALTH_INDICATOR_CLASS
import nl.teqplay.skeleton.common.config.MongoDbProperties
import nl.teqplay.skeleton.datasource.MongoDbBuilder
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary

@Configuration
@EnableConfigurationProperties(MongoDbProperties::class)
class MongoDbConfiguration(
    private val mongoDbProperties: MongoDbProperties
) {

    @Bean
    @Primary
    fun mongoClient(objectMapper: ObjectMapper): MongoClient {
        val mongoJackObjectMapper = MongoDbBuilder.configureObjectMapper(objectMapper.copy())
        return MongoDbBuilder.mongoClient(mongoDbProperties, mongoJackObjectMapper)
    }

    @Bean
    @Primary
    fun mongoDatabase(
        client: MongoClient
    ): MongoDatabase = MongoDbBuilder.mongoDatabase(client, mongoDbProperties)

    @Configuration
    @ConditionalOnClass(name = [HEALTH_INDICATOR_CLASS])
    inner class HealthConfiguration {
        @Bean
        fun mongoHealthIndicator(database: MongoDatabase): HealthIndicator? {
            // ensures no health checking is done, we don't want to restart ship-history based on mongo being down
            // we'll just reconnect when it comes back up again
            return null
        }
    }
}
