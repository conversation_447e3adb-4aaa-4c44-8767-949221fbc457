package nl.teqplay.aisengine.util

import nl.teqplay.aisengine.model.ShipHistoryState
import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage
import nl.teqplay.skeleton.model.Location

fun createLocationKey(location: Location?): String {
    if (location != null) {
        val flooredLocation = Location(
            lat = floorLatitudeToDecimals(location.lat, 0),
            lon = floorLongitudeToDecimals(location.lon, 0)
        )
        val flooredLon = roundToDecimalString(flooredLocation.lon, 0)
        val flooredLat = roundToDecimalString(flooredLocation.lat, 0)

        return "$flooredLat,$flooredLon"
    }

    return "NO_LOCATION"
}

fun getAisCurrentMessage(state: ShipHistoryState): AisCurrentMessage? {
    val message = getAisMessage(state.position, state.static, state.longrange)
        ?: return null
    return AisCurrentMessage(message, state.derived)
}
