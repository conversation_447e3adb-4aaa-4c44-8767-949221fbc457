package nl.teqplay.aisengine.service

import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage

interface ShipStateSubscriberService {

    /**
     * Indicate if the ship state is fully loaded, meaning the up-to-date state we load on start-up
     *  and the background loaded state
     */
    var isStateFullyLoaded: Boolean

    /**
     * Check if the provided mmsi is loaded in the state
     */
    fun containsMmsiInState(mmsi: Int): Boolean

    fun update(message: AisCurrentMessage)
    fun delete(mmsi: Int)
}
