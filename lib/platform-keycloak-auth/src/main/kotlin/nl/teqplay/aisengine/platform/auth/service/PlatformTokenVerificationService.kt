package nl.teqplay.aisengine.platform.auth.service

import nl.teqplay.skeleton.auth.credentials.services.TokenVerificationService
import nl.teqplay.skeleton.auth.credentials.services.TokenVerificationService.TokenVerificationException
import org.keycloak.common.VerificationException
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.Authentication

open class PlatformTokenVerificationService(
    private val tokenVerifier: PlatformKeycloakTokenVerifier
) : TokenVerificationService {
    override fun authenticate(token: String): Authentication {
        val username = verifyToken(token)
        return UsernamePasswordAuthenticationToken(username, token, emptyList())
    }

    protected fun verifyToken(token: String): String? {
        try {
            val parsedToken = tokenVerifier.verify(token).token
            return parsedToken.preferredUsername ?: parsedToken.email
        } catch (e: VerificationException) {
            throw TokenVerificationException("Error on parsing jwt token", e)
        }
    }
}
