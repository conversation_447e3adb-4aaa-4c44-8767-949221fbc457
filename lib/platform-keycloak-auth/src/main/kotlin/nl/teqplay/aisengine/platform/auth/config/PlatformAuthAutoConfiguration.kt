package nl.teqplay.aisengine.platform.auth.config

import nl.teqplay.aisengine.platform.auth.properties.PlatformAuthProperties
import nl.teqplay.aisengine.platform.auth.service.PlatformKeycloakTokenVerifier
import nl.teqplay.aisengine.platform.auth.service.PlatformTokenVerificationService
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@EnableConfigurationProperties(PlatformAuthProperties::class)
class PlatformAuthAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    fun service(
        properties: PlatformAuthProperties
    ): PlatformTokenVerificationService {
        val tokenVerifier = PlatformKeycloakTokenVerifier(
            realm = properties.keycloakRealm,
            url = properties.keycloakUrl
        )

        return PlatformTokenVerificationService(tokenVerifier)
    }
}
