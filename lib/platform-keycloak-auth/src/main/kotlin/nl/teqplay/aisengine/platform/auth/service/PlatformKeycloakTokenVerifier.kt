package nl.teqplay.aisengine.platform.auth.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.github.oshai.kotlinlogging.KotlinLogging
import org.keycloak.TokenVerifier
import org.keycloak.TokenVerifier.TokenTypeCheck
import org.keycloak.common.VerificationException
import org.keycloak.exceptions.TokenSignatureInvalidException
import org.keycloak.jose.jwk.JSONWebKeySet
import org.keycloak.jose.jwk.JWK.Use
import org.keycloak.jose.jwk.JWKParser
import org.keycloak.jose.jws.AlgorithmType
import org.keycloak.representations.AccessToken
import org.keycloak.util.TokenUtil
import java.net.URL
import java.security.PublicKey

/**
 * Class which can verify an Keycloak [AccessToken]
 *
 * @param realm the realm where the server and clients are located
 * @param url the url where Key<PERSON><PERSON><PERSON> is located
 */
open class PlatformKeycloakTokenVerifier(
    realm: String,
    url: String
) {
    private val LOG = KotlinLogging.logger {}

    private val realmUrl = "$url/auth/realms/$realm"
    private val certUrl = "$realmUrl/protocol/openid-connect/certs"

    private val realmCheck = TokenVerifier.RealmUrlCheck(realmUrl)

    private var jwks: JSONWebKeySet? = null

    /**
     * We only allow algorithm types that work with a public signature key
     */
    private val allowedAlgorithmTypes = listOf(
        AlgorithmType.RSA,
        AlgorithmType.ECDSA
    )

    private fun getPublicSignatureKey(): PublicKey? {
        if (jwks == null) {
            jwks = retrieveJsonWebKeySet()
        }

        return retrievePublicKeyFromCerts(jwks, Use.SIG)
    }

    /**
     * Verify a given [AccessToken], checking the following:
     *  - If the token is still active
     *  - If the token public key is the same as the known signature key received from the Keycloak server certs end-point
     *  - If the provided realm is the expected realm
     *  - If the algorithm type of the token is the one we allow
     *
     *  @param token The provided [AccessToken]
     */
    fun verify(token: String): TokenVerifier<AccessToken> {
        val signatureKey = getPublicSignatureKey()
            ?: throw VerificationException("Something went wrong while verifying the signature of the provided token")

        val checks = listOf(
            realmCheck,
            TokenVerifier.SUBJECT_EXISTS_CHECK,
            TokenTypeCheck(listOf(TokenUtil.TOKEN_TYPE_BEARER)),
            TokenVerifier.IS_ACTIVE
        )

        val tokenVerifier = TokenVerifier.create(token, AccessToken::class.java)
        val algorithmType = tokenVerifier.header.algorithm.type

        if (!allowedAlgorithmTypes.contains(algorithmType)) {
            throw TokenSignatureInvalidException(tokenVerifier.token, "Invalid token signature provided")
        }

        return tokenVerifier.publicKey(signatureKey)
            .withChecks(*checks.toTypedArray())
            .verify()
    }

    /**
     * Retrieve the jwks from Keycloak using the certUrl
     */
    private fun retrieveJsonWebKeySet(): JSONWebKeySet? {
        val objectMapper = ObjectMapper()
        return try {
            objectMapper.readValue(URL(certUrl).openStream())
        } catch (e: Throwable) {
            LOG.error(e) { "[KeycloakTokenVerifier] Could not receive JWKs from Keycloak which results in denying all Keycloak tokens" }
            null
        }
    }

    /**
     * Get the PublicKey from the jwks and put it in a JWKParser, so it can later be used to verify any given token
     */
    private fun retrievePublicKeyFromCerts(jwks: JSONWebKeySet?, certUse: Use): PublicKey? {
        val jwk = jwks?.keys?.find { it.publicKeyUse == certUse.asString() }

        return jwk?.let { JWKParser.create(it).toPublicKey() }
    }
}
