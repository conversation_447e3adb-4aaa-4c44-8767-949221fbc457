package nl.teqplay.aisengine.platform.auth.properties

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "auth.platform")
data class PlatformAuthProperties(
    /**
     * Url to the Keycloak server we use for authentication
     */
    val keycloakUrl: String,

    /**
     * The realm within the token we expect
     */
    val keycloakRealm: String
)
