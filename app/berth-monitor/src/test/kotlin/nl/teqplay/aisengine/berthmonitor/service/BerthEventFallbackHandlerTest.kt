package nl.teqplay.aisengine.berthmonitor.service

import nl.teqplay.aisengine.berthmonitor.model.BerthState
import nl.teqplay.aisengine.berthmonitor.service.poma.PomaService
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.skeleton.common.BaseTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.lenient
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever
import java.time.Instant
import nl.teqplay.poma.api.v1.Location as PomaLocation
import nl.teqplay.skeleton.model.Location as SkeletonLocation

@ExtendWith(MockitoExtension::class)
class BerthEventFallbackHandlerTest : BaseTest() {

    @Mock
    private lateinit var pomaService: PomaService

    @Mock
    private lateinit var berthStateService: BerthStateService

    private lateinit var berthEventFallbackHandler: BerthEventFallbackHandler

    private val currentTime = Instant.now()
    private val mmsi = 123456789
    private val berthId1 = "berth-1"
    private val berthId2 = "berth-2"
    private val berthId3 = "berth-3"

    @BeforeEach
    fun setup() {
        berthEventFallbackHandler = BerthEventFallbackHandler(pomaService, berthStateService)
    }

    @Test
    fun `test getFallbackBerths returns empty list when no current berths`() {
        // Given
        whenever(berthStateService.getAllMoored(mmsi)).thenReturn(emptyList())

        // When
        val result = berthEventFallbackHandler.getFallbackBerths(berthId1, currentTime, mmsi)

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `test getFallbackBerths filters out berths with same ID as new berth`() {
        // Given
        val berthState = createBerthState(mmsi, berthId1)
        whenever(berthStateService.getAllMoored(mmsi)).thenReturn(listOf(berthState))

        // When
        val result = berthEventFallbackHandler.getFallbackBerths(berthId1, currentTime, mmsi)

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `test getFallbackBerths returns berths beyond threshold distance`() {
        // Given
        val berthState = createBerthState(mmsi, berthId1)
        whenever(berthStateService.getAllMoored(mmsi)).thenReturn(listOf(berthState))

        // Create berths with locations far away from each other
        val berth1 = createBerth(
            berthId1,
            listOf(
                PomaLocation(
                    51.9,
                    4.5
                ),
                PomaLocation(
                    52.0,
                    4.5
                ),
                PomaLocation(
                    52.1,
                    4.5
                ),
                PomaLocation(
                    52.2,
                    4.5
                )
            )
        )
        val berth2 = createBerth(
            berthId2,
            listOf(
                PomaLocation(
                    53.1,
                    4.6
                ),
                PomaLocation(
                    53.2,
                    4.6
                ),
                PomaLocation(
                    53.3,
                    4.6
                ),
                PomaLocation(
                    53.4,
                    4.6
                )
            )
        ) // Different location beyond threshold

        whenever(pomaService.getBerthById(berthId1)).thenReturn(berth1)
        whenever(pomaService.getBerthById(berthId2)).thenReturn(berth2)

        // When
        val result = berthEventFallbackHandler.getFallbackBerths(berthId2, currentTime, mmsi)

        // Then
        assertEquals(1, result.size)
        assertEquals(berthId1, result.first().areaIdentifier.id)
    }

    @Test
    fun `test getFallbackBerths returns empty list for berths within threshold distance`() {
        // Given
        val berthState = createBerthState(mmsi, berthId1)
        whenever(berthStateService.getAllMoored(mmsi)).thenReturn(listOf(berthState))

        // Create berths with locations close to each other (within threshold)
        val berth1 = createBerth(berthId1, listOf(PomaLocation(51.9, 4.5)))
        val berth3 = createBerth(berthId3, listOf(PomaLocation(51.9001, 4.5001))) // Very close location

        whenever(pomaService.getBerthById(berthId1)).thenReturn(berth1)
        whenever(pomaService.getBerthById(berthId3)).thenReturn(berth3)

        // When
        val result = berthEventFallbackHandler.getFallbackBerths(berthId3, currentTime, mmsi)

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `test getFallbackBerths handles null berth id`() {
        // Given
        val berthState = createBerthState(mmsi, berthId1)
        whenever(berthStateService.getAllMoored(mmsi)).thenReturn(listOf(berthState))

        val berth1 = createBerth(berthId1, listOf(PomaLocation(51.9, 4.5)))
        whenever(pomaService.getBerthById(berthId1)).thenReturn(berth1)

        // When
        val result = berthEventFallbackHandler.getFallbackBerths(null, currentTime, mmsi)

        // Then
        assertEquals(0, result.size)
    }

    @Test
    fun `test getFallbackBerths handles null berth from PomaService`() {
        // Given
        val berthState = createBerthState(mmsi, berthId1)
        whenever(berthStateService.getAllMoored(mmsi)).thenReturn(listOf(berthState))

        whenever(pomaService.getBerthById(berthId1)).thenReturn(null)

        // When
        val result = berthEventFallbackHandler.getFallbackBerths(berthId2, currentTime, mmsi)

        // Then
        assertEquals(1, result.size)
    }

    @Test
    fun `test getFallbackBerths handles multiple berths correctly`() {
        // Given
        val berthState1 = createBerthState(mmsi, berthId1)
        val berthState2 = createBerthState(mmsi + 1, berthId2)
        whenever(berthStateService.getAllMoored(mmsi)).thenReturn(listOf(berthState1, berthState2))
        // Create berths with different distances
        val berth1 = createBerth(berthId1, listOf(PomaLocation(51.9, 4.5)))
        val berth2 = createBerth(berthId2, listOf(PomaLocation(52.0, 4.6))) // Far from berth1
        val berth3 = createBerth(berthId3, listOf(PomaLocation(53.0, 5.0))) // Far from both

        whenever(pomaService.getBerthById(berthId1)).thenReturn(berth1)
        whenever(pomaService.getBerthById(berthId2)).thenReturn(berth2)
        whenever(pomaService.getBerthById(berthId3)).thenReturn(berth3)

        // When
        val result = berthEventFallbackHandler.getFallbackBerths(berthId3, currentTime, mmsi)

        // Then
        assertEquals(2, result.size)
        assertTrue(result.any { it.areaIdentifier.id == berthId1 })
        assertTrue(result.any { it.areaIdentifier.id == berthId2 })
    }

    @Test
    fun `test getFallbackBerths calculates minimum distance between complex berth polygons`() {
        // Given
        val berthState = createBerthState(mmsi, berthId1)
        whenever(berthStateService.getAllMoored(mmsi)).thenReturn(listOf(berthState))
        // Create berths with complex polygons
        val berth1 = createBerth(
            berthId1,
            listOf(
                PomaLocation(51.90, 4.50),
                PomaLocation(51.91, 4.50),
                PomaLocation(51.91, 4.51),
                PomaLocation(51.90, 4.51)
            )
        )

        val berth2 = createBerth(
            berthId2,
            listOf(
                PomaLocation(51.95, 4.55), // This point is closest to berth1 but still far enough
                PomaLocation(51.96, 4.55),
                PomaLocation(51.96, 4.56),
                PomaLocation(51.95, 4.56)
            )
        )

        whenever(pomaService.getBerthById(berthId1)).thenReturn(berth1)
        whenever(pomaService.getBerthById(berthId2)).thenReturn(berth2)

        // When
        val result = berthEventFallbackHandler.getFallbackBerths(berthId2, currentTime, mmsi)

        // Then
        assertEquals(1, result.size)
        assertEquals(berthId1, result.first().areaIdentifier.id)
    }

    // Helper methods
    private fun createBerthState(mmsi: Int, berthId: String): BerthState {
        val startTime = Instant.now().minusSeconds(3600)
        return BerthState(
            ship = AisShipIdentifier(mmsi, null),
            areaIdentifier = AreaIdentifier(berthId, AreaIdentifier.AreaType.BERTH, "Test Berth", "NLRTM"),
            berthIdentifier = BerthIdentifier(null, null),
            heading = null,
            draught = null,
            location = SkeletonLocation(51.9, 4.5),
            start = startTime,
            startEventId = "event-id",
            processed = false
        )
    }

    private fun createBerth(id: String, locations: List<PomaLocation>): Berth {
        return mock<Berth>().apply {
            lenient().whenever(this.area).thenReturn(locations)
        }
    }
}
