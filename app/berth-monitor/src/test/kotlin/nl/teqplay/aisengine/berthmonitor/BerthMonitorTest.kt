package nl.teqplay.aisengine.berthmonitor

import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.nats.NatsClientBuilderMock
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary

@SpringBootTest
class BerthMonitorTest : BaseTest() {
    @Configuration
    @Import(BerthMonitor::class)
    class Config {
        @Primary
        @Bean
        fun natsClientBuilderMock() = NatsClientBuilderMock()
    }

    @Test
    fun contextLoads() {
    }
}
