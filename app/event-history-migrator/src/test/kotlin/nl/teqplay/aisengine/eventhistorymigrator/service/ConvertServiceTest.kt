package nl.teqplay.aisengine.eventhistorymigrator.service

import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.eventhistorymigrator.model.AisEngineBucket
import nl.teqplay.aisengine.eventhistorymigrator.model.PlatformBucket
import nl.teqplay.aisengine.testing.event.createEncounterStartEvent
import nl.teqplay.aisengine.testing.event.createShipMovingStartEvent
import nl.teqplay.aisengine.testing.event.defaultMmsi
import nl.teqplay.aisengine.testing.platform.createPlatformAisLostEvent
import nl.teqplay.aisengine.testing.platform.createPlatformBunkerEvent
import nl.teqplay.aisengine.testing.platform.createPlatformShipMovingEvent
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.LocalDate
import java.time.YearMonth
import nl.teqplay.platform.model.event.TeqplayEvent as PlatformEvent

class ConvertServiceTest {

    private val service = ConvertService()
    private val TIMESTAMP_1 = Instant.ofEpochMilli(1672534800000)
    private val TIMESTAMP_2 = Instant.ofEpochMilli(1672621200000)

    @Test
    fun `should convert platform bucket to ais engine`() {
        val platformBuckets = listOf(
            PlatformBucket(
                _id = "2023-01-01,$defaultMmsi",
                data = listOf<PlatformEvent>(
                    createPlatformShipMovingEvent(timestamp = TIMESTAMP_1.toEpochMilli())
                )
            ),
            PlatformBucket(
                _id = "2023-01-02,$defaultMmsi",
                data = listOf<PlatformEvent>(
                    createPlatformBunkerEvent(timestamp = TIMESTAMP_2.toEpochMilli())
                )
            )
        )

        val result = service.convertToAisEngineBucket(platformBuckets, ************(2023, 1))
        val expected = listOf(
            AisEngineBucket(
                fileName = "ship,2023-01,$defaultMmsi.zip",
                data = mapOf(
                    LocalDate.of(2023, 1, 1) to listOf<Event>(createShipMovingStartEvent(actualTime = TIMESTAMP_1, createdTime = TIMESTAMP_1)),
                    LocalDate.of(2023, 1, 2) to listOf<Event>(createEncounterStartEvent(encounterType = EncounterType.BUNKER, actualTime = TIMESTAMP_2, createdTime = TIMESTAMP_2))
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should remove events not interested for ais engine`() {
        val platformBuckets = listOf(
            PlatformBucket(
                _id = "2023-01-01,$defaultMmsi",
                data = listOf<PlatformEvent>(
                    createPlatformAisLostEvent(timestamp = TIMESTAMP_1.toEpochMilli())
                )
            )
        )

        val result = service.convertToAisEngineBucket(platformBuckets, ************(2023, 1))
        val expected = listOf(
            AisEngineBucket(
                fileName = "ship,2023-01,$defaultMmsi.zip",
                data = mapOf(
                    LocalDate.of(2023, 1, 1) to emptyList(),
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should create area bucket from ship based bucket`() {
        val shipBucket = listOf(
            AisEngineBucket(
                fileName = "ship,2023-01,$defaultMmsi.zip",
                data = mapOf(
                    LocalDate.of(2023, 1, 1) to listOf<Event>(createShipMovingStartEvent(actualTime = TIMESTAMP_1, createdTime = TIMESTAMP_1)),
                    LocalDate.of(2023, 1, 2) to listOf<Event>(createEncounterStartEvent(encounterType = EncounterType.BUNKER, actualTime = TIMESTAMP_2, createdTime = TIMESTAMP_2))
                )
            )
        )

        val result = service.createAreaBuckets(shipBucket, ************(2023, 1))
        val expected = listOf(
            AisEngineBucket(
                fileName = "area,2023-01,0.0,0.0.zip",
                data = mapOf(
                    LocalDate.of(2023, 1, 1) to listOf<Event>(createShipMovingStartEvent(actualTime = TIMESTAMP_1, createdTime = TIMESTAMP_1)),
                    LocalDate.of(2023, 1, 2) to listOf<Event>(createEncounterStartEvent(encounterType = EncounterType.BUNKER, actualTime = TIMESTAMP_2, createdTime = TIMESTAMP_2))
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should correctly round location when creating area bucket from ship based bucket`() {
        val groupedEvents = listOf(
            createShipMovingStartEvent(actualTime = TIMESTAMP_1, createdTime = TIMESTAMP_1, location = Location(0.01, 0.0)),
            createShipMovingStartEvent(actualTime = TIMESTAMP_1, createdTime = TIMESTAMP_1, location = Location(0.0, 0.01)),
            createShipMovingStartEvent(actualTime = TIMESTAMP_1, createdTime = TIMESTAMP_1, location = Location(0.09, 0.0)),
            createShipMovingStartEvent(actualTime = TIMESTAMP_1, createdTime = TIMESTAMP_1, location = Location(0.0, 0.09)),
            createShipMovingStartEvent(actualTime = TIMESTAMP_1, createdTime = TIMESTAMP_1, location = Location(0.09, 0.09)),
            createShipMovingStartEvent(actualTime = TIMESTAMP_1, createdTime = TIMESTAMP_1, location = Location(0.0, -0.0)),
            createShipMovingStartEvent(actualTime = TIMESTAMP_1, createdTime = TIMESTAMP_1, location = Location(-0.0, 0.0))
        )

        val shipBucket = listOf(
            AisEngineBucket(
                fileName = "ship,2023-01,$defaultMmsi.zip",
                data = mapOf(
                    LocalDate.of(2023, 1, 1) to groupedEvents
                )
            )
        )

        val result = service.createAreaBuckets(shipBucket, ************(2023, 1))
        val expected = listOf(
            AisEngineBucket(
                fileName = "area,2023-01,0.0,0.0.zip",
                data = mapOf(
                    LocalDate.of(2023, 1, 1) to groupedEvents
                )
            )
        )

        assertEquals(expected, result)
    }
}
