package nl.teqplay.aisengine.eventhistorymigrator.service

import nl.teqplay.aisengine.eventhistorymigrator.model.PlatformBucket
import nl.teqplay.aisengine.testing.event.defaultMmsi
import nl.teqplay.aisengine.testing.platform.createPlatformBunkerEvent
import nl.teqplay.aisengine.testing.platform.createPlatformShipMovingEvent
import nl.teqplay.aisengine.testing.platform.createTeqplayLocationBasedEvent
import nl.teqplay.platform.model.event.TeqplayEvent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import java.time.Instant

class PlatformBucketServiceTest {
    private val service = PlatformBucketService(mock { })
    private val TIMESTAMP_1 = Instant.ofEpochMilli(1672534800000)
    private val TIMESTAMP_2 = Instant.ofEpochMilli(1672621200000)

    @Test
    fun `should merge platform buckets`() {
        val initialData = listOf(
            PlatformBucket(
                _id = "2023-01-01,$defaultMmsi,BASIC",
                data = listOf<TeqplayEvent>(
                    createTeqplayLocationBasedEvent(type = "area.nlrtm.start", eventTime = TIMESTAMP_1.toEpochMilli()),
                    createPlatformShipMovingEvent(timestamp = TIMESTAMP_1.toEpochMilli())
                )
            ),
            PlatformBucket(
                _id = "2023-01-01,$defaultMmsi,ENCOUNTER",
                data = listOf<TeqplayEvent>(
                    createTeqplayLocationBasedEvent(type = "area.nlrtm.start", eventTime = TIMESTAMP_1.toEpochMilli()),
                    createPlatformBunkerEvent(timestamp = TIMESTAMP_1.toEpochMilli())
                )
            ),
            PlatformBucket(
                _id = "2023-01-01,$defaultMmsi,ENCOUNTER",
                data = listOf<TeqplayEvent>(
                    createTeqplayLocationBasedEvent(type = "area.nlrtm.start", eventTime = TIMESTAMP_1.toEpochMilli())
                )
            ),
            PlatformBucket(
                _id = "2023-01-02,$defaultMmsi,BASIC",
                data = listOf<TeqplayEvent>(
                    createPlatformShipMovingEvent(timestamp = TIMESTAMP_2.toEpochMilli())
                )
            ),
            PlatformBucket(
                _id = "2023-01-02,$defaultMmsi,ENCOUNTER",
                data = listOf<TeqplayEvent>(
                    createPlatformBunkerEvent(timestamp = TIMESTAMP_2.toEpochMilli())
                )
            )
        )

        val result = service.mergeBuckets(initialData)
        val expected = listOf(
            PlatformBucket(
                _id = "2023-01-01,$defaultMmsi",
                data = listOf<TeqplayEvent>(
                    createTeqplayLocationBasedEvent(type = "area.nlrtm.start", eventTime = TIMESTAMP_1.toEpochMilli()),
                    createPlatformBunkerEvent(timestamp = TIMESTAMP_1.toEpochMilli()),
                    createPlatformShipMovingEvent(timestamp = TIMESTAMP_1.toEpochMilli())
                )
            ),
            PlatformBucket(
                _id = "2023-01-02,$defaultMmsi",
                data = listOf<TeqplayEvent>(
                    createPlatformBunkerEvent(timestamp = TIMESTAMP_2.toEpochMilli()),
                    createPlatformShipMovingEvent(timestamp = TIMESTAMP_2.toEpochMilli())
                )
            )
        )

        assertEquals(expected, result)
    }
}
