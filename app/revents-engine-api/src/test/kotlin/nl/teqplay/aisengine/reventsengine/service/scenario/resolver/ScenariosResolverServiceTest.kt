package nl.teqplay.aisengine.reventsengine.service.scenario.resolver

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.PORT
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.service.external.CsiService
import nl.teqplay.aisengine.reventsengine.global.model.InterestRelevantResolvedInternal
import nl.teqplay.aisengine.reventsengine.global.model.InterestRelevantShipInternal
import nl.teqplay.aisengine.reventsengine.global.model.InterestResolvedInternal
import nl.teqplay.aisengine.reventsengine.global.model.InterestShipInternal
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea.Area
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestPolygon
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestShip
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestShip.Identifier
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent.ANCHOR
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent.AREA
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent.BERTH
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent.DIFF
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent.ENCOUNTER
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing.VESSEL_VOYAGE
import nl.teqplay.aisengine.reventsengine.service.scenario.scenarioCreateRequest
import nl.teqplay.aisengine.util.withMargin
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

class ScenariosResolverServiceTest {

    private val scenariosAreaResolverService = mock<ScenariosAreaResolverService>()
    private val csiService = mock<CsiService>()
    private val scenariosResolverService = ScenariosResolverService(scenariosAreaResolverService, csiService)

    private val scenario = ScenarioState(scenarioCreateRequest)

    @Test
    fun resolveEvents() {
        // empty list => all
        assertEquals(
            ScenarioEvent.values().toSet(),
            scenariosResolverService.resolveEvents(emptySet(), emptySet())
        )

        // no dependencies
        val events = setOf(DIFF, AREA, ENCOUNTER)
        assertEquals(
            events,
            scenariosResolverService.resolveEvents(events, emptySet())
        )

        // resolve dependencies for anchor
        assertEquals(
            setOf(AREA, ANCHOR),
            scenariosResolverService.resolveEvents(setOf(ANCHOR), emptySet())
        )

        // resolve dependencies for berth
        assertEquals(
            setOf(AREA, BERTH),
            scenariosResolverService.resolveEvents(setOf(BERTH), emptySet())
        )
    }

    @Test
    fun `resolveEvents - based on post-processing`() {
        // VesselVoyage => all that VesselVoyage needs
        assertEquals(
            setOf(DIFF, ANCHOR, AREA, BERTH, ENCOUNTER),
            scenariosResolverService.resolveEvents(emptySet(), setOf(VESSEL_VOYAGE))
        )
    }

    @Test
    fun `resolveInterests - area`() {
        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any())).thenReturn(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = Identifier(mmsi = 0),
                        window = TimeWindow(Instant.EPOCH, Instant.EPOCH)
                    )
                ),
                polygons = emptyList()
            )
        )

        scenariosResolverService.resolveInterests(
            scenario.copy(interests = listOf(InterestArea(Area(PORT, "NLRTM"))))
        )

        verify(scenariosAreaResolverService, times(1)).resolveInterests(any(), any(), any(), any(), any(), any())
    }

    @Test
    fun `resolveInterests - area - overlapping interests must not be simplified`() {
        val window1 = TimeWindow(Instant.EPOCH, Instant.EPOCH.plusSeconds(10))
        val window2 = TimeWindow(Instant.EPOCH.plusSeconds(5), Instant.EPOCH.plusSeconds(15))
        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any())).thenReturn(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = Identifier(mmsi = 0),
                        window = window1
                    ),
                    InterestShipInternal(
                        ship = Identifier(mmsi = 0),
                        window = window2
                    )
                ),
                polygons = emptyList()
            )
        )

        val res = scenariosResolverService.resolveInterests(
            scenario.copy(interests = listOf(InterestArea(Area(PORT, "NLRTM"))))
        )

        assertEquals(
            listOf(
                InterestRelevantShipInternal(mmsi = 0, imo = null, window = window1),
                InterestRelevantShipInternal(mmsi = 0, imo = null, window = window2)
            ),
            res.ships
        )
        verify(scenariosAreaResolverService, times(1)).resolveInterests(any(), any(), any(), any(), any(), any())
    }

    @Test
    fun `resolveInterests - area - overlapping interests don't need to be simplified if the IMO differs`() {
        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any())).thenReturn(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = Identifier(imo = 0),
                        window = TimeWindow(Instant.EPOCH, Instant.EPOCH.plusSeconds(10))
                    ),
                    InterestShipInternal(
                        ship = Identifier(imo = 1),
                        window = TimeWindow(Instant.EPOCH.plusSeconds(5), Instant.EPOCH.plusSeconds(15))
                    )
                ),
                polygons = emptyList()
            )
        )

        whenever(csiService.getInterestsByImo(any(), any(), any())).thenAnswer {
            val imo = it.getArgument<Int>(0)
            val window = it.getArgument<TimeWindow>(1)
            val windowNoMargin = it.getArgument<TimeWindow>(2)
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = imo,
                    window = window,
                    windowNoMargin = windowNoMargin
                )
            )
        }

        val res = scenariosResolverService.resolveInterests(
            scenario.copy(interests = listOf(InterestArea(Area(PORT, "NLRTM"))))
        )

        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = 0,
                    window = TimeWindow(Instant.EPOCH, Instant.EPOCH.plusSeconds(10))
                ),
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = 1,
                    window = TimeWindow(Instant.EPOCH.plusSeconds(5), Instant.EPOCH.plusSeconds(15))
                )
            ),
            res.ships
        )
        verify(scenariosAreaResolverService, times(1)).resolveInterests(any(), any(), any(), any(), any(), any())
        verify(csiService, times(2)).getInterestsByImo(any(), any(), any())
    }

    @Test
    fun `resolveInterests - empty`() {
        assertThrows<ScenarioCrashedException> {
            scenariosResolverService.resolveInterests(
                scenario.copy(interests = emptyList())
            )
        }
    }

    @Test
    fun `resolveInterests - polygon`() {
        val polygon = Polygon(listOf(Location(0.0, 0.0)))
        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any()))
            .thenReturn(InterestResolvedInternal(emptyList(), listOf(polygon), listOf("NLRTM")))
        assertEquals(
            InterestRelevantResolvedInternal(
                ships = emptyList(),
                unlocodes = setOf("NLRTM"),
                areas = listOf(polygon)
            ),
            scenariosResolverService.resolveInterests(
                scenario.copy(interests = listOf(InterestPolygon(polygon)))
            )
        )
    }

    @Test
    fun `resolveInterests - combining both ship and area-based interests is not allowed`() {
        assertThrows<ScenarioCrashedException> {
            scenariosResolverService.resolveInterests(
                scenario.copy(
                    interests = listOf(
                        InterestShip(Identifier(mmsi = 0)),
                        InterestArea(Area(PORT, "NLRTM"))
                    )
                )
            )
        }
    }

    @Test
    fun `resolveInterests - ship - both MMSI and IMO`() {
        assertThrows<ScenarioCrashedException> {
            whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any()))
                .thenReturn(InterestResolvedInternal.empty())
            scenariosResolverService.resolveInterests(
                scenario.copy(interests = listOf(InterestShip(Identifier(mmsi = 0, imo = 1))))
            )
        }
    }

    @Test
    fun `resolveInterests - ship - no MMSI and IMO`() {
        assertThrows<ScenarioCrashedException> {
            whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any()))
                .thenReturn(InterestResolvedInternal.empty())
            scenariosResolverService.resolveInterests(
                scenario.copy(interests = listOf(InterestShip(Identifier(mmsi = null, imo = null))))
            )
        }
    }

    @Test
    fun `resolveInterests - ship - mmsi, includes windowMargin upon resolving`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(1))
        val windowMargin = Scenario.WindowMargin(14, 7)

        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any()))
            .thenReturn(InterestResolvedInternal.empty())
        val result = scenariosResolverService.resolveInterests(
            scenario.copy(
                window = window,
                windowMargin = windowMargin,
                interests = listOf(InterestShip(Identifier(mmsi = 0)))
            )
        )
        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = null,
                    window = window.withMargin(windowMargin),
                    windowNoMargin = window,
                )
            ),
            result.ships
        )
    }

    @Test
    fun `resolveInterests - ship - mmsi, includes windowMargin upon resolving (enabled resolveShipInterestsForImo)`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(1))
        val windowMargin = Scenario.WindowMargin(14, 7)

        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any()))
            .thenReturn(InterestResolvedInternal.empty())
        whenever(csiService.resolveInterestsByMmsi(any())).thenAnswer {
            return@thenAnswer it.getArgument(0)
        }

        val result = scenariosResolverService.resolveInterests(
            scenario.copy(
                window = window,
                windowMargin = windowMargin,
                interests = listOf(InterestShip(Identifier(mmsi = 0))),
                settings = Scenario.Settings(scenario).copy(
                    resolveShipInterestsForImo = true
                )
            )
        )
        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = null,
                    window = window.withMargin(windowMargin),
                    windowNoMargin = window,
                )
            ),
            result.ships
        )
        verify(csiService, times(1)).resolveInterestsByMmsi(any())
    }

    @Test
    fun `resolveInterests - ship - imo, includes windowMargin upon resolving`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(1))
        val windowMargin = Scenario.WindowMargin(14, 7)

        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any()))
            .thenReturn(InterestResolvedInternal.empty())
        whenever(csiService.getInterestsByImo(any(), any(), any()))
            .thenReturn(listOf(InterestRelevantShipInternal(0, 0, window.withMargin(windowMargin), window)))
        val result = scenariosResolverService.resolveInterests(
            scenario.copy(
                window = window,
                windowMargin = windowMargin,
                interests = listOf(InterestShip(Identifier(imo = 0)))
            )
        )
        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = 0,
                    window = window.withMargin(windowMargin),
                    windowNoMargin = window,
                )
            ),
            result.ships
        )
    }

    @Test
    fun `resolveInterests - area - doesn't add windowMargin since it's applied already by the area resolver`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(1))
        val windowMargin = Scenario.WindowMargin(14, 7)

        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any()))
            .thenReturn(
                InterestResolvedInternal(
                    ships = listOf(
                        InterestShipInternal(
                            ship = Identifier(mmsi = 0),
                            window = window.withMargin(windowMargin)
                        )
                    ),
                    polygons = emptyList()
                )
            )
        val result = scenariosResolverService.resolveInterests(
            scenario.copy(
                window = window,
                windowMargin = windowMargin,
                interests = listOf(InterestArea(Area(PORT, "NLRTM")))
            )
        )
        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = null,
                    window = window.withMargin(windowMargin)
                )
            ),
            result.ships
        )
    }

    @Test
    fun `resolveInterests - area - partial MMSI interest`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(1))
        val windowMargin = Scenario.WindowMargin(14, 7)

        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any())).thenReturn(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = Identifier(mmsi = 0),
                        window = window.withMargin(windowMargin),
                        windowNoMargin = window,
                        partial = true
                    )
                ),
                polygons = emptyList()
            )
        )
        val result = scenariosResolverService.resolveInterests(
            scenario.copy(interests = listOf(InterestArea(Area(PORT, "NLRTM"))))
        )
        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = null,
                    window = window.withMargin(windowMargin),
                    windowNoMargin = window,
                    partial = true
                )
            ),
            result.ships
        )
    }

    @Test
    fun `resolveInterests - area - partial IMO interest`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(1))
        val windowMargin = Scenario.WindowMargin(14, 7)

        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any())).thenReturn(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = Identifier(imo = 0),
                        window = window.withMargin(windowMargin),
                        windowNoMargin = window,
                        partial = true
                    )
                ),
                polygons = emptyList()
            )
        )
        whenever(csiService.getInterestsByImo(any(), any(), any())).thenAnswer {
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = it.getArgument(0),
                    window = it.getArgument(1),
                    windowNoMargin = it.getArgument(2)
                )
            )
        }
        val result = scenariosResolverService.resolveInterests(
            scenario.copy(interests = listOf(InterestArea(Area(PORT, "NLRTM"))))
        )
        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = 0,
                    window = window.withMargin(windowMargin),
                    windowNoMargin = window,
                    partial = true
                )
            ),
            result.ships
        )
    }

    @Test
    fun `resolveInterests - area - partial MMSI interest resolve IMO`() {
        val firstWindow = TimeWindow(
            time = Instant.EPOCH,
            duration = Duration.ofDays(1)
        )
        val secondWindow = TimeWindow(
            time = Instant.EPOCH.plus(2, ChronoUnit.DAYS),
            duration = Duration.ofDays(1)
        )
        whenever(scenariosAreaResolverService.resolveInterests(any(), any(), any(), any(), any(), any())).thenReturn(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = Identifier(mmsi = 0),
                        window = firstWindow,
                        windowNoMargin = firstWindow,
                        partial = true
                    ),
                    InterestShipInternal(
                        ship = Identifier(mmsi = 0),
                        window = secondWindow,
                        windowNoMargin = secondWindow,
                        partial = false
                    )
                ),
                polygons = emptyList()
            )
        )
        whenever(csiService.resolveInterestsByMmsi(any())).thenAnswer {
            it.getArgument<List<InterestRelevantShipInternal>>(0).map { interest ->
                InterestRelevantShipInternal(
                    mmsi = interest.mmsi,
                    imo = 0,
                    window = interest.window,
                    windowNoMargin = interest.windowNoMargin,
                    partial = interest.partial // CsiService should preserve this flag.
                )
            }
        }
        val result = scenariosResolverService.resolveInterests(
            scenario.copy(
                interests = listOf(InterestArea(Area(PORT, "NLRTM"))),
                settings = Scenario.Settings(resolveShipInterestsForImo = true)
            )
        )
        assertEquals(
            listOf(
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = 0,
                    window = firstWindow,
                    windowNoMargin = firstWindow,
                    partial = true
                ),
                InterestRelevantShipInternal(
                    mmsi = 0,
                    imo = 0,
                    window = secondWindow,
                    windowNoMargin = secondWindow,
                    partial = false
                )
            ),
            result.ships
        )
    }
}
