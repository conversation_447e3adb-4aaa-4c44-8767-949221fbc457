package nl.teqplay.aisengine.reventsengine.controller

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.event.model.AisDestinationChangedEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.reventsengine.BaseTest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEventRequests
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioRequestEvent
import nl.teqplay.aisengine.reventsengine.service.event.ActualEventsService
import nl.teqplay.aisengine.reventsengine.service.scenario.ScenariosService
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Instant

@AutoConfigureMockMvc
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class ActualEventsControllerTest(
    private val mockMvc: MockMvc,
    @MockitoBean private val actualEventsService: ActualEventsService,
    @MockitoBean private val scenariosService: ScenariosService,

    private val objectMapper: ObjectMapper,
) : BaseTest() {

    @Test
    @WithMockUser
    fun `fetchEvents - by mmsi`() {
        val result = listOf(
            AisDestinationChangedEvent(
                _id = "",
                ship = AisShipIdentifier(0, 1),
                location = Location(0.0, 0.0),
                oldValue = null,
                newValue = null,
                actualTime = Instant.EPOCH
            )
        )
        whenever(actualEventsService.fetchEventsByMmsi(any(), any(), any(), any())).thenReturn(result)

        mockMvc
            .perform(
                post("/events")
                    .queryParam("scenario", "scenarioId")
                    .queryParam("mmsi", "0")
                    .queryParam("types", "Event1,Event2")
                    .content(objectMapper.writeValueAsString(TimeWindow(Instant.EPOCH, Instant.EPOCH)))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(result)))
    }

    @Test
    @WithMockUser
    fun `fetchEvents - by imo`() {
        val result = listOf(
            AisDestinationChangedEvent(
                _id = "",
                ship = AisShipIdentifier(0, 1),
                location = Location(0.0, 0.0),
                oldValue = null,
                newValue = null,
                actualTime = Instant.EPOCH
            )
        )
        whenever(actualEventsService.fetchEventsByImo(any(), any(), any(), any())).thenReturn(result)

        mockMvc
            .perform(
                post("/events")
                    .queryParam("scenario", "scenarioId")
                    .queryParam("imo", "1")
                    .queryParam("types", "Event1,Event2")
                    .content(objectMapper.writeValueAsString(TimeWindow(Instant.EPOCH, Instant.EPOCH)))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(result)))
    }

    @Test
    @WithMockUser
    fun `fetchEvents - invalid`() {
        mockMvc
            .perform(
                post("/events")
                    .queryParam("scenario", "scenarioId")
                    .queryParam("mmsi", "0")
                    .queryParam("imo", "1")
                    .queryParam("types", "Event1,Event2")
                    .content(objectMapper.writeValueAsString(TimeWindow(Instant.EPOCH, Instant.EPOCH)))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isBadRequest)
    }

    @Test
    @WithMockUser
    fun `fetchScenarioRequests - by mmsi`() {
        val result = listOf(
            ScenarioRequestEvent(
                mmsi = 0,
                scenario = "scenario",
                window = TimeWindow(Instant.EPOCH, Instant.EPOCH)
            )
        )
        whenever(actualEventsService.fetchScenarioRequestsByMmsi(any(), any(), any())).thenReturn(result)
        whenever(scenariosService.getByIds(any())).thenReturn(emptyList())

        mockMvc
            .perform(
                post("/events/scenario/requests")
                    .queryParam("scenario", "scenarioId")
                    .queryParam("mmsi", "0")
                    .content(objectMapper.writeValueAsString(TimeWindow(Instant.EPOCH, Instant.EPOCH)))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(ScenarioEventRequests(result, emptyList()))))
    }

    @Test
    @WithMockUser
    fun `fetchScenarioRequests - by imo`() {
        val result = listOf(
            ScenarioRequestEvent(
                mmsi = 0,
                scenario = "scenario",
                window = TimeWindow(Instant.EPOCH, Instant.EPOCH)
            )
        )
        whenever(actualEventsService.fetchScenarioRequestsByImo(any(), any(), any())).thenReturn(result)
        whenever(scenariosService.getByIds(any())).thenReturn(emptyList())

        mockMvc
            .perform(
                post("/events/scenario/requests")
                    .queryParam("scenario", "scenarioId")
                    .queryParam("imo", "1")
                    .content(objectMapper.writeValueAsString(TimeWindow(Instant.EPOCH, Instant.EPOCH)))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(ScenarioEventRequests(result, emptyList()))))
    }

    @Test
    @WithMockUser
    fun `fetchScenarioRequests - invalid`() {
        mockMvc
            .perform(
                post("/events/scenario/requests")
                    .queryParam("scenario", "scenarioId")
                    .queryParam("mmsi", "0")
                    .queryParam("imo", "1")
                    .queryParam("types", "Event1,Event2")
                    .content(objectMapper.writeValueAsString(TimeWindow(Instant.EPOCH, Instant.EPOCH)))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isBadRequest)
    }
}
