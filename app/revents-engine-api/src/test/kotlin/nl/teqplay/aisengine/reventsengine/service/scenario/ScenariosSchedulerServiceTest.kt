package nl.teqplay.aisengine.reventsengine.service.scenario

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CANCELLED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CRASHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FINISHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_ASSIGN_CONTEXT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_END
import nl.teqplay.aisengine.reventsengine.common.service.cleanup.ScenariosCleanupTaskService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosPhaseService
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class ScenariosSchedulerServiceTest {

    private val scenariosDataSource = mock<ScenariosDataSource>()
    private val scenariosQueueService = mock<ScenariosQueueService>()
    private val scenariosCleanupTaskService = ScenariosCleanupTaskService(scenariosDataSource)
    private val scenariosPhaseService = mock<ScenariosPhaseService>()
    private val scenariosBaseSchedulerService =
        ScenariosBaseSchedulerService(scenariosDataSource, scenariosPhaseService)
    private val scenariosSchedulerService = ScenariosSchedulerService(
        scenariosQueueService, scenariosCleanupTaskService, scenariosBaseSchedulerService
    )

    private val scenario = ScenarioState(scenarioCreateRequest)

    @Test
    fun `loopOverScenarios - with initial`() {
        whenever(scenariosQueueService.getCurrentScenario()).thenReturn(null)
        whenever(scenariosPhaseService.runPhase(any()))
            .thenReturn(ScenariosPhaseService.RunPhaseResult(null, true))
        doNothing().whenever(scenariosDataSource).save(any())

        scenariosSchedulerService.loopOverScenarios(scenario)

        val amountOfPhases = ScenarioState.InternalPhase.values()
            .filter { it <= QUEUED_END }
            .size
        verify(scenariosPhaseService, times(amountOfPhases)).runPhase(any())
        verify(scenariosDataSource, times(amountOfPhases)).save(any())
        verify(scenariosQueueService, times(1)).getCurrentScenario()
    }

    @Test
    fun loopOverScenarios() {
        whenever(scenariosQueueService.getCurrentScenario())
            .thenReturn(scenario)
            .thenReturn(null)
        whenever(scenariosPhaseService.runPhase(any()))
            .thenReturn(ScenariosPhaseService.RunPhaseResult(null, true))
        doNothing().whenever(scenariosDataSource).save(any())

        scenariosSchedulerService.loopOverScenarios()

        val amountOfPhases = ScenarioState.InternalPhase.values()
            .filter { it <= QUEUED_END }
            .size
        verify(scenariosPhaseService, times(amountOfPhases)).runPhase(any())
        verify(scenariosDataSource, times(amountOfPhases)).save(any())
        verify(scenariosQueueService, times(2)).getCurrentScenario()
    }

    @Test
    fun `loopOverScenarios - skip to end`() {
        whenever(scenariosQueueService.getCurrentScenario())
            .thenReturn(scenario)
            .thenReturn(null)
        whenever(scenariosPhaseService.runPhase(any()))
            .thenReturn(ScenariosPhaseService.RunPhaseResult(null, true, FINISHED))
        doNothing().whenever(scenariosDataSource).save(any())

        scenariosSchedulerService.loopOverScenarios()

        verify(scenariosPhaseService, times(1)).runPhase(any())
        verify(scenariosDataSource, times(1)).save(any())
        verify(scenariosQueueService, times(2)).getCurrentScenario()
    }

    @Test
    fun `loopOverScenarios - crash saves CRASHED state, but preserves original phase it crashed on`() {
        whenever(scenariosQueueService.getCurrentScenario())
            .thenReturn(scenario.copy(phase = PROGRESSING))
            .thenReturn(null)
        whenever(scenariosPhaseService.runPhase(any()))
            // First scenario should crash.
            .thenThrow(ScenarioCrashedException(ScenarioCrashReason.REVENTS_ENGINE_INCORRECT_SCENARIO))
            // Teardown scenario can just skip to the end.
            .thenReturn(ScenariosPhaseService.RunPhaseResult(null, true, FINISHED))
        doNothing().whenever(scenariosDataSource).save(any())

        var saveCount = 0
        whenever(scenariosDataSource.save(any())).thenAnswer {
            val scenario = it.getArgument<ScenarioState>(0)
            when (saveCount++) {
                // First scenario to be saved should indicate it crashed.
                0 -> assertEquals(CRASHED, scenario.phase)
                // Saving the teardown scenario should be at the end of progressing, to perform all teardown steps.
                1 -> assertEquals(PROGRESSING_END, scenario.phase)
            }
            return@thenAnswer scenario
        }

        scenariosSchedulerService.loopOverScenarios()

        verify(scenariosPhaseService, times(2)).runPhase(any())
        verify(scenariosDataSource, times(3)).save(any())
        verify(scenariosQueueService, times(2)).getCurrentScenario()
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `loopOverScenarios - cancelled should result in a crash and teardown`(
        moveToNextPhase: Boolean
    ) {
        val mockedScenariosCleanupTaskService = mock<ScenariosCleanupTaskService>()
        val scenariosSchedulerService = ScenariosSchedulerService(
            scenariosQueueService, mockedScenariosCleanupTaskService, scenariosBaseSchedulerService
        )

        var savedScenario = 0

        whenever(scenariosQueueService.getCurrentScenario())
            .thenReturn(scenario.copy(phase = INITIALIZING_ASSIGN_CONTEXT))
            .thenReturn(null)
        whenever(scenariosPhaseService.runPhase(any()))
            .thenReturn(ScenariosPhaseService.RunPhaseResult(null, moveToNextPhase))

        whenever(scenariosDataSource.save(any())).thenAnswer {
            val argScenario = it.getArgument<ScenarioState>(0)
            if (argScenario.id == scenario.id) savedScenario++
            return@thenAnswer Unit
        }

        whenever(scenariosDataSource.get(any())).thenReturn(scenario.copy(phase = CANCELLED))

        // When create and save is called, we instead don't save and only create the teardown scenario.
        whenever(mockedScenariosCleanupTaskService.createAndSaveTeardownScenario(any())).thenAnswer {
            val scenario = it.getArgument<ScenarioState>(0)
            val teardownScenario = this.scenariosCleanupTaskService.createTeardownScenario(scenario)
            if (moveToNextPhase) {
                assertEquals(PROGRESSING_END, teardownScenario.phase)
            }
            teardownScenario
        }

        scenariosSchedulerService.loopOverScenarios()

        // Ensure we don't save the cancelled scenario.
        // Although the teardown scenario should have been saved, we bypass that and only create it without saving.
        assertEquals(0, savedScenario)

        verify(mockedScenariosCleanupTaskService, times(1)).createAndSaveTeardownScenario(any())
        verify(scenariosQueueService, times(2)).getCurrentScenario()
    }
}
