package nl.teqplay.aisengine.reventsengine.service.vesselvoyage

import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioMetadata
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosMetadataService
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageService
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntry
import nl.teqplay.aisengine.util.withMargin
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.PortAreaVisit
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.clearInvocations
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class VesselVoyageMergingServiceTest {

    private val vesselVoyageStorageService = mock<VesselVoyageStorageService>()
    private val scenariosMetadataService = mock<ScenariosMetadataService>()
    private val vesselVoyageMergingService = VesselVoyageMergingService(
        vesselVoyageStorageService,
        scenariosMetadataService
    )

    private val window1 = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
    private val window2 = TimeWindow(Instant.EPOCH.plus(10, ChronoUnit.DAYS), Duration.ofDays(7))
    private val window3 = TimeWindow(Instant.EPOCH.plus(20, ChronoUnit.DAYS), Duration.ofDays(7))

    @BeforeEach
    fun beforeEach() {
        clearInvocations(
            vesselVoyageStorageService,
            scenariosMetadataService
        )
    }

    @Test
    fun `fetchEntriesForMerging - invalid IMO`() {
        assertThrows<BadRequestException> {
            vesselVoyageMergingService.fetchEntriesForMerging("scenario", "imo")
        }
    }

    @Test
    fun `fetchEntriesForMerging - multiple interests should result into multiple merge entries`() {
        whenever(vesselVoyageStorageService.getInterestsByScenarioAndImo(any(), any())).thenReturn(
            listOf(
                ScenarioInterestVesselVoyage(0, window1, window1, false, "scenario"),
                ScenarioInterestVesselVoyage(0, window2, window2, false, "scenario"),
                ScenarioInterestVesselVoyage(0, window3, window3, true, "scenario"),
            )
        )
        whenever(vesselVoyageStorageService.fetchEntries(any(), any(), any())).thenReturn(emptyList())

        val actual = vesselVoyageMergingService.fetchEntriesForMerging("scenario", "0")

        val expected = listOf(
            MergeEntry(window = window1, entries = emptyList()),
            MergeEntry(window = window2, entries = emptyList())
        )
        assertEquals(expected, actual)

        verify(vesselVoyageStorageService, times(1)).getInterestsByScenarioAndImo(eq("scenario"), eq(0))
    }

    @Test
    fun `fetchEntriesForMerging - filter visits if unlocodes are specified in the metadata`() {
        whenever(vesselVoyageStorageService.getInterestsByScenarioAndImo(any(), any())).thenReturn(
            listOf(ScenarioInterestVesselVoyage(0, window1, window1, false, "scenario"))
        )
        whenever(scenariosMetadataService.getMetadataByScenario(any())).thenReturn(
            ScenarioMetadata(
                id = "scenario",
                unlocodes = setOf("NLRTM"),
                areas = emptyList()
            )
        )

        var expectedVisit: Visit? = null
        whenever(vesselVoyageStorageService.fetchEntries(any(), any(), any())).thenReturn(
            build {
                createVoyage(Duration.ofDays(1))
                expectedVisit = createVisit(Duration.ofDays(1), portId = "NLRTM")
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1), portId = "USHOU")
            }
        )

        val actual = vesselVoyageMergingService.fetchEntriesForMerging("scenario", "0")

        val expected = listOf(
            MergeEntry(window = window1, entries = listOf(expectedVisit!!))
        )
        assertEquals(expected, actual)

        verify(scenariosMetadataService, times(1)).getMetadataByScenario(eq("scenario"))
        verify(vesselVoyageStorageService, times(1)).getInterestsByScenarioAndImo(eq("scenario"), eq(0))
    }

    data class FetchEntriesForMergingTestData(
        val message: String,
        val windowMargin: Scenario.WindowMargin,
        val windowNoMargin: TimeWindow,
        val entries: List<Entry>,
        val expected: List<Entry>,
    )

    @ParameterizedTest
    @MethodSource("fetchEntriesForMergingTestData")
    fun `fetchEntriesForMerging - no metadata`(data: FetchEntriesForMergingTestData) {
        val window = data.windowNoMargin.withMargin(data.windowMargin)

        whenever(vesselVoyageStorageService.getInterestsByScenarioAndImo(any(), any()))
            .thenReturn(listOf(ScenarioInterestVesselVoyage(0, window, data.windowNoMargin, false, "scenario")))
        whenever(vesselVoyageStorageService.fetchEntries(any(), any(), any()))
            .thenReturn(data.entries)
        whenever(scenariosMetadataService.getMetadataByScenario(any()))
            .thenReturn(null)

        val actual = vesselVoyageMergingService.fetchEntriesForMerging("scenario", "0").first()
        val expected = MergeEntry(window = window, entries = data.expected)
        assertThat(actual).isEqualTo(expected)
    }

    @ParameterizedTest
    @MethodSource("fetchEntriesForMergingTestData")
    fun `fetchEntriesForMerging - metadata with empty unlocodes`(data: FetchEntriesForMergingTestData) {
        val window = data.windowNoMargin.withMargin(data.windowMargin)

        whenever(vesselVoyageStorageService.getInterestsByScenarioAndImo(any(), any()))
            .thenReturn(listOf(ScenarioInterestVesselVoyage(0, window, data.windowNoMargin, false, "scenario")))
        whenever(vesselVoyageStorageService.fetchEntries(any(), any(), any()))
            .thenReturn(data.entries)
        whenever(scenariosMetadataService.getMetadataByScenario(any())).thenReturn(
            ScenarioMetadata(
                id = "scenario",
                unlocodes = emptySet(),
                areas = emptyList()
            )
        )

        val actual = vesselVoyageMergingService.fetchEntriesForMerging("scenario", "0").first()
        val expected = MergeEntry(window = window, entries = data.expected)
        assertThat(actual).isEqualTo(expected)
    }

    private fun fetchEntriesForMergingTestData() = Stream.of(
        FetchEntriesForMergingTestData(
            message = "empty",
            windowMargin = Scenario.WindowMargin(),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            entries = emptyList(),
            expected = emptyList(),
        ),
        FetchEntriesForMergingTestData(
            message = "visit within window stays the same",
            windowMargin = Scenario.WindowMargin(),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            entries = build {
                createVisit(Duration.ofDays(1))
            },
            expected = build {
                createVisit(Duration.ofDays(1))
            },
        ),
        FetchEntriesForMergingTestData(
            message = "unfinished visit within window is not used",
            windowMargin = Scenario.WindowMargin(),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            entries = build {
                createVisit(Duration.ofDays(1), finished = false)
            },
            expected = emptyList(),
        ),
        FetchEntriesForMergingTestData(
            message = "unfinished entries are not used",
            windowMargin = Scenario.WindowMargin(),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
            entries = build {
                createVoyage(Duration.ofDays(1), finished = false)
                createVisit(Duration.ofDays(1), finished = false)
                createVoyage(Duration.ofDays(1), finished = false)
            },
            expected = emptyList(),
        ),
        FetchEntriesForMergingTestData(
            message = "voyages at both ends should be removed",
            windowMargin = Scenario.WindowMargin(),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
            entries = build {
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
            },
            expected = build(offset = Duration.ofDays(1)) {
                createVisit(Duration.ofDays(1))
            },
        ),
        FetchEntriesForMergingTestData(
            // First/last visits might not have enough data, therefore we need to remove them.
            message = "margin - remove first/last visits at the edges outside window",
            windowMargin = Scenario.WindowMargin(2, 2),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
            entries = build(offset = Duration.ofDays(-2)) {
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1)) // <-- only this should be kept
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
            },
            expected = build(offset = Duration.ofDays(1)) {
                createVisit(Duration.ofDays(1))
            },
        ),
        FetchEntriesForMergingTestData(
            // First/last visits might not have enough data, therefore we need to remove them.
            message = "margin - remove first/last visits at the edges outside window (more data)",
            windowMargin = Scenario.WindowMargin(4, 4),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
            entries = build(offset = Duration.ofDays(-4)) {
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1)) // <--
                createVoyage(Duration.ofDays(1)) //
                createVisit(Duration.ofDays(1)) // Within this range should be kept.
                createVoyage(Duration.ofDays(1)) //
                createVisit(Duration.ofDays(1)) // <--
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
            },
            expected = build(offset = Duration.ofDays(-1)) {
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
            },
        ),
    )

    private fun build(
        offset: Duration = Duration.ZERO,
        action: Builder.() -> Unit,
    ): List<Entry> {
        val builder = Builder(offset)
        action(builder)
        return builder.entries
    }

    private class Builder(
        private var duration: Duration,
    ) {
        val entries: MutableList<Entry> = mutableListOf()

        private fun time(): ZonedDateTime = Instant.EPOCH.plus(duration).atZone(ZoneOffset.UTC)

        private fun time(duration: Duration): ZonedDateTime {
            val time = time()
            this.duration += duration
            return time
        }

        fun createVisit(
            duration: Duration,
            finished: Boolean = true,
            portId: String = "portId"
        ): Visit {
            val visit = Visit(
                _id = "",
                imo = "0",
                mmsi = "0",
                anchorAreas = emptyList(),
                portAreas = listOf(
                    PortAreaVisit(
                        portId = portId,
                        startEventId = "",
                        startTime = time(),
                        startLocation = Location(0.0, 0.0),
                        startDraught = null,
                        endEventId = "",
                        endTime = time(duration),
                        endLocation = null,
                        endDraught = null
                    )
                ),
                berthAreas = emptyList(),
                passThroughAreas = null,
                esof = null,
                eta = null,
                previousEntryId = null,
                nextEntryId = null,
                finished = finished
            )
            entries.add(visit)
            return visit
        }

        fun createVoyage(
            duration: Duration,
            finished: Boolean = true
        ): Voyage {
            val voyage = Voyage(
                _id = "",
                imo = "0",
                mmsi = "0",
                startTime = time(),
                startPortIds = emptyList(),
                endTime = time(duration),
                endPortIds = emptyList(),
                passThroughAreas = null,
                nonMatchingAnchorAreas = null,
                esof = null,
                eta = null,
                previousEntryId = null,
                nextEntryId = null,
                finished = finished
            )
            entries.add(voyage)
            return voyage
        }
    }
}
