package nl.teqplay.aisengine.reventsengine.controller

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.reventsengine.BaseTest
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioMetadata
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.model.interest.InterestRelevantShip
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCancelAllResponse
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioQueryResponse
import nl.teqplay.aisengine.reventsengine.service.scenario.ScenariosService
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Instant
import java.time.temporal.ChronoUnit

@AutoConfigureMockMvc
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class ScenariosControllerTest(
    private val mockMvc: MockMvc,
    @MockitoBean private val scenariosService: ScenariosService,

    private val objectMapper: ObjectMapper,
) : BaseTest() {

    private val scenarioCreateRequest = ScenarioCreateRequest(
        window = TimeWindow(Instant.EPOCH, Instant.EPOCH),
        interests = emptyList()
    )
    private val childScenarios = emptyList<ScenarioState>()
    private val inheritedScenarios = emptyList<ScenarioState>()
    private val response = ScenarioState(scenarioCreateRequest).toResponse(childScenarios, inheritedScenarios).copy(
        events = ScenarioEvent.values().toSet()
    )
    private val expectedResponse = response.copy(
        events = setOf(
            ScenarioEvent.DIFF,
            // TODO: add once PTO supports this field
            // ScenarioEvent.STOP,
            ScenarioEvent.ANCHOR,
            ScenarioEvent.AREA,
            ScenarioEvent.BERTH,
            ScenarioEvent.ENCOUNTER
        )
    )

    @Test
    @WithMockUser
    fun create() {
        whenever(scenariosService.create(any())).thenReturn(response)
        mockMvc
            .perform(
                post("/scenario")
                    .content(objectMapper.writeValueAsString(scenarioCreateRequest))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(expectedResponse)))
    }

    @Test
    @WithMockUser
    fun createForAfterwardsPostProcessing() {
        whenever(scenariosService.createForAfterwardsPostProcessing(any(), any())).thenReturn(response)
        mockMvc
            .perform(
                post("/scenario/{id}/post-processing", "scenarioId")
                    .content(objectMapper.writeValueAsString(ScenarioPostProcessing.values().toSet()))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(expectedResponse)))
    }

    @Test
    @WithMockUser
    fun get() {
        whenever(scenariosService.get(any())).thenReturn(response)
        mockMvc
            .perform(get("/scenario/{id}", "scenarioId"))
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(expectedResponse)))
    }

    @Test
    @WithMockUser
    fun `get - not found`() {
        whenever(scenariosService.get(any())).thenReturn(null)
        mockMvc
            .perform(get("/scenario/{id}", "scenarioId"))
            .andExpect(status().isNotFound)
    }

    @Test
    @WithMockUser
    fun prune() {
        whenever(scenariosService.prune(any())).thenReturn(response)
        mockMvc
            .perform(delete("/scenario/{id}/prune", "scenarioId"))
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(expectedResponse)))
    }

    @Test
    @WithMockUser
    fun `prune - not found`() {
        whenever(scenariosService.prune(any())).thenReturn(null)
        mockMvc
            .perform(delete("/scenario/{id}/prune", "scenarioId"))
            .andExpect(status().isNotFound)
    }

    @Test
    @WithMockUser
    fun `cancel - not found`() {
        whenever(scenariosService.cancel(any())).thenReturn(null)
        mockMvc
            .perform(put("/scenario/{id}/cancel", "scenarioId"))
            .andExpect(status().isNotFound)
    }

    @Test
    @WithMockUser
    fun cancel() {
        whenever(scenariosService.cancel(any())).thenReturn(response)
        mockMvc
            .perform(put("/scenario/{id}/cancel", "scenarioId"))
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(expectedResponse)))
    }

    @Test
    @WithMockUser
    fun query() {
        val queryResponse = ScenarioQueryResponse(
            matches = listOf(response),
            overlaps = listOf(response)
        )
        whenever(scenariosService.query(any())).thenReturn(queryResponse)

        val expectedQueryResponse = ScenarioQueryResponse(
            matches = listOf(expectedResponse),
            overlaps = listOf(expectedResponse)
        )
        mockMvc
            .perform(
                post("/scenario/query")
                    .content(objectMapper.writeValueAsString(scenarioCreateRequest))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(expectedQueryResponse)))
    }

    @Test
    @WithMockUser
    fun getInterests() {
        val result = listOf(
            InterestRelevantShip(
                ship = AisShipIdentifier(0, 1),
                window = TimeWindow(
                    from = Instant.EPOCH.minus(1, ChronoUnit.DAYS),
                    to = Instant.EPOCH.plus(2, ChronoUnit.DAYS)
                ),
                windowNoMargin = TimeWindow(
                    from = Instant.EPOCH,
                    to = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
                )
            )
        )
        whenever(scenariosService.getInterests(any())).thenReturn(result)
        mockMvc
            .perform(get("/scenario/{id}/interests", "scenarioId"))
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(result)))
    }

    @Test
    @WithMockUser
    fun `getInterests - not found`() {
        whenever(scenariosService.getInterests(any())).thenReturn(null)
        mockMvc
            .perform(get("/scenario/{id}/interests", "scenarioId"))
            .andExpect(status().isNotFound)
    }

    @Test
    @WithMockUser
    fun getMetadata() {
        val result = ScenarioMetadata(
            id = "",
            unlocodes = emptySet(),
            areas = emptyList()
        )
        whenever(scenariosService.getMetadata(any())).thenReturn(result)
        mockMvc
            .perform(get("/scenario/{id}/metadata", "scenarioId"))
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(result)))
    }

    @Test
    @WithMockUser
    fun `getMetadata - not found`() {
        whenever(scenariosService.getMetadata(any())).thenReturn(null)
        mockMvc
            .perform(get("/scenario/{id}/metadata", "scenarioId"))
            .andExpect(status().isNotFound)
    }

    @Test
    @WithMockUser
    fun cancelAll() {
        val cancelAllResponse = ScenarioCancelAllResponse(
            totalCancelled = 2,
            cancelledScenarioIds = listOf("scenario1", "scenario2"),
        )
        whenever(scenariosService.cancelAll()).thenReturn(cancelAllResponse)
        mockMvc
            .perform(put("/scenario/cancel-all"))
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(cancelAllResponse)))
    }
}
