package nl.teqplay.aisengine.reventsengine.service.scenario

import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestRelevantShip
import nl.teqplay.aisengine.reventsengine.common.service.external.CsiService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosMetadataService
import nl.teqplay.aisengine.reventsengine.model.interest.InterestRelevantShip
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestPolygon
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing.VESSEL_VOYAGE
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse
import nl.teqplay.aisengine.reventsengine.service.scenario.resolver.ScenariosResolverService
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.argThat
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Instant

class ScenariosServiceTest {

    private val scenariosDataSource = mock<ScenariosDataSource>()
    private val scenariosSchedulerService = mock<ScenariosSchedulerService>()
    private val scenariosResolverService = mock<ScenariosResolverService>()
    private val scenariosInterestsService = mock<ScenariosInterestsService>()
    private val scenariosMetadataService = mock<ScenariosMetadataService>()
    private val scenariosPruneService = mock<ScenariosPruneService>()
    private val csiService = mock<CsiService>()

    private val scenariosService = ScenariosService(
        scenariosDataSource, scenariosSchedulerService,
        scenariosResolverService, scenariosInterestsService, scenariosMetadataService, scenariosPruneService
    )

    private val childScenarios = emptyList<ScenarioState>()
    private val inheritedScenarios = emptyList<ScenarioState>()

    private val window = TimeWindow(Instant.EPOCH, Instant.EPOCH)

    @Test
    fun create() {
        val scenario = ScenarioCreateRequest(
            window = window,
            windowMargin = Scenario.WindowMargin(),
            events = emptySet(),
            postProcessing = setOf(VESSEL_VOYAGE),
            guarantees = emptySet(),
            interests = emptyList()
        )

        val allEvents = ScenarioEvent.values().toSet()
        whenever(scenariosResolverService.resolveEvents(eq(emptySet()), eq(setOf(VESSEL_VOYAGE))))
            .thenReturn(allEvents)

        doNothing().whenever(scenariosDataSource).save(any())
        doNothing().whenever(scenariosSchedulerService).loopOverScenarios(anyOrNull())

        val response = scenariosService.create(scenario)

        assertEquals(allEvents, response.events)

        verify(scenariosDataSource, times(1)).save(any())
        verify(scenariosSchedulerService, times(1)).loopOverScenarios(anyOrNull())
    }

    @Test
    fun `create - guarantees VesselVoyage merging`() {
        val scenario = ScenarioCreateRequest(
            window = window,
            windowMargin = Scenario.WindowMargin(),
            events = emptySet(),
            postProcessing = emptySet(),
            guarantees = setOf(VESSEL_VOYAGE_MERGING),
            interests = emptyList()
        )

        whenever(scenariosResolverService.resolveEvents(any(), any())).thenCallRealMethod()

        doNothing().whenever(scenariosDataSource).save(any())
        doNothing().whenever(scenariosSchedulerService).loopOverScenarios(anyOrNull())

        val response = scenariosService.create(scenario)

        assertEquals(VESSEL_VOYAGE.events, response.events)
        assertEquals(setOf(VESSEL_VOYAGE), response.postProcessing)
        assertEquals(VESSEL_VOYAGE_MERGING.adjustWindowMargin(Scenario.WindowMargin()), response.windowMargin)
        assertEquals(VESSEL_VOYAGE_MERGING.adjustSettings(Scenario.Settings()), response.settings)

        verify(scenariosDataSource, times(1)).save(any())
        verify(scenariosSchedulerService, times(1)).loopOverScenarios(anyOrNull())
    }

    @Test
    fun `create - guarantees VesselVoyage merging - don't resolve events`() {
        val settings = Scenario.Settings(
            resolveScenarioEventsByPostProcessing = false
        )
        val scenario = ScenarioCreateRequest(
            window = window,
            windowMargin = Scenario.WindowMargin(),
            events = setOf(ScenarioEvent.ANCHOR),
            postProcessing = emptySet(),
            guarantees = setOf(VESSEL_VOYAGE_MERGING),
            interests = emptyList(),
            settings = settings
        )

        whenever(scenariosResolverService.resolveEvents(any(), any())).thenCallRealMethod()
        doNothing().whenever(scenariosDataSource).save(any())
        doNothing().whenever(scenariosSchedulerService).loopOverScenarios(anyOrNull())

        val response = scenariosService.create(scenario)

        assertEquals(setOf(ScenarioEvent.ANCHOR, ScenarioEvent.AREA), response.events)
        assertEquals(setOf(VESSEL_VOYAGE), response.postProcessing)
        assertEquals(VESSEL_VOYAGE_MERGING.adjustWindowMargin(Scenario.WindowMargin()), response.windowMargin)
        assertEquals(VESSEL_VOYAGE_MERGING.adjustSettings(settings), response.settings)

        verify(scenariosDataSource, times(1)).save(any())
        verify(scenariosSchedulerService, times(1)).loopOverScenarios(anyOrNull())
    }

    @Test
    fun `create - fails if VesselVoyage merging guarantee for interests is not met`() {
        val scenario = ScenarioCreateRequest(
            window = window,
            windowMargin = Scenario.WindowMargin(),
            events = emptySet(),
            postProcessing = emptySet(),
            guarantees = setOf(VESSEL_VOYAGE_MERGING),
            interests = listOf(
                // VesselVoyage merging doesn't support polygon interests, should fail the scenario creation.
                InterestPolygon(polygon = Polygon())
            )
        )

        doNothing().whenever(scenariosDataSource).save(any())
        doNothing().whenever(scenariosSchedulerService).loopOverScenarios(anyOrNull())

        assertThrows<IllegalArgumentException> {
            scenariosService.create(scenario)
        }
    }

    @Test
    fun `createForAfterwardsPostProcessing - should specify post-processing`() {
        assertThrows<BadRequestException> {
            scenariosService.createForAfterwardsPostProcessing("id", emptySet())
        }
    }

    @Test
    fun `createForAfterwardsPostProcessing - scenario should exist`() {
        whenever(scenariosDataSource.get(any())).thenReturn(null)
        assertThrows<NotFoundException> {
            scenariosService.createForAfterwardsPostProcessing("id", setOf(VESSEL_VOYAGE))
        }
    }

    @Test
    fun `createForAfterwardsPostProcessing - events should match pre-requisites of post-processing step`() {
        val state = ScenarioState(scenarioCreateRequest.copy(events = emptySet()))
        whenever(scenariosResolverService.resolveEvents(any(), any())).thenCallRealMethod()
        whenever(scenariosDataSource.get(any())).thenReturn(state)
        assertThrows<BadRequestException> {
            scenariosService.createForAfterwardsPostProcessing("id", setOf(VESSEL_VOYAGE))
        }
    }

    @Test
    fun `createForAfterwardsPostProcessing - scenario that's inherited should be in finished state`() {
        val state = ScenarioState(scenarioCreateRequest).copy(
            events = ScenarioEvent.values().toSet(),
            phase = ScenarioState.InternalPhase.CRASHED
        )
        whenever(scenariosResolverService.resolveEvents(any(), any())).thenCallRealMethod()
        whenever(scenariosDataSource.get(any())).thenReturn(state)
        assertThrows<BadRequestException> {
            scenariosService.createForAfterwardsPostProcessing("id", setOf(VESSEL_VOYAGE))
        }
    }

    @Test
    fun `createForAfterwardsPostProcessing - newly created scenario should inherit and add postProcessing step`() {
        val state = ScenarioState(scenarioCreateRequest).copy(
            events = ScenarioEvent.values().toSet(),
            phase = ScenarioState.InternalPhase.FINISHED,
        )
        whenever(scenariosResolverService.resolveEvents(any(), any())).thenCallRealMethod()
        whenever(scenariosDataSource.get(any())).thenReturn(state)

        // Capture saved state.
        var newState: ScenarioState? = null
        whenever(scenariosDataSource.save(any())).then {
            newState = it.getArgument(0)
            return@then Unit
        }

        val id = "id"
        val postProcessing = setOf(VESSEL_VOYAGE)
        val response = scenariosService.createForAfterwardsPostProcessing(id, postProcessing)
        assertNotNull(response)
        assertThat(newState)
            .hasFieldOrPropertyWithValue("inherit", id)
            .hasFieldOrPropertyWithValue("postProcessing", postProcessing)
    }

    @Test
    fun get() {
        val state = mock<ScenarioState>().apply {
            whenever(phase).thenReturn(ScenarioState.InternalPhase.FINISHED)
        }
        whenever(state.toResponse(childScenarios, inheritedScenarios)).thenReturn(mock())
        whenever(scenariosDataSource.get(any())).thenReturn(state)
        assertNotNull(scenariosService.get(""))
    }

    @Test
    fun `prune - does nothing if already pruned`() {
        val state = mock<ScenarioState>().apply {
            whenever(phase).thenReturn(ScenarioState.InternalPhase.PRUNED)
        }
        whenever(scenariosDataSource.get(any())).thenReturn(state)

        scenariosService.prune("")

        verify(scenariosPruneService, never()).prune(any())
    }

    @ParameterizedTest
    @EnumSource(ScenarioState.InternalPhase::class)
    fun `prune - calls prune if not yet pruned, but only if not queued,initializing,progressing`(internalPhase: ScenarioState.InternalPhase) {
        val pruningAllowedPhases = setOf(
            ScenarioState.InternalPhase.FINISHED,
            ScenarioState.InternalPhase.CRASHED,
            ScenarioState.InternalPhase.FORKED,
            ScenarioState.InternalPhase.CANCELLED,
            ScenarioState.InternalPhase.PRUNED, // results in noop
        )

        val state = mock<ScenarioState>().apply {
            whenever(phase).thenReturn(internalPhase)
        }
        whenever(scenariosDataSource.get(any())).thenReturn(state)
        whenever(scenariosDataSource.getChildScenarios(anyString())).thenReturn(emptyList())
        whenever(scenariosDataSource.getInheritedScenarios(anyString())).thenReturn(emptyList())
        whenever(scenariosPruneService.prune(any())).thenReturn(state)

        // Pruning is only allowed on final phases.
        if (internalPhase !in pruningAllowedPhases) {
            assertThrows<BadRequestException> {
                scenariosService.prune("")
            }
            return
        }

        scenariosService.prune("")

        // Pruning an already pruned scenario is a noop.
        if (internalPhase == ScenarioState.InternalPhase.PRUNED) {
            verify(scenariosPruneService, never()).prune(any())
        } else {
            verify(scenariosPruneService, times(1)).prune(any())
        }
    }

    @Test
    fun `cancel - does nothing if already cancelled`() {
        val state = mock<ScenarioState>().apply {
            whenever(phase).thenReturn(ScenarioState.InternalPhase.CANCELLED)
        }
        whenever(scenariosDataSource.get(any())).thenReturn(state)

        scenariosService.cancel("")
        verify(scenariosDataSource, never()).save(any())
    }

    @ParameterizedTest
    @EnumSource(ScenarioState.InternalPhase::class)
    fun `cancel - calls cancel if not yet cancelled, but only if not already in final state`(internalPhase: ScenarioState.InternalPhase) {
        val cancelNotAllowedPhases = ScenarioState.InternalPhase.FINAL_PHASES - ScenarioState.InternalPhase.CANCELLED

        val state = ScenarioState(scenarioCreateRequest).copy(phase = internalPhase)
        whenever(scenariosDataSource.get(any())).thenReturn(state)
        whenever(scenariosDataSource.getChildScenarios(anyString())).thenReturn(emptyList())
        whenever(scenariosDataSource.getInheritedScenarios(anyString())).thenReturn(emptyList())
        whenever(scenariosPruneService.prune(any())).thenReturn(state)

        // Cancelling is only allowed on non-final phases.
        if (internalPhase in cancelNotAllowedPhases) {
            assertThrows<BadRequestException> {
                scenariosService.cancel("")
            }
            return
        }

        val response = scenariosService.cancel("")
        assertEquals(ScenarioResponse.Phase.CANCELLED, response?.phase)

        // Cancelling an already cancelled scenario is a noop.
        if (internalPhase == ScenarioState.InternalPhase.CANCELLED) {
            verify(scenariosDataSource, never()).save(any())
        } else {
            assertNotNull(response?.cancelled)
            verify(scenariosDataSource, times(1)).save(any())
        }
    }

    @Test
    fun query() {
        val stretchedScenarioId = "stretchedScenarioId"
        val stretchedScenario = ScenarioState(
            ScenarioCreateRequest(
                window = TimeWindow(
                    from = Instant.EPOCH,
                    to = Instant.EPOCH.plusSeconds(6)
                ),
                interests = emptyList()
            )
        ).copy(id = stretchedScenarioId, queued = Instant.EPOCH)

        val innerScenarioCreateRequest = ScenarioCreateRequest(
            window = TimeWindow(
                from = Instant.EPOCH.plusSeconds(2),
                to = Instant.EPOCH.plusSeconds(4)
            ),
            interests = emptyList()
        )
        val innerScenarioId = "innerScenarioId"
        val innerScenario = ScenarioState(innerScenarioCreateRequest).copy(
            id = innerScenarioId,
            queued = Instant.EPOCH.plusSeconds(1)
        )

        val leftScenarioId = "leftScenarioId"
        val leftScenario = ScenarioState(
            ScenarioCreateRequest(
                window = TimeWindow(
                    from = Instant.EPOCH,
                    to = Instant.EPOCH.plusSeconds(3)
                ),
                interests = emptyList()
            )
        ).copy(id = leftScenarioId, queued = Instant.EPOCH)

        val rightScenarioId = "rightScenarioId"
        val rightScenario = ScenarioState(
            ScenarioCreateRequest(
                window = TimeWindow(
                    from = Instant.EPOCH.plusSeconds(3),
                    to = Instant.EPOCH.plusSeconds(6)
                ),
                interests = emptyList()
            )
        ).copy(id = rightScenarioId, queued = Instant.EPOCH.plusSeconds(1))

        val overlappingScenarioId = "overlappingScenarioId"
        val overlappingScenario = ScenarioState(
            ScenarioCreateRequest(
                window = TimeWindow(
                    from = Instant.EPOCH,
                    to = Instant.EPOCH.plusSeconds(3).plusMillis(1)
                ),
                interests = emptyList()
            )
        ).copy(id = overlappingScenarioId, queued = Instant.EPOCH)

        val queryResult = listOf(
            stretchedScenario,
            innerScenario,
            leftScenario,
            rightScenario,
            overlappingScenario
        )

        whenever(scenariosDataSource.query(any(), any())).thenReturn(queryResult)
        val output = scenariosService.query(innerScenarioCreateRequest)

        assertEquals(
            listOf(innerScenarioId, stretchedScenarioId),
            output.matches.map { it.id }
        )
        assertEquals(
            listOf(overlappingScenarioId, rightScenarioId, leftScenarioId),
            output.overlaps.map { it.id }
        )
    }

    @Test
    fun getByIds() {
        val state = mock<ScenarioState>()
        whenever(state.toResponse(childScenarios, inheritedScenarios)).thenReturn(mock())
        whenever(scenariosDataSource.getByIds(any())).thenReturn(listOf(state))
        assertEquals(1, scenariosService.getByIds(setOf("")).size)
    }

    @Test
    fun getInterests() {
        val state = mock<ScenarioState>()
        whenever(state.getSourceScenarioId()).thenReturn("")
        whenever(scenariosDataSource.get(any())).thenReturn(state)
        whenever(scenariosInterestsService.getInterestsByScenario(any())).thenReturn(
            listOf(
                ScenarioInterestRelevantShip(mmsi = 0, imo = null, window = window, scenario = ""),
                ScenarioInterestRelevantShip(mmsi = 1, imo = null, window = window, scenario = "", partial = true),
            )
        )

        assertEquals(
            listOf(
                InterestRelevantShip(
                    ship = AisShipIdentifier(mmsi = 0, imo = null),
                    window = window,
                    windowNoMargin = window
                ),
                InterestRelevantShip(
                    ship = AisShipIdentifier(mmsi = 1, imo = null),
                    window = window,
                    windowNoMargin = window,
                    partial = true
                )
            ),
            scenariosService.getInterests("")
        )

        verify(csiService, never()).getImoAtTime(any(), any())
    }

    @Test
    fun `cancelAll - cancels all non-final scenarios and reports already cancelled ones`() {
        val queuedScenario = createTestScenario("queued-scenario", ScenarioState.InternalPhase.QUEUED)
        val initializingScenario = createTestScenario("initializing-scenario", ScenarioState.InternalPhase.INITIALIZING_STREAMING)
        val progressingScenario = createTestScenario("progressing-scenario", ScenarioState.InternalPhase.PROGRESSING)
        val postProcessingScenario = createTestScenario("post-processing-scenario", ScenarioState.InternalPhase.POST_PROCESSING_INIT)

        whenever(scenariosDataSource.getAvailableScenarios()).thenReturn(
            listOf(queuedScenario, initializingScenario, progressingScenario, postProcessingScenario)
        )

        val response = scenariosService.cancelAll()

        // Verify response counts
        assertEquals(4, response.totalCancelled)

        // Verify cancelled scenario IDs
        assertEquals(
            setOf("queued-scenario", "initializing-scenario", "progressing-scenario", "post-processing-scenario"),
            response.cancelledScenarioIds.toSet()
        )

        // Verify that saveMany was called with the correct scenarios
        verify(scenariosDataSource, times(1)).saveMany(
            argThat { savedScenarios ->
                savedScenarios.size == 4 &&
                    savedScenarios.all { it.phase == ScenarioState.InternalPhase.CANCELLED } &&
                    savedScenarios.all { it.cancelled != null } &&
                    savedScenarios.map { it.id }.toSet() == setOf("queued-scenario", "initializing-scenario", "progressing-scenario", "post-processing-scenario")
            }
        )
    }

    @Test
    fun `cancelAll - handles empty scenario list`() {
        whenever(scenariosDataSource.getAvailableScenarios()).thenReturn(emptyList())

        val response = scenariosService.cancelAll()

        assertEquals(0, response.totalCancelled)
        assertEquals(emptyList<String>(), response.cancelledScenarioIds)

        // Verify saveMany is not called when there are no scenarios to cancel
        verify(scenariosDataSource, never()).saveMany(any())
    }

    private fun createTestScenario(id: String, phase: ScenarioState.InternalPhase): ScenarioState {
        return ScenarioState(
            id = id,
            phase = phase,
            crashed = null,
            queued = Instant.EPOCH,
            pruned = null,
            cancelled = null,
            status = null,
            window = TimeWindow(Instant.EPOCH, Instant.EPOCH.plusSeconds(1)),
            windowMargin = Scenario.WindowMargin(),
            interests = emptyList(),
            settings = null,
            teardown = false,
            parent = null,
            inherit = null,
            metaData = null
        )
    }
}
