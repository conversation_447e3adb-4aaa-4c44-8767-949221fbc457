package nl.teqplay.aisengine.reventsengine.controller

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.reventsengine.BaseTest
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageV2Service
import nl.teqplay.aisengine.reventsengine.model.interest.InterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntryV2
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.ReventsVesselVoyageVisitV2Query
import nl.teqplay.aisengine.reventsengine.service.vesselvoyage.VesselVoyageMergingV2Service
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import org.junit.jupiter.api.Test
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Duration
import java.time.Instant

@AutoConfigureMockMvc
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class VesselVoyageV2ControllerTest(
    private val mockMvc: MockMvc,
    @MockitoBean private val vesselVoyageStorageV2Service: VesselVoyageStorageV2Service,
    @MockitoBean private val vesselVoyageMergingV2Service: VesselVoyageMergingV2Service,

    private val objectMapper: ObjectMapper,
) : BaseTest() {

    private val scenario = "scenario"
    private val imo = 0
    private val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
    private val interests = listOf(InterestVesselVoyage(imo, window, window, false))
    private val visit = mock<EntryESoFWrapper<NewVisit>>()
    private val voyage = mock<EntryESoFWrapper<NewVoyage>>()
    private val entries = listOf(visit, voyage)

    @Test
    @WithMockUser
    fun getInterests() {
        whenever(vesselVoyageStorageV2Service.getInterestsByScenario(eq(scenario)))
            .thenReturn(interests.map { ScenarioInterestVesselVoyage(it.imo, it.window, it.window, false, "scenario") })
        mockMvc
            .perform(get("/vesselvoyage/v2/{id}/interests", scenario))
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(interests)))
    }

    @Test
    @WithMockUser
    fun fetchEntries() {
        whenever(vesselVoyageStorageV2Service.fetchEntries(eq(scenario), eq(imo), eq(window))).thenReturn(entries)
        mockMvc
            .perform(
                post("/vesselvoyage/v2/{id}/entries/imo/{imo}", scenario, imo.toString())
                    .content(objectMapper.writeValueAsString(window))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(entries)))
    }

    @Test
    @WithMockUser
    fun getEntryById() {
        val entryId = "entryId"
        whenever(vesselVoyageStorageV2Service.getEntryById(eq(scenario), eq(entryId))).thenReturn(visit)
        mockMvc
            .perform(
                get("/vesselvoyage/v2/{id}/entries/{entryId}", scenario, entryId)
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(visit)))
    }

    @Test
    @WithMockUser
    fun getEntriesById() {
        val entryIds = listOf("entryId")
        whenever(vesselVoyageStorageV2Service.getEntriesById(eq(scenario), eq(entryIds))).thenReturn(listOf(visit))
        mockMvc
            .perform(
                post("/vesselvoyage/v2/{id}/entries", scenario)
                    .content(objectMapper.writeValueAsString(entryIds))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(listOf(visit))))
    }

    @Test
    @WithMockUser
    fun queryVisits() {
        val query = ReventsVesselVoyageVisitV2Query(
            window = window,
            areaIds = setOf("NLRTM_AREA_ID"),
            imos = setOf(0)
        )
        whenever(vesselVoyageStorageV2Service.queryVisits(eq(scenario), eq(query))).thenReturn(listOf(visit))
        mockMvc
            .perform(
                post("/vesselvoyage/v2/{id}/visits/query", scenario)
                    .content(objectMapper.writeValueAsString(query))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(listOf(visit))))
    }

    @Test
    @WithMockUser
    fun getInterestsForMerging() {
        val result = listOf(imo)
        whenever(vesselVoyageStorageV2Service.getInterestsForMergingByScenario(eq(scenario))).thenReturn(result)
        mockMvc
            .perform(
                get("/vesselvoyage/v2/{id}/merging/interests", scenario)
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(result)))
    }

    @Test
    @WithMockUser
    fun fetchEntriesForMerging() {
        val result = listOf(MergeEntryV2(window, entries))
        whenever(vesselVoyageMergingV2Service.fetchEntriesForMerging(eq(scenario), eq(imo))).thenReturn(result)
        mockMvc
            .perform(
                get("/vesselvoyage/v2/{id}/merging/entries/imo/{imo}", scenario, imo.toString())
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(result)))
    }
}
