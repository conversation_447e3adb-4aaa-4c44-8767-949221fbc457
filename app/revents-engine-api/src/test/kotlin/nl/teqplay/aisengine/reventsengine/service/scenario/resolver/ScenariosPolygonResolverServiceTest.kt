package nl.teqplay.aisengine.reventsengine.service.scenario.resolver

import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.S3Object
import com.amazonaws.services.s3.model.S3ObjectInputStream
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByArea
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryAreaIndexArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveClientService
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadByMonthStorageImpl
import nl.teqplay.aisengine.bucketing.storage.ship.area.implementation.ShipHistoryByAreaReadCache
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.Duration
import java.time.YearMonth
import java.time.ZoneOffset
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

class ScenariosPolygonResolverServiceTest {

    private val testYearMonth = YearMonth.of(2024, 1)
    private val mmsi1 = 111111111
    private val mmsi2 = 222222222
    private val mmsi3 = 333333333
    private val mmsi4 = 444444444
    private val from = testYearMonth.atDay(1)
        .atStartOfDay()
        .toInstant(ZoneOffset.UTC)

    private val day1 = testYearMonth.atDay(1)
    private val day2 = testYearMonth.atDay(2)
    private val day3 = testYearMonth.atDay(3)
    private val day4 = testYearMonth.atDay(4)
    private val day5 = testYearMonth.atDay(5)
    private val day6 = testYearMonth.atDay(6)
    private val day7 = testYearMonth.atDay(7)

    private val objectMapper = jacksonObjectMapper()
        .registerKotlinModule()
        .registerModule(JavaTimeModule())
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
    private val mockedS3Client = mock<AmazonS3>()
    private val fakeArchive = ShipHistoryAreaIndexArchiveProperties(
        enabled = true,
        writeNotBefore = null,
        prefix = "area-index",
        name = "ship-history",
        region = Regions.EU_WEST_1
    )
    private val archiveClientService = mock<ArchiveClientService>().apply {
        whenever(s3Client).thenReturn(mockedS3Client)
        whenever(archive).thenReturn(fakeArchive)
    }
    private val mockedArchiveStorage = ArchiveReadByMonthStorageImpl(
        dataClass = AisHistoricOrderedDiffMessage::class.java,
        archive = fakeArchive,
        objectMapper = objectMapper,
        archiveClientService = archiveClientService
    )
    private val shipHistoryByAreaReadCache = mock<ShipHistoryByAreaReadCache>().apply {
        whenever(archiveStorage).thenReturn(mockedArchiveStorage)
        whenever(factory).thenReturn(AisHistoricDiffMessageBucketFactoryByArea)
    }
    private val scenariosPolygonResolverService = ScenariosPolygonResolverService(
        shipHistoryByAreaReadCache,
        archiveClientService,
        fakeArchive,
        objectMapper
    )

    private val polygons = listOf(
        Polygon(Location(0.0, 0.0))
    )

    @Test
    fun `should find ships near polygon for all days`() {
        val day1Mock = mockS3Object(TestData("0.0,0.0", setOf(mmsi1, mmsi2, mmsi3)))
        val day2Mock = mockS3Object(TestData("0.0,0.0", setOf(mmsi1, mmsi2)))
        val day3Mock = mockS3Object(TestData("0.0,0.0", setOf(mmsi1, mmsi3)))
        val day4Mock = mockS3Object(TestData("0.0,0.0", setOf(mmsi2, mmsi3)))
        val day5Mock = mockS3Object(TestData("0.0,0.0", setOf(mmsi1)))
        val day6Mock = mockS3Object(TestData("0.0,0.0", setOf(mmsi2)))
        val day7Mock = mockS3Object(TestData("0.0,0.0", setOf(mmsi3)))

        whenever(mockedS3Client.getObject(eq("ship-history"), eq("area-index,2024-01-01.zip"))).thenReturn(day1Mock)
        whenever(mockedS3Client.getObject(eq("ship-history"), eq("area-index,2024-01-02.zip"))).thenReturn(day2Mock)
        whenever(mockedS3Client.getObject(eq("ship-history"), eq("area-index,2024-01-03.zip"))).thenReturn(day3Mock)
        whenever(mockedS3Client.getObject(eq("ship-history"), eq("area-index,2024-01-04.zip"))).thenReturn(day4Mock)
        whenever(mockedS3Client.getObject(eq("ship-history"), eq("area-index,2024-01-05.zip"))).thenReturn(day5Mock)
        whenever(mockedS3Client.getObject(eq("ship-history"), eq("area-index,2024-01-06.zip"))).thenReturn(day6Mock)
        whenever(mockedS3Client.getObject(eq("ship-history"), eq("area-index,2024-01-07.zip"))).thenReturn(day7Mock)

        val timeWindow = TimeWindow(from, Duration.ofDays(6))

        val result = scenariosPolygonResolverService.findShipsNearPolygons(timeWindow, polygons)
        val expected = mapOf(
            day1 to setOf(mmsi1, mmsi2, mmsi3),
            day2 to setOf(mmsi1, mmsi2),
            day3 to setOf(mmsi1, mmsi3),
            day4 to setOf(mmsi2, mmsi3),
            day5 to setOf(mmsi1),
            day6 to setOf(mmsi2),
            day7 to setOf(mmsi3)
        )
        assertEquals(expected, result)
        verify(mockedS3Client, times(7)).getObject(any<String>(), any<String>())
    }

    @Test
    fun `should find ships near polygon for multiple areas`() {
        val day1Mock = mockS3Object(
            TestData("0.0,0.0", setOf(mmsi1)),
            TestData("0.1,0.0", setOf(mmsi2)),
            TestData("0.1,1.0", setOf(mmsi3)),
            TestData("0.2,1.0", setOf(mmsi3)),
            TestData("2.5,2.5", setOf(mmsi4))
        )
        val day2Mock = mockS3Object(
            TestData("0.5,0.4", setOf(mmsi1, mmsi2)),
            TestData("0.4,0.4", setOf(mmsi3))
        )

        val testPolygons = listOf(
            Polygon(
                Location(0.0, 0.0),
                Location(0.0, 0.5),
                Location(0.5, 0.5),
                Location(0.5, 0.0),
                Location(0.0, 0.0)
            ),
            Polygon(
                Location(2.0, 2.0),
                Location(2.0, 3.0),
                Location(3.0, 3.0),
                Location(3.0, 2.0),
                Location(2.0, 2.0)
            )
        )

        whenever(mockedS3Client.getObject(eq("ship-history"), eq("area-index,2024-01-01.zip"))).thenReturn(day1Mock)
        whenever(mockedS3Client.getObject(eq("ship-history"), eq("area-index,2024-01-02.zip"))).thenReturn(day2Mock)

        val timeWindow = TimeWindow(from, Duration.ofDays(1))

        val result = scenariosPolygonResolverService.findShipsNearPolygons(timeWindow, testPolygons)
        val expected = mapOf(
            day1 to setOf(mmsi1, mmsi2, mmsi4),
            day2 to setOf(mmsi3)
        )
        assertEquals(expected, result)
        verify(mockedS3Client, times(2)).getObject(any<String>(), any<String>())
    }

    private data class TestData(
        val areaId: String,
        val mmsis: Set<Int>
    )

    /**
     * @param testData all ships we want to test with.Here a test data contains the date when it happened,
     *  the bucket key it used and the list of mmsi it would return.
     */
    private fun mockS3Object(
        vararg testData: TestData
    ): S3Object = mock<S3Object>().apply {
        val outputStream = ByteArrayOutputStream()
        val zipOutputStream = ZipOutputStream(outputStream)
        try {
            zipOutputStream.putNextEntry(ZipEntry("index.json"))
            testData.associate { (areaId, mmsis) -> areaId to mmsis }
                .let { dataAsMap ->
                    val dataAsBytes = objectMapper.writeValueAsBytes(dataAsMap)
                    zipOutputStream.write(dataAsBytes)
                }
            zipOutputStream.closeEntry()
            val bytes = outputStream.toByteArray()
            val inputStream = ByteArrayInputStream(bytes)

            whenever(objectContent).thenReturn(S3ObjectInputStream(inputStream, mock()))
        } finally {
            zipOutputStream.close()
            outputStream.close()
        }
    }
}
