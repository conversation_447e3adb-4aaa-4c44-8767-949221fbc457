package nl.teqplay.aisengine.reventsengine.service.external

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.PORT
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.TERMINAL
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.poma.api.v1.Anchorage
import nl.teqplay.poma.api.v1.PilotBoardingPlace
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.poma.api.v1.Terminal
import nl.teqplay.skeleton.poma.client.PomaInfrastructureClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.web.client.RestTemplate

class PomaServiceTest {

    private val restTemplate = mock<RestTemplate>()
    private val pomaInfrastructureClient = mock<PomaInfrastructureClient>()
    private val pomaService = PomaService(restTemplate, pomaInfrastructureClient)

    private val unlocodes = listOf("NLRTM")

    private val portInterests = listOf(InterestArea(InterestArea.Area(PORT, "id")))
    private val terminalInterests = listOf(InterestArea(InterestArea.Area(TERMINAL, "id")))
    private val interests = portInterests + terminalInterests

    @Test
    fun requestUnlocodesForTerminals() {
        // ports not supported
        assertEquals(emptyList<String>(), pomaService.requestUnlocodesForTerminals(portInterests))

        val terminal = mock<Terminal>().apply {
            whenever(ports).thenReturn(unlocodes)
        }
        whenever(restTemplate.postForObject<Array<Terminal>>(anyString(), anyOrNull(), any()))
            .thenThrow(Error())
            .thenReturn(emptyArray())
            .thenReturn(arrayOf(terminal))

        // request failed
        assertThrows<ScenarioCrashedException> {
            pomaService.requestUnlocodesForTerminals(interests)
        }

        // request returns too little
        assertThrows<ScenarioCrashedException> {
            pomaService.requestUnlocodesForTerminals(interests)
        }

        // returns list of terminals
        assertEquals(1, pomaService.requestUnlocodesForTerminals(interests).size)
    }

    @Test
    fun requestPortsByInterests() {
        // terminals not supported
        assertEquals(emptyList<Port>(), pomaService.requestPortsByInterests(terminalInterests))

        val port = mock<Port>().apply {
            whenever(unlocode).thenReturn(unlocodes.first())
        }
        whenever(restTemplate.postForObject<Array<Port>>(anyString(), anyOrNull(), any()))
            .thenThrow(Error())
            .thenReturn(emptyArray())
            .thenReturn(arrayOf(port))

        // request failed
        assertThrows<ScenarioCrashedException> {
            pomaService.requestPortsByInterests(interests)
        }

        // request returns too little
        assertThrows<ScenarioCrashedException> {
            pomaService.requestPortsByInterests(interests)
        }

        // returns list of ports
        assertEquals(1, pomaService.requestPortsByInterests(interests).size)
    }

    @Test
    fun requestPortsByUnlocodes() {
        assertEquals(emptyList<Port>(), pomaService.requestPortsByUnlocodes(emptyList()))

        // request failed
        whenever(pomaInfrastructureClient.getPortsByUnlocode(any()))
            .thenThrow(Error())
        assertThrows<ScenarioCrashedException> {
            pomaService.requestPortsByUnlocodes(unlocodes)
        }

        // request returns too little
        whenever(pomaInfrastructureClient.getPortsByUnlocode(any()))
            .thenReturn(emptyArray())
        assertThrows<ScenarioCrashedException> {
            pomaService.requestPortsByUnlocodes(unlocodes)
        }

        // returns list of ports
        whenever(pomaInfrastructureClient.getPortsByUnlocode(any()))
            .thenReturn(arrayOf(mock()))
        assertEquals(1, pomaService.requestPortsByUnlocodes(unlocodes).size)
    }

    @Test
    fun requestAnchorages() {
        assertEquals(emptyList<Anchorage>(), pomaService.requestAnchorages(emptyList()))

        whenever(
            pomaInfrastructureClient.getAnchorages(
                searchPattern = anyOrNull(),
                topLeftLat = anyOrNull(),
                topLeftLon = anyOrNull(),
                bottomRightLat = anyOrNull(),
                bottomRightLon = anyOrNull(),
                withArea = anyOrNull(),
                port = anyOrNull(),
                limit = anyOrNull(),
                skip = anyOrNull(),
                validated = anyOrNull()
            )
        ).thenThrow(Error()).thenReturn(arrayOf(mock()))

        // request failed
        assertThrows<ScenarioCrashedException> {
            pomaService.requestAnchorages(unlocodes)
        }

        // returns list of anchorages
        assertEquals(1, pomaService.requestAnchorages(unlocodes).size)
    }

    @Test
    fun requestPilotBoardingPlaces() {
        assertEquals(emptyList<PilotBoardingPlace>(), pomaService.requestPilotBoardingPlaces(emptyList()))

        whenever(
            pomaInfrastructureClient.getPilotBoardingPlaces(
                searchPattern = anyOrNull(),
                topLeftLat = anyOrNull(),
                topLeftLon = anyOrNull(),
                bottomRightLat = anyOrNull(),
                bottomRightLon = anyOrNull(),
                withArea = anyOrNull(),
                port = anyOrNull(),
                limit = anyOrNull(),
                skip = anyOrNull(),
                validated = anyOrNull()
            )
        ).thenThrow(Error()).thenReturn(arrayOf(mock()))

        // request failed
        assertThrows<ScenarioCrashedException> {
            pomaService.requestPilotBoardingPlaces(unlocodes)
        }

        // returns list of pilot boarding places
        assertEquals(1, pomaService.requestPilotBoardingPlaces(unlocodes).size)
    }
}
