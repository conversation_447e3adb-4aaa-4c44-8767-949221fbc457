package nl.teqplay.aisengine.reventsengine.service.scenario.resolver

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.service.external.CsiService
import nl.teqplay.aisengine.reventsengine.global.model.InterestResolvedInternal
import nl.teqplay.aisengine.reventsengine.global.model.InterestShipInternal
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestPolygon
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestShip
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee
import nl.teqplay.aisengine.reventsengine.service.external.PomaService
import nl.teqplay.aisengine.reventsengine.service.scenario.scenarioCreateRequest
import nl.teqplay.aisengine.util.coerceTimeWindowWithinBoundary
import nl.teqplay.aisengine.util.withMargin
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.poma.api.v1.Terminal
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.clearInvocations
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.stream.Stream
import nl.teqplay.poma.api.v1.Location as PomaLocation

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ScenariosAreaResolverServiceTest {

    private val csiService = mock<CsiService>()
    private val pomaService = mock<PomaService>()
    private val scenariosPolygonResolverService = mock<ScenariosPolygonResolverService>()
    private val scenariosAreaResolverService = ScenariosAreaResolverService(
        csiService,
        pomaService,
        scenariosPolygonResolverService
    )

    private val window = TimeWindow(
        from = Instant.EPOCH,
        to = Instant.EPOCH.plusSeconds(1)
    )
    private val windowMargin = Scenario.WindowMargin(2, 1)

    private val areaInterests = listOf(
        InterestArea(
            area = InterestArea.Area(
                type = AreaIdentifier.AreaType.TERMINAL,
                id = "TERMINAL"
            )
        )
    )

    private val settings = Scenario.Settings(scenarioCreateRequest).copy(
        resolveShipInterestsForImo = false,
    )

    @BeforeEach
    fun beforeEach() {
        reset(
            csiService,
            pomaService,
            scenariosPolygonResolverService
        )
    }

    @Test
    fun `resolveInterests - empty`() {
        assertEquals(
            InterestResolvedInternal.empty(),
            scenariosAreaResolverService.resolveInterests(
                window = window,
                windowMargin = windowMargin,
                guarantees = emptySet(),
                settings = this.settings,
                areaInterests = emptyList(),
                polygonInterests = emptyList()
            )
        )
        verify(pomaService, never()).requestUnlocodesForTerminals(any())
    }

    @Test
    fun `resolveInterests - areaInterests from polygons`() {
        whenever(pomaService.requestUnlocodesForTerminals(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByUnlocodes(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByInterests(any())).thenReturn(listOf(port))
        whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any()))
            .thenReturn(mapOf(LocalDate.EPOCH to setOf(0)))

        val shipWindow = TimeWindow(
            from = Instant.EPOCH,
            to = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
        )
        assertEquals(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = InterestShip.Identifier(mmsi = 0),
                        window = shipWindow.withMargin(windowMargin),
                        windowNoMargin = shipWindow
                    )
                ),
                polygons = listOf(polygon),
                unlocodes = listOf("NLRTM")
            ),
            scenariosAreaResolverService.resolveInterests(
                window = shipWindow,
                windowMargin = windowMargin,
                guarantees = emptySet(),
                settings = this.settings,
                areaInterests = areaInterests,
                polygonInterests = emptyList()
            )
        )
    }

    @Test
    fun `resolveInterests - areaInterests from polygons (using window smaller than 1 day)`() {
        whenever(pomaService.requestUnlocodesForTerminals(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByUnlocodes(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByInterests(any())).thenReturn(listOf(port))
        whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any()))
            .thenReturn(mapOf(LocalDate.EPOCH to setOf(0)))

        assertEquals(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = InterestShip.Identifier(mmsi = 0),
                        window = window.withMargin(windowMargin),
                        windowNoMargin = window
                    )
                ),
                polygons = listOf(polygon),
                unlocodes = listOf("NLRTM")
            ),
            scenariosAreaResolverService.resolveInterests(
                window = window,
                windowMargin = windowMargin,
                guarantees = emptySet(),
                settings = this.settings,
                areaInterests = areaInterests,
                polygonInterests = emptyList()
            )
        )
    }

    @Test
    fun `resolveInterests - areaInterests from polygons, allow partial matches before window (empty, since not found inside window)`() {
        whenever(pomaService.requestUnlocodesForTerminals(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByUnlocodes(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByInterests(any())).thenReturn(listOf(port))
        whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any()))
            .thenReturn(mapOf(LocalDate.EPOCH.minusDays(1) to setOf(0)))

        assertEquals(
            InterestResolvedInternal(
                ships = emptyList(),
                polygons = listOf(polygon),
                unlocodes = listOf("NLRTM")
            ),
            scenariosAreaResolverService.resolveInterests(
                window = window,
                windowMargin = windowMargin,
                guarantees = emptySet(),
                settings = this.settings.copy(
                    allowPartialInterests = true
                ),
                areaInterests = areaInterests,
                polygonInterests = emptyList()
            )
        )
    }

    @Test
    fun `resolveInterests - areaInterests from polygons, allow partial matches after window (empty, since not found inside window)`() {
        whenever(pomaService.requestUnlocodesForTerminals(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByUnlocodes(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByInterests(any())).thenReturn(listOf(port))
        whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any()))
            .thenReturn(mapOf(LocalDate.EPOCH.plusDays(1) to setOf(0)))

        assertEquals(
            InterestResolvedInternal(
                ships = emptyList(),
                polygons = listOf(polygon),
                unlocodes = listOf("NLRTM")
            ),
            scenariosAreaResolverService.resolveInterests(
                window = window,
                windowMargin = windowMargin,
                guarantees = emptySet(),
                settings = this.settings.copy(
                    allowPartialInterests = true
                ),
                areaInterests = areaInterests,
                polygonInterests = emptyList()
            )
        )
    }

    @Test
    fun `resolveInterests - areaInterests from polygons, allow partial matches covering window`() {
        whenever(pomaService.requestUnlocodesForTerminals(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByUnlocodes(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByInterests(any())).thenReturn(listOf(port))
        whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any()))
            .thenReturn(
                mapOf(
                    LocalDate.EPOCH.minusDays(1) to setOf(0),
                    LocalDate.EPOCH to setOf(0),
                    LocalDate.EPOCH.plusDays(1) to setOf(0),
                )
            )

        assertEquals(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = InterestShip.Identifier(mmsi = 0),
                        window = window.withMargin(windowMargin),
                        windowNoMargin = window,
                        partial = true
                    )
                ),
                polygons = listOf(polygon),
                unlocodes = listOf("NLRTM")
            ),
            scenariosAreaResolverService.resolveInterests(
                window = window,
                windowMargin = windowMargin,
                guarantees = emptySet(),
                settings = this.settings.copy(
                    allowPartialInterests = true
                ),
                areaInterests = areaInterests,
                polygonInterests = emptyList()
            )
        )
    }

    @Test
    fun `resolveInterests - areaInterests from polygons, allow partial matches outside window`() {
        whenever(pomaService.requestUnlocodesForTerminals(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByUnlocodes(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByInterests(any())).thenReturn(listOf(port))
        whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any()))
            .thenReturn(
                mapOf(
                    LocalDate.EPOCH.minusDays(1) to setOf(0),
                    LocalDate.EPOCH to setOf(0),
                    LocalDate.EPOCH.plusDays(1) to emptySet(),
                    LocalDate.EPOCH.plusDays(2) to setOf(0),
                    LocalDate.EPOCH.plusDays(3) to setOf(0),
                )
            )

        val window = TimeWindow(
            from = Instant.EPOCH,
            to = Instant.EPOCH.plus(3, ChronoUnit.DAYS)
        )
        val shipWindowLeft = TimeWindow(
            from = Instant.EPOCH,
            to = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
        )
        val shipWindowRight = TimeWindow(
            from = Instant.EPOCH.plus(2, ChronoUnit.DAYS),
            to = Instant.EPOCH.plus(3, ChronoUnit.DAYS)
        )
        assertEquals(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = InterestShip.Identifier(mmsi = 0),
                        window = coerceTimeWindowWithinBoundary(shipWindowLeft, window).withMargin(windowMargin),
                        windowNoMargin = shipWindowLeft,
                        partial = true
                    ),
                    InterestShipInternal(
                        ship = InterestShip.Identifier(mmsi = 0),
                        window = coerceTimeWindowWithinBoundary(shipWindowRight, window).withMargin(windowMargin),
                        windowNoMargin = shipWindowRight,
                        partial = true
                    )
                ),
                polygons = listOf(polygon),
                unlocodes = listOf("NLRTM")
            ),
            scenariosAreaResolverService.resolveInterests(
                window = window,
                windowMargin = windowMargin,
                guarantees = emptySet(),
                settings = this.settings.copy(
                    allowPartialInterests = true
                ),
                areaInterests = areaInterests,
                polygonInterests = emptyList()
            )
        )
    }

    @ParameterizedTest
    @EnumSource(ScenarioGuarantee::class, names = ["VESSEL_VOYAGE_MERGING", "VESSEL_VOYAGE_MERGING_V2"])
    fun `resolveInterests - port areaInterests with VesselVoyage merging guarantee should only allow main ports`(guarantee: ScenarioGuarantee) {
        whenever(pomaService.requestUnlocodesForTerminals(any())).thenReturn(terminal.ports)
        whenever(pomaService.requestPortsByUnlocodes(any())).thenReturn(listOf(port))
        whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any()))
            .thenReturn(mapOf(LocalDate.EPOCH to setOf(0)))

        val res = scenariosAreaResolverService.resolveInterests(
            window = window,
            windowMargin = windowMargin,
            guarantees = setOf(guarantee),
            settings = this.settings,
            areaInterests = areaInterests,
            polygonInterests = emptyList()
        )
        assertThat(res.ships).isNotEmpty()

        whenever(pomaService.requestPortsByUnlocodes(any()))
            .thenReturn(listOf(port.copy(mainPort = "OTHER_PORT_IS_MAIN")))

        assertThrows<ScenarioCrashedException> {
            scenariosAreaResolverService.resolveInterests(
                window = window,
                windowMargin = windowMargin,
                guarantees = setOf(guarantee),
                settings = this.settings,
                areaInterests = areaInterests,
                polygonInterests = emptyList()
            )
        }
    }

    @ParameterizedTest
    @MethodSource("resolvePolygonInterestsTestData")
    fun `resolveInterests - polygonInterests`(data: TestData) {
        val map = data.before + data.map + data.after
        val permutations = listOf(
            "normal order" to map,
            "reversed order" to map.toList().reversed().toMap()
        )

        val window = TimeWindow(
            from = data.map.keys.minOrNull()?.atStartOfDay()?.toInstant(ZoneOffset.UTC) ?: window.from,
            to = data.map.keys.maxOrNull()?.atStartOfDay()?.plusDays(1)?.toInstant(ZoneOffset.UTC) ?: window.to,
        )
        val paddedWindow = window.withMargin(Duration.ofDays(1))
        permutations.forEach { (message, map) ->
            whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any())).thenReturn(map)
            assertEquals(
                data.output.sortedBy { it.ship.mmsi },
                scenariosAreaResolverService.resolveInterests(
                    window = window,
                    windowMargin = data.windowMargin,
                    guarantees = emptySet(),
                    settings = this.settings,
                    areaInterests = emptyList(),
                    polygonInterests = polygonInterests
                ).ships.sortedBy { it.ship.mmsi },
                message
            )

            // Check that we used the padded window.
            verify(scenariosPolygonResolverService, times(1)).findShipsNearPolygons(eq(paddedWindow), any())
            clearInvocations(scenariosPolygonResolverService)
        }
    }

    data class TestData(
        val windowMargin: Scenario.WindowMargin = Scenario.WindowMargin(),
        val before: Map<LocalDate, Set<Int>> = emptyMap(),
        val map: Map<LocalDate, Set<Int>>,
        val after: Map<LocalDate, Set<Int>> = emptyMap(),
        val output: List<InterestShipInternal>,
    )

    private fun resolvePolygonInterestsTestData() = Stream.of(
        TestData(
            map = emptyMap(),
            output = emptyList(),
        ),
        TestData(
            map = mapOf(
                LocalDate.EPOCH to setOf(0),
            ),
            output = listOf(
                InterestShipInternal(
                    ship = InterestShip.Identifier(mmsi = 0),
                    window = TimeWindow(
                        from = Instant.EPOCH,
                        to = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
                    )
                )
            ),
        ),
        TestData(
            before = mapOf(
                LocalDate.EPOCH.minusDays(1) to setOf(0),
            ),
            map = mapOf(
                LocalDate.EPOCH to setOf(0, 1),
            ),
            after = mapOf(
                LocalDate.EPOCH.plusDays(1) to setOf(1),
            ),
            // ships are not exclusively within the time window
            output = emptyList(),
        ),
        TestData(
            windowMargin = Scenario.WindowMargin(1, 1),
            before = mapOf(
                LocalDate.EPOCH.minusDays(1) to setOf(0),
            ),
            map = mapOf(
                LocalDate.EPOCH to setOf(0, 1, 2),
            ),
            after = mapOf(
                LocalDate.EPOCH.plusDays(1) to setOf(1),
            ),
            // ships are not exclusively within the time window (only mmsi=2 is used including the margin)
            output = listOf(
                InterestShipInternal(
                    ship = InterestShip.Identifier(mmsi = 2),
                    window = TimeWindow(
                        from = Instant.EPOCH.minus(1, ChronoUnit.DAYS),
                        to = Instant.EPOCH.plus(2, ChronoUnit.DAYS)
                    ),
                    windowNoMargin = TimeWindow(
                        from = Instant.EPOCH,
                        to = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
                    ),
                )
            ),
        ),
        TestData(
            map = mapOf(
                LocalDate.EPOCH to emptySet(),
                LocalDate.EPOCH.plusDays(1) to setOf(0),
                LocalDate.EPOCH.plusDays(2) to setOf(0),
                LocalDate.EPOCH.plusDays(3) to emptySet()
            ),
            output = listOf(
                InterestShipInternal(
                    ship = InterestShip.Identifier(mmsi = 0),
                    window = TimeWindow(
                        from = Instant.EPOCH.plus(1, ChronoUnit.DAYS),
                        to = Instant.EPOCH.plus(3, ChronoUnit.DAYS)
                    )
                )
            ),
        ),
        TestData(
            windowMargin = Scenario.WindowMargin(2, 2),
            map = mapOf(
                LocalDate.EPOCH.minusDays(1) to setOf(1),
                LocalDate.EPOCH to setOf(0),
                LocalDate.EPOCH.plusDays(1) to setOf(2),
            ),
            output = listOf(
                InterestShipInternal(
                    ship = InterestShip.Identifier(mmsi = 1),
                    window = TimeWindow(
                        from = Instant.EPOCH.minus(3, ChronoUnit.DAYS),
                        to = Instant.EPOCH.plus(2, ChronoUnit.DAYS)
                    ),
                    windowNoMargin = TimeWindow(
                        from = Instant.EPOCH.minus(1, ChronoUnit.DAYS),
                        to = Instant.EPOCH
                    ),
                ),
                InterestShipInternal(
                    ship = InterestShip.Identifier(mmsi = 0),
                    window = TimeWindow(
                        from = Instant.EPOCH.minus(2, ChronoUnit.DAYS),
                        to = Instant.EPOCH.plus(3, ChronoUnit.DAYS)
                    ),
                    windowNoMargin = TimeWindow(
                        from = Instant.EPOCH,
                        to = Instant.EPOCH.plus(1, ChronoUnit.DAYS)
                    ),
                ),
                InterestShipInternal(
                    ship = InterestShip.Identifier(mmsi = 2),
                    window = TimeWindow(
                        from = Instant.EPOCH.minus(1, ChronoUnit.DAYS),
                        to = Instant.EPOCH.plus(4, ChronoUnit.DAYS)
                    ),
                    windowNoMargin = TimeWindow(
                        from = Instant.EPOCH.plus(1, ChronoUnit.DAYS),
                        to = Instant.EPOCH.plus(2, ChronoUnit.DAYS)
                    ),
                )
            )
        ),
    )

    private val terminal = Terminal(
        authorityId = null, uniqueId = null,
        name = "", ports = listOf("NLRTM"),
        portCity = null, country = null, city = null,
        region = null, maxLength = null, maxWidth = null,
        headofficeID = null, headofficeName = null, assetType = null,
        maxDraft = null, capacity = null, numberOfTanks = null,
        hasLPBData = null, hasVCG = null, terminalGroup = null,
        cargoCategoryType = null, cargoType = null,
        location = PomaLocation(0.0, 0.0),
        area = emptyList(),
        areaSizeInM2 = null, manualOverriddenArea = false,
        mooringArea = emptyList(), manualOverriddenMooringArea = false,
        modelType = "", source = null, sourceType = null, _id = null,
        displayName = "", mainPort = "", functionType = null
    )

    private val pomaLocation = PomaLocation(0.0, 0.0)
    private val portArea = listOf(pomaLocation)
    private val port = Port(
        name = "NLRTM",
        displayName = "NLRTM",
        unlocode = "NLRTM",
        countryCode = null,
        country = null,
        location = pomaLocation,
        area = portArea,
        areaSizeInM2 = null,
        manualOverriddenArea = false,
        eosArea = null,
        uniqueId = null,
        modelType = "",
        source = null,
        sourceType = null,
        _id = null
    )

    private val polygon = Polygon(Location(0.0, 0.0))
    private val polygonInterests = listOf(
        InterestPolygon(polygon)
    )

    @Test
    fun `resolvePolygonInterests - applying margins, all interests are kept separately`() {
        whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any()))
            .thenReturn(
                mapOf(
                    LocalDate.parse("2021-12-31") to emptySet(),
                    // inside the window
                    LocalDate.parse("2022-01-01") to setOf(0),
                    LocalDate.parse("2022-01-02") to emptySet(),
                    // inside the window
                    LocalDate.parse("2022-01-03") to setOf(0),
                    LocalDate.parse("2022-01-04") to emptySet(),
                    // inside the window
                    LocalDate.parse("2022-01-05") to setOf(0),
                    LocalDate.parse("2022-01-06") to emptySet(),
                )
            )

        val window = TimeWindow(
            from = Instant.parse("2022-01-01T00:00:00Z"),
            to = Instant.parse("2022-01-06T00:00:00Z")
        )
        val windowMargin = Scenario.WindowMargin(
            beforeInDays = 1,
            afterInDays = 1
        )

        val res = scenariosAreaResolverService.resolveInterests(
            window = window,
            windowMargin = windowMargin,
            guarantees = emptySet(),
            settings = Scenario.Settings(),
            areaInterests = emptyList(),
            polygonInterests = listOf(mock())
        )

        val window1 = TimeWindow(Instant.parse("2022-01-01T00:00:00Z"), Duration.ofDays(1))
        val window2 = TimeWindow(Instant.parse("2022-01-03T00:00:00Z"), Duration.ofDays(1))
        val window3 = TimeWindow(Instant.parse("2022-01-05T00:00:00Z"), Duration.ofDays(1))
        assertThat(res.ships).isEqualTo(
            listOf(
                InterestShipInternal(
                    ship = InterestShip.Identifier(mmsi = 0),
                    window = window1.withMargin(windowMargin),
                    windowNoMargin = window1
                ),
                InterestShipInternal(
                    ship = InterestShip.Identifier(mmsi = 0),
                    window = window2.withMargin(windowMargin),
                    windowNoMargin = window2
                ),
                InterestShipInternal(
                    ship = InterestShip.Identifier(mmsi = 0),
                    window = window3.withMargin(windowMargin),
                    windowNoMargin = window3
                )
            )
        )
    }

    @Test
    fun `resolvePolygonInterests - applying margins should not result in discarding all`() {
        whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any()))
            .thenReturn(
                mapOf(
                    // falls outside/before the window
                    LocalDate.parse("2021-12-31") to setOf(0),
                    LocalDate.parse("2022-01-01") to setOf(0),
                    LocalDate.parse("2022-01-02") to emptySet(),
                    // inside the window
                    LocalDate.parse("2022-01-03") to setOf(0),
                    LocalDate.parse("2022-01-04") to emptySet(),
                    // falls outside/after the window
                    LocalDate.parse("2022-01-05") to setOf(0),
                    LocalDate.parse("2022-01-06") to setOf(0),
                )
            )

        val window = TimeWindow(
            from = Instant.parse("2022-01-01T00:00:00Z"),
            to = Instant.parse("2022-01-06T00:00:00Z")
        )
        val windowMargin = Scenario.WindowMargin(
            beforeInDays = 1,
            afterInDays = 1
        )

        val res = scenariosAreaResolverService.resolveInterests(
            window = window,
            windowMargin = windowMargin,
            guarantees = emptySet(),
            settings = Scenario.Settings(),
            areaInterests = emptyList(),
            polygonInterests = listOf(mock())
        )

        // Resolving should result in just the inner interest with margins applied.
        val expectedWindow = TimeWindow(
            from = Instant.parse("2022-01-03T00:00:00Z"),
            to = Instant.parse("2022-01-04T00:00:00Z")
        )
        assertThat(res.ships).isEqualTo(
            listOf(
                InterestShipInternal(
                    ship = InterestShip.Identifier(mmsi = 0),
                    window = expectedWindow.withMargin(windowMargin),
                    windowNoMargin = expectedWindow
                )
            )
        )
    }

    @Test
    fun `resolvePolygonInterests - ensure partial match is bounded to provided window`() {
        whenever(pomaService.requestUnlocodesForTerminals(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByUnlocodes(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByInterests(any())).thenReturn(listOf(port))
        whenever(scenariosPolygonResolverService.findShipsNearPolygons(any(), any()))
            .thenReturn(
                mapOf(
                    LocalDate.EPOCH.minusDays(1) to setOf(0),
                    LocalDate.EPOCH to setOf(0),
                    LocalDate.EPOCH.plusDays(1) to setOf(0),
                )
            )

        assertEquals(
            InterestResolvedInternal(
                ships = listOf(
                    InterestShipInternal(
                        ship = InterestShip.Identifier(mmsi = 0),
                        window = window,
                        windowNoMargin = window,
                        partial = true
                    )
                ),
                polygons = listOf(polygon),
                unlocodes = listOf("NLRTM")
            ),
            scenariosAreaResolverService.resolveInterests(
                window = window,
                windowMargin = Scenario.WindowMargin(),
                guarantees = emptySet(),
                settings = this.settings.copy(
                    allowPartialInterests = true
                ),
                areaInterests = areaInterests,
                polygonInterests = emptyList()
            )
        )
    }

    @Test
    fun `should resolve area interest for multiple ports with same polygon`() {
        val testPortId = "TEST_PORT_ID"
        val testSettings = Scenario.Settings(scenarioCreateRequest)
        val testAreaInterests = InterestArea(
            area = InterestArea.Area(
                type = AreaIdentifier.AreaType.PORT,
                id = testPortId
            )
        )
        val windowStartTime = Instant.ofEpochMilli(1672531200000)
        val testWindow = TimeWindow(windowStartTime, Duration.ofDays(30))
        val testPomaArea = listOf(
            PomaLocation(0.0, 0.0),
            PomaLocation(0.0, 1.0),
            PomaLocation(1.0, 1.0),
            PomaLocation(1.0, 0.0),
            PomaLocation(0.0, 0.0)
        )
        val testPort1 = port.copy(unlocode = "TEST_PORT_1", area = testPomaArea, outerArea = testPomaArea, eosArea = testPomaArea)
        val testPort2 = port.copy(unlocode = "TEST_PORT_2", area = testPomaArea, outerArea = emptyList(), eosArea = testPomaArea)
        val testPort3 = port.copy(unlocode = "TEST_PORT_3", area = testPomaArea, outerArea = emptyList(), eosArea = emptyList())
        val testPort4 = port.copy(unlocode = "TEST_PORT_4", area = testPomaArea, outerArea = testPomaArea, eosArea = emptyList())
        val testPort5 = port.copy(unlocode = "TEST_PORT_5", area = testPomaArea, outerArea = testPomaArea, eosArea = null)

        whenever(pomaService.requestUnlocodesForTerminals(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByUnlocodes(any())).thenReturn(emptyList())
        whenever(pomaService.requestPortsByInterests(eq(listOf(testAreaInterests))))
            .thenReturn(listOf(testPort1, testPort2, testPort3, testPort4, testPort5))

        val result = scenariosAreaResolverService.resolveInterests(
            window = testWindow,
            windowMargin = windowMargin,
            guarantees = emptySet(),
            settings = testSettings,
            areaInterests = listOf(testAreaInterests),
            polygonInterests = emptyList()
        )
        val expectedPortPolygon = Polygon(testPomaArea.map { Location(it.latitude, it.longitude) })
        val expected = InterestResolvedInternal(
            ships = emptyList(),
            polygons = listOf(
                expectedPortPolygon
            ),
            unlocodes = listOfNotNull(
                testPort1.unlocode,
                testPort2.unlocode,
                testPort3.unlocode,
                testPort4.unlocode,
                testPort5.unlocode
            )
        )

        assertEquals(expected, result)
    }
}
