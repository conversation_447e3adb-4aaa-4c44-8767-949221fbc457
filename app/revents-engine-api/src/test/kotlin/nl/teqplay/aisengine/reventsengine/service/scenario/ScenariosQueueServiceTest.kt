package nl.teqplay.aisengine.reventsengine.service.scenario

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_INITIALIZE_INTERESTS
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant

class ScenariosQueueServiceTest {

    private val scenariosDataSource = mock<ScenariosDataSource>()
    private val scenariosQueueService = ScenariosQueueService(scenariosDataSource)

    private val scenario = ScenarioState(scenarioCreateRequest).copy(queued = Instant.EPOCH)
    private val progressingScenario = scenario.copy(phase = QUEUED_INITIALIZE_INTERESTS)

    @Test
    fun `getCurrentScenario - empty`() {
        whenever(scenariosDataSource.getAvailableScenarios()).thenReturn(emptyList())
        assertNull(scenariosQueueService.getCurrentScenario())
    }

    @Test
    fun `getCurrentScenario - multiple progressing throws exception`() {
        whenever(scenariosDataSource.getAvailableScenarios())
            .thenReturn(listOf(progressingScenario, progressingScenario))
        assertThrows<Exception> { scenariosQueueService.getCurrentScenario() }
    }

    @Test
    fun `getCurrentScenario - return progressing`() {
        whenever(scenariosDataSource.getAvailableScenarios())
            .thenReturn(listOf(scenario, progressingScenario))
        assertEquals(progressingScenario, scenariosQueueService.getCurrentScenario())
    }

    @Test
    fun `getCurrentScenario - return one of queued`() {
        whenever(scenariosDataSource.getAvailableScenarios())
            .thenReturn(listOf(scenario.copy(queued = Instant.EPOCH.plusSeconds(1)), scenario))
        assertEquals(scenario, scenariosQueueService.getCurrentScenario())
    }
}
