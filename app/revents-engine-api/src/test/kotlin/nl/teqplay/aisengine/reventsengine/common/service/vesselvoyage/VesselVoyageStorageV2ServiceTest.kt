package nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.datasource.VesselVoyageEntriesV2DataSource
import nl.teqplay.aisengine.reventsengine.common.datasource.VesselVoyageInterestsV2DataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.aisengine.reventsengine.service.scenario.scenarioCreateRequest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class VesselVoyageStorageV2ServiceTest {

    private val vesselVoyageInterestsV2DataSource = mock<VesselVoyageInterestsV2DataSource>()
    private val vesselVoyageEntriesV2DataSource = mock<VesselVoyageEntriesV2DataSource>()
    private val scenariosDataSource = mock<ScenariosDataSource>()
    private val vesselVoyageStorageV2Service = VesselVoyageStorageV2Service(
        vesselVoyageInterestsV2DataSource,
        vesselVoyageEntriesV2DataSource,
        scenariosDataSource
    )

    private val scenarioId = "scenarioId"
    private val imoList = listOf(0)

    init {
        whenever(vesselVoyageEntriesV2DataSource.getImosByScenario(any(), any())).thenReturn(imoList)
    }

    @Test
    fun `getInterestsForMergingByScenario - no metadata, no interests`() {
        whenever(scenariosDataSource.get(any())).thenReturn(ScenarioState(scenarioCreateRequest))

        assertThat(vesselVoyageStorageV2Service.getInterestsForMergingByScenario(scenarioId))
            .isEqualTo(imoList)

        verify(vesselVoyageEntriesV2DataSource, times(1))
            .getImosByScenario(eq(scenarioId), eq(emptySet()))
    }

    @Test
    fun `getInterestsForMergingByScenario - no filtering on port interests`() {
        whenever(scenariosDataSource.get(any())).thenReturn(ScenarioState(scenarioCreateRequest))

        assertThat(vesselVoyageStorageV2Service.getInterestsForMergingByScenario(scenarioId))
            .isEqualTo(imoList)

        verify(vesselVoyageEntriesV2DataSource, times(1))
            .getImosByScenario(eq(scenarioId), eq(emptySet()))
    }

    @Test
    fun `getInterestsForMergingByScenario - with filtering on port interests`() {
        whenever(scenariosDataSource.get(any())).thenReturn(
            ScenarioState(
                scenarioCreateRequest.copy(
                    interests = listOf(InterestArea(InterestArea.Area(AreaIdentifier.AreaType.PORT, "NLRTM_AREA_ID")))
                )
            )
        )

        assertThat(vesselVoyageStorageV2Service.getInterestsForMergingByScenario(scenarioId))
            .isEqualTo(imoList)

        verify(vesselVoyageEntriesV2DataSource, times(1))
            .getImosByScenario(eq(scenarioId), eq(setOf("NLRTM_AREA_ID")))
    }
}
