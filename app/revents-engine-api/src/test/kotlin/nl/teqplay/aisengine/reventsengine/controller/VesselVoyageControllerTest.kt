package nl.teqplay.aisengine.reventsengine.controller

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.reventsengine.BaseTest
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageService
import nl.teqplay.aisengine.reventsengine.model.interest.InterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntry
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.ReventsVesselVoyageVisitQuery
import nl.teqplay.aisengine.reventsengine.service.vesselvoyage.VesselVoyageMergingService
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import org.junit.jupiter.api.Test
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Duration
import java.time.Instant

@AutoConfigureMockMvc
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class VesselVoyageControllerTest(
    private val mockMvc: MockMvc,
    @MockitoBean private val vesselVoyageStorageService: VesselVoyageStorageService,
    @MockitoBean private val vesselVoyageMergingService: VesselVoyageMergingService,

    private val objectMapper: ObjectMapper,
) : BaseTest() {

    private val scenario = "scenario"
    private val imo = 0
    private val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
    private val interests = listOf(InterestVesselVoyage(imo, window, window, false))
    private val visit = mock<Visit>()
    private val voyage = mock<Voyage>()
    private val entries = listOf(visit, voyage)

    @Test
    @WithMockUser
    fun getInterests() {
        whenever(vesselVoyageStorageService.getInterestsByScenario(eq(scenario)))
            .thenReturn(interests.map { ScenarioInterestVesselVoyage(it.imo, it.window, it.window, false, "scenario") })
        mockMvc
            .perform(get("/vesselvoyage/{id}/interests", scenario))
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(interests)))
    }

    @Test
    @WithMockUser
    fun fetchEntries() {
        whenever(vesselVoyageStorageService.fetchEntries(eq(scenario), eq(imo.toString()), eq(window))).thenReturn(entries)
        mockMvc
            .perform(
                post("/vesselvoyage/{id}/entries/imo/{imo}", scenario, imo.toString())
                    .content(objectMapper.writeValueAsString(window))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(entries)))
    }

    @Test
    @WithMockUser
    fun getEntryById() {
        val entryId = "entryId"
        whenever(vesselVoyageStorageService.getEntryById(eq(scenario), eq(entryId))).thenReturn(visit)
        mockMvc
            .perform(
                get("/vesselvoyage/{id}/entries/{entryId}", scenario, entryId)
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(visit)))
    }

    @Test
    @WithMockUser
    fun getEntriesById() {
        val entryIds = listOf("entryId")
        whenever(vesselVoyageStorageService.getEntriesById(eq(scenario), eq(entryIds))).thenReturn(listOf(visit))
        mockMvc
            .perform(
                post("/vesselvoyage/{id}/entries", scenario)
                    .content(objectMapper.writeValueAsString(entryIds))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(listOf(visit))))
    }

    @Test
    @WithMockUser
    fun queryVisits() {
        val query = ReventsVesselVoyageVisitQuery(
            window = window,
            portIds = setOf("NLRTM"),
            imos = setOf("0")
        )
        whenever(vesselVoyageStorageService.queryVisits(eq(scenario), eq(query))).thenReturn(listOf(visit))
        mockMvc
            .perform(
                post("/vesselvoyage/{id}/visits/query", scenario)
                    .content(objectMapper.writeValueAsString(query))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(listOf(visit))))
    }

    @Test
    @WithMockUser
    fun getInterestsForMerging() {
        val result = listOf(imo.toString())
        whenever(vesselVoyageStorageService.getInterestsForMergingByScenario(eq(scenario))).thenReturn(result)
        mockMvc
            .perform(
                get("/vesselvoyage/{id}/merging/interests", scenario)
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(result)))
    }

    @Test
    @WithMockUser
    fun fetchEntriesForMerging() {
        val result = listOf(MergeEntry(window, entries))
        whenever(vesselVoyageMergingService.fetchEntriesForMerging(eq(scenario), eq(imo.toString()))).thenReturn(result)
        mockMvc
            .perform(
                get("/vesselvoyage/{id}/merging/entries/imo/{imo}", scenario, imo.toString())
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isOk)
            .andExpect(content().json(objectMapper.writeValueAsString(result)))
    }
}
