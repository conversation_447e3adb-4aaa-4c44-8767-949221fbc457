package nl.teqplay.aisengine.reventsengine.service.scenario

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosInterestsDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_QUEUED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_INITIALIZE_INTERESTS
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestRelevantShip
import nl.teqplay.aisengine.reventsengine.global.model.InterestRelevantShipInternal
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.properties.InterestsDistributionProperties
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.mock
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ScenariosInterestsServiceTest {

    private val scenariosInterestsDataSource = mock<ScenariosInterestsDataSource>()

    private val properties = InterestsDistributionProperties(
        maxInterestsPerScenario = 2,
        maxForkCount = 2
    )

    private val scenariosInterestsService = ScenariosInterestsService(
        scenariosInterestsDataSource,
        properties
    )

    private val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
    private val scenario = "scenario"

    private val scenarioState = ScenarioState(ScenarioCreateRequest(window, interests = emptyList()))
        .copy(id = scenario, phase = QUEUED_INITIALIZE_INTERESTS)

    data class DistributeInterestsTestData(
        val message: String,
        val interests: List<InterestRelevantShipInternal>,
        val output: ScenariosInterestsService.ScenarioDistribution,

        val useInterestDistribution: Boolean = true,
    )

    @ParameterizedTest
    @MethodSource("distributeInterestsTestData")
    fun distributeInterests(data: DistributeInterestsTestData) {
        val state = when {
            !data.useInterestDistribution -> scenarioState.copy(
                settings = Scenario.Settings(useInterestDistribution = false)
            )
            else -> scenarioState
        }

        assertEquals(
            data.output,
            scenariosInterestsService.distributeInterests(state, data.interests),
            data.message
        )

        // Partials should be preserved.
        assertEquals(
            data.output.copy(
                interests = data.output.interests.map { it.copy(partial = true) }
            ),
            scenariosInterestsService.distributeInterests(state, data.interests.map { it.copy(partial = true) }),
            "using partial: ${data.message}"
        )
    }

    private fun distributeInterestsTestData(): Stream<DistributeInterestsTestData> = Stream.of(
        DistributeInterestsTestData(
            message = "no interests",
            interests = emptyList(),
            output = ScenariosInterestsService.ScenarioDistribution(
                interests = emptyList(),
                childScenarios = emptyList()
            )
        ),
        DistributeInterestsTestData(
            message = "interests are not forked, matches the maximum amount",
            interests = listOf(
                InterestRelevantShipInternal(0, null, window),
                InterestRelevantShipInternal(1, null, window),
            ),
            output = ScenariosInterestsService.ScenarioDistribution(
                interests = listOf(
                    ScenarioInterestRelevantShip(mmsi = 0, imo = null, window = window, scenario = scenario),
                    ScenarioInterestRelevantShip(mmsi = 1, imo = null, window = window, scenario = scenario),
                ),
                childScenarios = emptyList()
            )
        ),
        DistributeInterestsTestData(
            message = "interests are not forked due to interest distribution disabled",
            useInterestDistribution = false,
            interests = listOf(
                InterestRelevantShipInternal(0, null, window),
                InterestRelevantShipInternal(1, null, window),
                InterestRelevantShipInternal(2, null, window),
                InterestRelevantShipInternal(3, null, window),
                InterestRelevantShipInternal(4, null, window),
            ),
            output = ScenariosInterestsService.ScenarioDistribution(
                interests = listOf(
                    ScenarioInterestRelevantShip(mmsi = 0, imo = null, window = window, scenario = scenario),
                    ScenarioInterestRelevantShip(mmsi = 1, imo = null, window = window, scenario = scenario),
                    ScenarioInterestRelevantShip(mmsi = 2, imo = null, window = window, scenario = scenario),
                    ScenarioInterestRelevantShip(mmsi = 3, imo = null, window = window, scenario = scenario),
                    ScenarioInterestRelevantShip(mmsi = 4, imo = null, window = window, scenario = scenario),
                )
            )
        ),
        DistributeInterestsTestData(
            message = "interests are forked due to distribution properties, but it's capped at 2 children",
            interests = listOf(
                InterestRelevantShipInternal(0, null, window),
                InterestRelevantShipInternal(1, null, window),
                InterestRelevantShipInternal(2, null, window),
                InterestRelevantShipInternal(3, null, window),
                InterestRelevantShipInternal(4, null, window),
            ),
            output = ScenariosInterestsService.ScenarioDistribution(
                interests = listOf(
                    ScenarioInterestRelevantShip(
                        mmsi = 0,
                        imo = null,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 1,
                        imo = null,
                        window = window,
                        parent = "$scenario.1",
                        scenarios = setOf("$scenario.1", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 2,
                        imo = null,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 3,
                        imo = null,
                        window = window,
                        parent = "$scenario.1",
                        scenarios = setOf("$scenario.1", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 4,
                        imo = null,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    )
                ),
                childScenarios = listOf(
                    scenarioState.copy(id = "$scenario.0", parent = scenario, phase = INITIALIZING_QUEUED),
                    scenarioState.copy(id = "$scenario.1", parent = scenario, phase = INITIALIZING_QUEUED),
                )
            )
        ),
        DistributeInterestsTestData(
            message = "interests are forked, but grouped by IMO",
            interests = listOf(
                InterestRelevantShipInternal(0, 0, window),
                InterestRelevantShipInternal(1, 0, window),
                InterestRelevantShipInternal(2, 1, window),
                InterestRelevantShipInternal(3, 1, window),
                InterestRelevantShipInternal(4, 2, window),
            ),
            output = ScenariosInterestsService.ScenarioDistribution(
                interests = listOf(
                    ScenarioInterestRelevantShip(
                        mmsi = 4,
                        imo = 2,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 0,
                        imo = 0,
                        window = window,
                        parent = "$scenario.1",
                        scenarios = setOf("$scenario.1", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 1,
                        imo = 0,
                        window = window,
                        parent = "$scenario.1",
                        scenarios = setOf("$scenario.1", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 2,
                        imo = 1,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 3,
                        imo = 1,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    )
                ),
                childScenarios = listOf(
                    scenarioState.copy(id = "$scenario.0", parent = scenario, phase = INITIALIZING_QUEUED),
                    scenarioState.copy(id = "$scenario.1", parent = scenario, phase = INITIALIZING_QUEUED),
                )
            )
        ),
        DistributeInterestsTestData(
            message = "interests are forked, but grouped by IMO and MMSI, which might overlap",
            interests = listOf(
                InterestRelevantShipInternal(0, 0, window),
                InterestRelevantShipInternal(0, 0, window),
                InterestRelevantShipInternal(0, null, window),
                InterestRelevantShipInternal(0, null, window),
                InterestRelevantShipInternal(0, 2, window),
            ),
            output = ScenariosInterestsService.ScenarioDistribution(
                interests = listOf(
                    ScenarioInterestRelevantShip(
                        mmsi = 0,
                        imo = 2,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 0,
                        imo = 0,
                        window = window,
                        parent = "$scenario.1",
                        scenarios = setOf("$scenario.1", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 0,
                        imo = 0,
                        window = window,
                        parent = "$scenario.1",
                        scenarios = setOf("$scenario.1", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 0,
                        imo = null,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 0,
                        imo = null,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    )
                ),
                childScenarios = listOf(
                    scenarioState.copy(id = "$scenario.0", parent = scenario, phase = INITIALIZING_QUEUED),
                    scenarioState.copy(id = "$scenario.1", parent = scenario, phase = INITIALIZING_QUEUED),
                )
            )
        ),
        DistributeInterestsTestData(
            message = "interests are forked, but grouped by IMO and MMSI if IMO doesn't exist",
            interests = listOf(
                InterestRelevantShipInternal(0, 0, window),
                InterestRelevantShipInternal(1, 0, window),
                InterestRelevantShipInternal(
                    mmsi = 2,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(0, ChronoUnit.DAYS), Duration.ofDays(3))
                ),
                InterestRelevantShipInternal(
                    mmsi = 2,
                    imo = null,
                    window = TimeWindow(Instant.EPOCH.plus(4, ChronoUnit.DAYS), Duration.ofDays(3))
                ),
                InterestRelevantShipInternal(4, 2, window),
            ),
            output = ScenariosInterestsService.ScenarioDistribution(
                interests = listOf(
                    ScenarioInterestRelevantShip(
                        mmsi = 2,
                        imo = null,
                        window = TimeWindow(Instant.EPOCH.plus(0, ChronoUnit.DAYS), Duration.ofDays(3)),
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 2,
                        imo = null,
                        window = TimeWindow(Instant.EPOCH.plus(4, ChronoUnit.DAYS), Duration.ofDays(3)),
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 4,
                        imo = 2,
                        window = window,
                        parent = "$scenario.1",
                        scenarios = setOf("$scenario.1", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 0,
                        imo = 0,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 1,
                        imo = 0,
                        window = window,
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    )
                ),
                childScenarios = listOf(
                    scenarioState.copy(id = "$scenario.0", parent = scenario, phase = INITIALIZING_QUEUED),
                    scenarioState.copy(id = "$scenario.1", parent = scenario, phase = INITIALIZING_QUEUED),
                )
            )
        ),
        DistributeInterestsTestData(
            message = "interests are forked evenly based on their time window",
            interests = listOf(
                InterestRelevantShipInternal(0, null, TimeWindow(Instant.EPOCH, Duration.ofDays(1))),
                InterestRelevantShipInternal(3, null, TimeWindow(Instant.EPOCH, Duration.ofDays(4))),
                InterestRelevantShipInternal(2, null, TimeWindow(Instant.EPOCH, Duration.ofDays(3))),
                InterestRelevantShipInternal(1, null, TimeWindow(Instant.EPOCH, Duration.ofDays(2))),
                InterestRelevantShipInternal(4, null, TimeWindow(Instant.EPOCH, Duration.ofDays(5))),
            ),
            output = ScenariosInterestsService.ScenarioDistribution(
                interests = listOf(
                    ScenarioInterestRelevantShip(
                        mmsi = 0,
                        imo = null,
                        window = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 1,
                        imo = null,
                        window = TimeWindow(Instant.EPOCH, Duration.ofDays(2)),
                        parent = "$scenario.1",
                        scenarios = setOf("$scenario.1", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 2,
                        imo = null,
                        window = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 3,
                        imo = null,
                        window = TimeWindow(Instant.EPOCH, Duration.ofDays(4)),
                        parent = "$scenario.1",
                        scenarios = setOf("$scenario.1", scenario)
                    ),
                    ScenarioInterestRelevantShip(
                        mmsi = 4,
                        imo = null,
                        window = TimeWindow(Instant.EPOCH, Duration.ofDays(5)),
                        parent = "$scenario.0",
                        scenarios = setOf("$scenario.0", scenario)
                    )
                ),
                childScenarios = listOf(
                    scenarioState.copy(id = "$scenario.0", parent = scenario, phase = INITIALIZING_QUEUED),
                    scenarioState.copy(id = "$scenario.1", parent = scenario, phase = INITIALIZING_QUEUED),
                )
            )
        )
    )
}
