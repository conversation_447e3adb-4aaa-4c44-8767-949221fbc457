package nl.teqplay.aisengine.reventsengine.service.scenario

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FORKED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PRUNED
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageService
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageV2Service
import nl.teqplay.aisengine.reventsengine.properties.PruneProperties
import nl.teqplay.aisengine.reventsengine.service.event.ActualEventsService
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class ScenariosPruneServiceTest {

    private val scenariosDataSource = mock<ScenariosDataSource>()
    private val pruneProperties = mock<PruneProperties>()
    private val actualEventsService = mock<ActualEventsService>()
    private val vesselVoyageStorageService = mock<VesselVoyageStorageService>()
    private val vesselVoyageStorageV2Service = mock<VesselVoyageStorageV2Service>()
    private val scenariosPruneService = ScenariosPruneService(
        scenariosDataSource,
        pruneProperties,
        actualEventsService,
        vesselVoyageStorageService,
        vesselVoyageStorageV2Service,
    )

    private val scenarioId = "scenario"
    private val scenario = ScenarioState(scenarioCreateRequest).copy(id = scenarioId)

    @Test
    fun `prune - updates phase=PRUNED and pruned timestamp`() {
        val result = scenariosPruneService.prune(scenario)
        assertEquals(PRUNED, result.phase)
        assertNotNull(result.pruned)

        verify(actualEventsService, times(1)).prune(eq(scenarioId))
        verify(vesselVoyageStorageService, times(1)).prune(eq(scenarioId))
        verify(vesselVoyageStorageV2Service, times(1)).prune(eq(scenarioId))
        verify(scenariosDataSource, times(1)).save(any())
    }

    @Test
    fun `prune - forked scenario recursively prunes children`() {
        whenever(scenariosDataSource.getChildScenarios(anyString())).thenReturn(listOf(scenario, scenario))

        val result = scenariosPruneService.prune(scenario.copy(phase = FORKED))
        assertEquals(PRUNED, result.phase)
        assertNotNull(result.pruned)

        verify(scenariosDataSource, times(1)).getChildScenarios(eq(scenarioId))
        verify(actualEventsService, times(3)).prune(eq(scenarioId))
        verify(vesselVoyageStorageService, times(3)).prune(eq(scenarioId))
        verify(vesselVoyageStorageV2Service, times(3)).prune(eq(scenarioId))
        verify(scenariosDataSource, times(3)).save(any())
    }

    @Test
    fun `scheduledPruning - skip if no prunable scenarios`() {
        whenever(scenariosDataSource.getPrunableScenarios(any())).thenReturn(emptyList())

        scenariosPruneService.scheduledPruning()

        verify(actualEventsService, never()).prune(any())
        verify(vesselVoyageStorageService, never()).prune(any())
        verify(vesselVoyageStorageV2Service, never()).prune(any())
        verify(scenariosDataSource, never()).save(any())
    }

    @Test
    fun `scheduledPruning - scenarios should be pruned`() {
        whenever(scenariosDataSource.getPrunableScenarios(any())).thenReturn(listOf(scenario))

        scenariosPruneService.scheduledPruning()

        verify(actualEventsService, times(1)).prune(eq(scenarioId))
        verify(vesselVoyageStorageService, times(1)).prune(eq(scenarioId))
        verify(vesselVoyageStorageV2Service, times(1)).prune(eq(scenarioId))
        verify(scenariosDataSource, times(1)).save(any())
    }
}
