package nl.teqplay.aisengine.reventsengine.service.scenario

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCancelledException
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CANCELLED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FINISHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FORKED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_QUEUED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_INITIALIZE_INTERESTS
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_SCENARIO_NOTIFY_EXTERNAL
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestRelevantShip
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosMetadataService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosPhaseService
import nl.teqplay.aisengine.reventsengine.global.model.InterestRelevantResolvedInternal
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING_V2
import nl.teqplay.aisengine.reventsengine.properties.NotificationProperties
import nl.teqplay.aisengine.reventsengine.service.external.VesselVoyageService
import nl.teqplay.aisengine.reventsengine.service.scenario.resolver.ScenariosResolverService
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Instant

class ScenariosPhaseServiceImplTest {

    private val scenariosResolverService = mock<ScenariosResolverService>()
    private val scenariosInterestsService = mock<ScenariosInterestsService>()
    private val scenariosMetadataService = mock<ScenariosMetadataService>()
    private val scenariosDataSource = mock<ScenariosDataSource>()
    private val vesselVoyageService = mock<VesselVoyageService>()
    private val notificationProperties = mock<NotificationProperties>()

    private val scenariosPhaseService = ScenariosPhaseServiceImpl(
        scenariosResolverService,
        scenariosInterestsService,
        scenariosMetadataService,
        scenariosDataSource,
        notificationProperties,
        vesselVoyageService,
    )

    private val scenario = ScenarioState(scenarioCreateRequest).copy(
        status = ScenarioState.InternalStatus(Instant.EPOCH),
        events = ScenarioEvent.values().toSet()
    )
    private val defaultReturn = ScenariosPhaseService.RunPhaseResult(scenario.status, true)

    @Test
    fun `runPhase - queued,finished,crashed or noop`() {
        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = QUEUED)))
        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = QUEUED_END)))

        ScenarioState.InternalPhase.values()
            .filter { it > ScenarioState.InternalPhase.QUEUED_END }
            .forEach { phase ->
                if (phase == CANCELLED) assertThrows<ScenarioCancelledException> {
                    scenariosPhaseService.runPhase(scenario.copy(phase = phase))
                } else assertThrows<ScenarioCrashedException> {
                    scenariosPhaseService.runPhase(scenario.copy(phase = phase))
                }
            }
    }

    @Test
    fun `runPhase - init interests`() {
        val newInterests = listOf(
            ScenarioInterestRelevantShip(
                mmsi = 0,
                imo = null,
                window = TimeWindow(Instant.EPOCH, Instant.EPOCH),
                scenario = scenario.id
            )
        )

        whenever(scenariosResolverService.resolveInterests(any()))
            .thenReturn(InterestRelevantResolvedInternal(emptyList(), emptySet(), emptyList()))
        whenever(scenariosInterestsService.distributeInterests(any(), any()))
            .thenReturn(ScenariosInterestsService.ScenarioDistribution(newInterests))
        doNothing().whenever(scenariosInterestsService).save(any())
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(newInterests)

        assertNull(scenariosPhaseService.runPhase(scenario.copy(phase = QUEUED_INITIALIZE_INTERESTS)).skipToPhase)

        verify(scenariosResolverService, times(1)).resolveInterests(any())
        verify(scenariosInterestsService, times(1)).save(any())
    }

    @Test
    fun `runPhase - init interests - no interests, skip to end`() {
        whenever(scenariosResolverService.resolveInterests(any()))
            .thenReturn(InterestRelevantResolvedInternal(emptyList(), emptySet(), emptyList()))
        whenever(scenariosInterestsService.distributeInterests(any(), any()))
            .thenReturn(ScenariosInterestsService.ScenarioDistribution(emptyList()))
        doNothing().whenever(scenariosInterestsService).save(any())
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(emptyList())

        assertEquals(
            FINISHED,
            scenariosPhaseService.runPhase(scenario.copy(phase = QUEUED_INITIALIZE_INTERESTS)).skipToPhase
        )

        verify(scenariosResolverService, times(1)).resolveInterests(any())
        verify(scenariosInterestsService, never()).save(any())
    }

    @Test
    fun `runPhase - init interests - inherits scenario, crashes on parent not found`() {
        whenever(scenariosDataSource.get(any())).thenReturn(null)
        assertThrows<ScenarioCrashedException> {
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = QUEUED_INITIALIZE_INTERESTS,
                    inherit = "inherit"
                )
            )
        }
    }

    @Test
    fun `runPhase - init interests - inherits scenario, skip to next`() {
        whenever(scenariosDataSource.get(eq("inherit"))).thenReturn(
            scenario.copy(id = "inherit", phase = FINISHED)
        )
        val result = scenariosPhaseService.runPhase(
            scenario.copy(
                phase = QUEUED_INITIALIZE_INTERESTS,
                inherit = "inherit"
            )
        )
        assertNull(result.skipToPhase)
        assertTrue(result.moveToNextPhase)
        verify(scenariosDataSource, times(1)).get(any())
        verify(scenariosDataSource, never()).getChildScenarios(anyString())
    }

    @Test
    fun `runPhase - init interests - inherits forked scenario, crashes on no children found`() {
        whenever(scenariosDataSource.get(eq("inherit"))).thenReturn(
            scenario.copy(id = "inherit", phase = FORKED)
        )
        whenever(scenariosDataSource.getChildScenarios(anyString())).thenReturn(emptyList())
        assertThrows<ScenarioCrashedException> {
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = QUEUED_INITIALIZE_INTERESTS,
                    inherit = "inherit"
                )
            )
        }
    }

    @Test
    fun `runPhase - init interests - inherits forked scenario, create forks inheriting the children, and skip to end`() {
        whenever(scenariosDataSource.get(eq("inherit"))).thenReturn(
            scenario.copy(id = "inherit", phase = FORKED)
        )
        whenever(scenariosDataSource.getChildScenarios(anyString())).thenReturn(
            listOf(
                scenario.copy(id = "child.0", phase = FINISHED),
                scenario.copy(id = "child.1", phase = FINISHED),
            )
        )
        whenever(scenariosInterestsService.createChildScenario(any(), any())).thenCallRealMethod()

        var childScenarios: List<ScenarioState> = emptyList()
        whenever(scenariosDataSource.saveMany(any())).then {
            childScenarios = it.getArgument(0)
            return@then Unit
        }

        val result = scenariosPhaseService.runPhase(
            scenario.copy(
                phase = QUEUED_INITIALIZE_INTERESTS,
                inherit = "inherit"
            )
        )

        assertTrue(result.moveToNextPhase)
        assertNull(result.skipToPhase)
        assertTrue(result.forked)
        verify(scenariosDataSource, times(1)).get(any())
        verify(scenariosDataSource, times(1)).getChildScenarios(eq("inherit"))
        verify(scenariosInterestsService, times(2)).createChildScenario(any(), any())
        verify(scenariosDataSource, times(1)).saveMany(any())

        val expectedChildScenarios = listOf(
            scenario.copy(
                id = "${scenario.id}.0",
                phase = INITIALIZING_QUEUED,
                inherit = "child.0",
                parent = scenario.id
            ),
            scenario.copy(
                id = "${scenario.id}.1",
                phase = INITIALIZING_QUEUED,
                inherit = "child.1",
                parent = scenario.id
            ),
        )
        assertEquals(expectedChildScenarios, childScenarios)
    }

    @Test
    fun `runPhase - init interests - forked scenario`() {
        val newInterests = listOf(
            ScenarioInterestRelevantShip(
                mmsi = 0,
                imo = null,
                window = TimeWindow(Instant.EPOCH, Instant.EPOCH),
                scenario = scenario.id
            )
        )
        val childScenarios = listOf(
            mock<ScenarioState>(),
            mock<ScenarioState>(),
        )

        whenever(scenariosResolverService.resolveInterests(any()))
            .thenReturn(InterestRelevantResolvedInternal(emptyList(), emptySet(), emptyList()))
        whenever(scenariosInterestsService.distributeInterests(any(), any()))
            .thenReturn(ScenariosInterestsService.ScenarioDistribution(newInterests, childScenarios))
        doNothing().whenever(scenariosDataSource).saveMany(any())
        doNothing().whenever(scenariosInterestsService).save(any())
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(newInterests)

        val runPhaseResult = scenariosPhaseService.runPhase(scenario.copy(phase = QUEUED_INITIALIZE_INTERESTS))

        assertNull(runPhaseResult.skipToPhase)
        assertTrue(runPhaseResult.forked)

        verify(scenariosResolverService, times(1)).resolveInterests(any())
        verify(scenariosDataSource, times(1))
            .saveMany(eq(childScenarios))
        verify(scenariosInterestsService, times(1)).save(any())
    }

    @Test
    fun `runPhase - notify - noop if no guarantees`() {
        val runPhaseResult = scenariosPhaseService.runPhase(scenario.copy(phase = QUEUED_SCENARIO_NOTIFY_EXTERNAL))

        assertNull(runPhaseResult.skipToPhase)
        assertTrue(runPhaseResult.moveToNextPhase)

        verify(vesselVoyageService, never()).trackScenario(eq(scenario.id))
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `runPhase - notify - VesselVoyage V1 merging guarantee`(sendHttp: Boolean) {
        whenever(notificationProperties.sendHttp).thenReturn(sendHttp)

        val runPhaseResult = scenariosPhaseService.runPhase(
            scenario.copy(
                phase = QUEUED_SCENARIO_NOTIFY_EXTERNAL,
                guarantees = setOf(VESSEL_VOYAGE_MERGING)
            )
        )

        assertNull(runPhaseResult.skipToPhase)
        assertTrue(runPhaseResult.moveToNextPhase)

        val invocations = if (sendHttp) times(1) else never()
        verify(vesselVoyageService, invocations).trackScenario(eq(scenario.id))
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `runPhase - notify - VesselVoyage V2 merging guarantee`(sendHttp: Boolean) {
        whenever(notificationProperties.sendHttp).thenReturn(sendHttp)

        val runPhaseResult = scenariosPhaseService.runPhase(
            scenario.copy(
                phase = QUEUED_SCENARIO_NOTIFY_EXTERNAL,
                guarantees = setOf(VESSEL_VOYAGE_MERGING_V2)
            )
        )

        assertNull(runPhaseResult.skipToPhase)
        assertTrue(runPhaseResult.moveToNextPhase)

        val invocations = if (sendHttp) times(1) else never()
        verify(vesselVoyageService, invocations).trackScenario(eq(scenario.id))
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `runPhase - notify - both VesselVoyage V1 & V2 merging guarantees`(sendHttp: Boolean) {
        whenever(notificationProperties.sendHttp).thenReturn(sendHttp)

        val runPhaseResult = scenariosPhaseService.runPhase(
            scenario.copy(
                phase = QUEUED_SCENARIO_NOTIFY_EXTERNAL,
                guarantees = setOf(VESSEL_VOYAGE_MERGING, VESSEL_VOYAGE_MERGING_V2)
            )
        )

        assertNull(runPhaseResult.skipToPhase)
        assertTrue(runPhaseResult.moveToNextPhase)

        val invocations = if (sendHttp) times(1) else never()
        verify(vesselVoyageService, invocations).trackScenario(eq(scenario.id))
    }
}
