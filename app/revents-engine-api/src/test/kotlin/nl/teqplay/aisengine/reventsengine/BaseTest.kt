package nl.teqplay.aisengine.reventsengine

import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.storage.ship.area.implementation.ShipHistoryByAreaReadCache
import nl.teqplay.aisengine.reventsengine.service.scenario.ScenariosQueueService
import nl.teqplay.aisengine.reventsengine.service.scenario.resolver.ScenariosPolygonResolverService
import nl.teqplay.skeleton.csi.client.CsiShipClient
import org.bson.conversions.Bson
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.context.junit.jupiter.SpringExtension

@SpringBootTest
@ContextConfiguration
@ExtendWith(SpringExtension::class)
@MockitoBean(
    types = [
        ShipHistoryByAreaReadCache::class,
        ScenariosPolygonResolverService::class,
        ScenariosQueueService::class,
        CsiShipClient::class,
    ]
)
abstract class BaseTest {

    @TestConfiguration
    @Import(ReventsEngineApi::class)
    class Configuration {

        @Bean
        @Primary
        fun mongoDatabase() = mock<MongoDatabase> {
            val mockCollection = mock<MongoCollection<Any>> {
                on { createIndex(any<Bson>(), any()) } doReturn ""
            }
            on { getCollection(any(), any<Class<Any>>()) } doReturn mockCollection
        }
    }
}
