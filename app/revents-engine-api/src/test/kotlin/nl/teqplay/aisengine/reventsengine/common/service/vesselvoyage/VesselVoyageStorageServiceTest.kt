package nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage

import nl.teqplay.aisengine.reventsengine.common.datasource.VesselVoyageEntriesDataSource
import nl.teqplay.aisengine.reventsengine.common.datasource.VesselVoyageInterestsDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioMetadata
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosMetadataService
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class VesselVoyageStorageServiceTest {

    private val vesselVoyageInterestsDataSource = mock<VesselVoyageInterestsDataSource>()
    private val vesselVoyageEntriesDataSource = mock<VesselVoyageEntriesDataSource>()
    private val scenariosMetadataService = mock<ScenariosMetadataService>()
    private val vesselVoyageStorageService = VesselVoyageStorageService(
        vesselVoyageInterestsDataSource,
        vesselVoyageEntriesDataSource,
        scenariosMetadataService
    )

    private val scenarioId = "scenarioId"
    private val imoList = listOf("imo")

    init {
        whenever(vesselVoyageEntriesDataSource.getImosByScenario(any(), any())).thenReturn(imoList)
    }

    @Test
    fun `getInterestsForMergingByScenario - no metadata, no unlocodes`() {
        whenever(scenariosMetadataService.getMetadataByScenario(any())).thenReturn(null)

        assertThat(vesselVoyageStorageService.getInterestsForMergingByScenario(scenarioId))
            .isEqualTo(imoList)

        verify(vesselVoyageEntriesDataSource, times(1))
            .getImosByScenario(eq(scenarioId), eq(emptySet()))
    }

    @Test
    fun `getInterestsForMergingByScenario - no filtering on unlocodes`() {
        whenever(scenariosMetadataService.getMetadataByScenario(any())).thenReturn(
            ScenarioMetadata(
                id = "id",
                unlocodes = emptySet(),
                areas = emptyList()
            )
        )

        assertThat(vesselVoyageStorageService.getInterestsForMergingByScenario(scenarioId))
            .isEqualTo(imoList)

        verify(vesselVoyageEntriesDataSource, times(1))
            .getImosByScenario(eq(scenarioId), eq(emptySet()))
    }

    @Test
    fun `getInterestsForMergingByScenario - with filtering on unlocodes`() {
        whenever(scenariosMetadataService.getMetadataByScenario(any())).thenReturn(
            ScenarioMetadata(
                id = "id",
                unlocodes = setOf("NLRTM"),
                areas = emptyList()
            )
        )

        assertThat(vesselVoyageStorageService.getInterestsForMergingByScenario(scenarioId))
            .isEqualTo(imoList)

        verify(vesselVoyageEntriesDataSource, times(1))
            .getImosByScenario(eq(scenarioId), eq(setOf("NLRTM")))
    }
}
