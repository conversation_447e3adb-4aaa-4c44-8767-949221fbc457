package nl.teqplay.aisengine.reventsengine.service.scenario

import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.skeleton.model.TimeWindow
import java.time.Instant

val scenarioCreateRequest = ScenarioCreateRequest(
    window = TimeWindow(Instant.EPOCH, Instant.EPOCH),
    windowMargin = Scenario.WindowMargin(),
    events = emptySet(),
    interests = emptyList()
)
