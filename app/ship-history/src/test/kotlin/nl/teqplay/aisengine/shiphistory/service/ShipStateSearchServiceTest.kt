package nl.teqplay.aisengine.shiphistory.service

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.datasource.ShipHistoryStateDataSource
import nl.teqplay.aisengine.datasource.ShipHistoryStateWrapper
import nl.teqplay.aisengine.model.ShipHistoryState
import nl.teqplay.aisengine.shiphistory.exception.ShipStateNotFullyInitializedException
import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.util.createBoundingBoxFromCircle
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import reactor.kotlin.test.verifyError
import reactor.test.StepVerifier
import java.time.Duration
import java.time.Instant

@TestInstance(TestInstance.Lifecycle.PER_METHOD)
class ShipStateSearchServiceTest {
    private val mmsi = 0
    private val imo = 1
    private val newImo = 2

    private val location = Location(52.0, 4.0)
    private val locationKey = "52,4"

    private val messageNew = AisCurrentMessage(
        message = AisMessage(
            mmsi = mmsi,
            imo = imo,
            messageTime = Instant.EPOCH,
            sources = setOf("AIS:TEST:POSITION", "AIS:TEST:STATIC"),
            location = location,
            shipType = AisMessage.ShipType.UNDEFINED,
            usingDataTerminal = false
        ),
        derived = null
    )
    private val messageOld = messageNew.copy(
        mmsi = 100,
        messageTime = Instant.EPOCH.minusSeconds(1)
    )

    private val state = ShipHistoryState(
        position = AisPositionWrapper(
            timestamp = Instant.EPOCH,
            source = "TEST",
            subSource = null,
            message = AisPositionMessage(mmsi = mmsi, location = location)
        ),
        static = AisStaticWrapper(
            timestamp = Instant.EPOCH,
            source = "TEST",
            subSource = null,
            message = AisStaticMessage(mmsi = mmsi, imo = imo)
        )
    )

    private val stateDataSource = mock<ShipHistoryStateDataSource>().also { dataSource ->
        val defaultResult = listOf(
            ShipHistoryStateWrapper(
                _id = 100,
                state = state.copy(
                    position = state.position?.copy(
                        timestamp = messageOld.messageTime,
                        message = AisPositionMessage(mmsi = 100, location = location)
                    ),
                    static = state.static?.copy(
                        timestamp = messageOld.messageTime,
                        message = AisStaticMessage(mmsi = 100, imo = imo)
                    )
                )
            ),
            ShipHistoryStateWrapper(_id = mmsi, state = state)
        )
        whenever(dataSource.findStateByImo(eq(imo))).thenReturn(defaultResult)
        whenever(dataSource.findStateByLocationKeys(argThat { arg -> arg.contains(locationKey) }))
            .thenReturn(defaultResult.asIterable())
    }

    private val shipStateSearchService = ShipStateSearchService(stateDataSource = stateDataSource)
        .also { it.isStateFullyLoaded = true }

    private val loadingShipStateSearchService = ShipStateSearchService(stateDataSource = stateDataSource)
        .also { it.isStateFullyLoaded = false }

    @Test
    fun updateAndDelete() {
        // assert empty state
        StepVerifier.create(shipStateSearchService.getByMmsi(mmsi)).verifyComplete()
        StepVerifier.create(shipStateSearchService.getByMmsiList(listOf(mmsi))).verifyComplete()
        StepVerifier.create(shipStateSearchService.getByImo(imo)).verifyComplete()
        StepVerifier.create(shipStateSearchService.getByImoList(listOf(imo))).verifyComplete()

        shipStateSearchService.update(messageNew)

        // assert updated added ship
        StepVerifier.create(shipStateSearchService.getByMmsi(mmsi)).expectNext(messageNew).verifyComplete()
        StepVerifier.create(shipStateSearchService.getByMmsiList(listOf(mmsi))).expectNext(messageNew).verifyComplete()
        StepVerifier.create(shipStateSearchService.getByImo(imo)).expectNext(messageNew).verifyComplete()
        StepVerifier.create(shipStateSearchService.getByImoList(listOf(imo))).expectNext(messageNew).verifyComplete()

        shipStateSearchService.delete(mmsi)

        // assert empty state (after deletion)
        StepVerifier.create(shipStateSearchService.getByMmsi(mmsi)).verifyComplete()
        StepVerifier.create(shipStateSearchService.getByMmsiList(listOf(mmsi))).verifyComplete()
        StepVerifier.create(shipStateSearchService.getByImo(imo)).verifyComplete()
        StepVerifier.create(shipStateSearchService.getByImoList(listOf(imo))).verifyComplete()
    }

    @Test
    fun updateImo() {
        shipStateSearchService.update(messageNew)
        StepVerifier.create(shipStateSearchService.getByMmsi(mmsi))
            .expectNext(messageNew)
            .verifyComplete()

        StepVerifier.create(shipStateSearchService.getByImo(imo))
            .expectNext(messageNew)
            .verifyComplete()

        val messageWithUpdatedImo = messageNew.copy(imo = newImo)

        shipStateSearchService.update(messageWithUpdatedImo)

        StepVerifier.create(shipStateSearchService.getByMmsi(mmsi))
            .expectNext(messageWithUpdatedImo)
            .verifyComplete()

        // old IMO is now empty, new IMO is populated
        StepVerifier.create(shipStateSearchService.getByImo(imo))
            .verifyComplete()

        StepVerifier.create(shipStateSearchService.getByImo(newImo))
            .expectNext(messageWithUpdatedImo)
            .verifyComplete()
    }

    @Test
    fun getInBoundingBox() {
        shipStateSearchService.update(messageNew)

        StepVerifier.create(shipStateSearchService.getInBoundingBox(createBoundingBoxFromCircle(location, 5.0), true, null))
            .expectNext(messageNew)
            .verifyComplete()

        StepVerifier.create(shipStateSearchService.getInBoundingBox(createBoundingBoxFromCircle(Location(0.0, 0.0), 5.0), true, null))
            .verifyComplete()
    }

    @Test
    fun testGetByImoViaDatabase() {
        StepVerifier.create(loadingShipStateSearchService.getByImo(imo))
            .expectNext(messageNew)
            .expectNext(messageOld)
            .verifyComplete()
    }

    @Test
    fun testGetByImoViaDatabaseNoData() {
        StepVerifier.create(loadingShipStateSearchService.getByImo(0))
            .verifyComplete()
    }

    @Test
    fun testGetAllNotAged() {
        shipStateSearchService.update(messageNew)

        StepVerifier.create(shipStateSearchService.getAll(false, null))
            .verifyComplete()
    }

    @Test
    fun testGetAllAged() {
        shipStateSearchService.update(messageNew)

        StepVerifier.create(shipStateSearchService.getAll(true, null))
            .expectNext(messageNew)
            .verifyComplete()
    }

    @Test
    fun testShouldThrowAnExceptionWhenGetAllAndNotFullyLoaded() {
        StepVerifier.create(loadingShipStateSearchService.getAll(null, Duration.ofHours(5)))
            .verifyError(ShipStateNotFullyInitializedException::class)
        StepVerifier.create(loadingShipStateSearchService.getAll(false, Duration.ofHours(5)))
            .verifyError(ShipStateNotFullyInitializedException::class)
        StepVerifier.create(loadingShipStateSearchService.getAll(true, Duration.ofHours(5)))
            .verifyError(ShipStateNotFullyInitializedException::class)
    }

    @Test
    fun testGetInBoundingBoxViaDatabaseNoData() {
        StepVerifier.create(loadingShipStateSearchService.getInBoundingBox(BoundingBox(Location(0.0, 0.0), Location(2.0, 2.0)), null, null))
            .verifyComplete()
    }

    @Test
    fun testGetInBoundingBoxViaDatabaseBoundingBoxOnEdge() {
        StepVerifier.create(loadingShipStateSearchService.getInBoundingBox(BoundingBox(location, location), true, null))
            .expectNext(messageOld)
            .expectNext(messageNew)
            .verifyComplete()
    }

    @Test
    fun testGetInBoundingBoxViaDatabaseBoundingBoxAroundResult() {
        StepVerifier.create(loadingShipStateSearchService.getInBoundingBox(BoundingBox(Location(51.0, 3.0), Location(53.0, 5.0)), true, null))
            .expectNext(messageOld)
            .expectNext(messageNew)
            .verifyComplete()
    }
}
