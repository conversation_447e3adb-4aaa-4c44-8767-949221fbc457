package nl.teqplay.aisengine.util

import nl.teqplay.aisengine.shiphistory.util.setUpLocationKeys
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class LocationKeyUtilsTest {
    private fun locationKeysTestData() = Stream.of(
        // All variants with 0.0
        Arguments.of(Location(0.0, 0.0), Location(0.0, 0.0), listOf("0,0")),
        Arguments.of(Location(-0.0, -0.0), Location(-0.0, -0.0), listOf("0,0")),
        Arguments.of(Location(-0.0, -0.0), Location(0.0, 0.0), listOf("0,0")),
        Arguments.of(Location(0.0, 0.0), Location(-0.0, -0.0), listOf("0,0")),

        // Decimal numbers should always floor
        Arguments.of(Location(0.0, 0.0), Location(0.1, 0.0), listOf("0,0")),
        Arguments.of(Location(0.0, 0.0), Location(0.5, 0.0), listOf("0,0")),
        Arguments.of(Location(0.0, 0.0), Location(0.9, 0.0), listOf("0,0")),

        // Normal ranges
        Arguments.of(Location(0.0, 0.0), Location(2.0, 2.0), listOf("0,0", "0,1", "0,2", "1,0", "1,1", "1,2", "2,0", "2,1", "2,2")),
        Arguments.of(Location(0.0, 0.0), Location(2.0, 0.0), listOf("0,0", "1,0", "2,0")),
        Arguments.of(Location(0.0, 0.0), Location(0.0, 2.0), listOf("0,0", "0,1", "0,2")),

        // Ranges going around the world, where longitude should be normalized
        Arguments.of(Location(0.0, -181.0), Location(0.0, -179.0), listOf("0,179", "0,-180", "0,-179")),
        Arguments.of(Location(0.0, 179.0), Location(0.0, 181.0), listOf("0,179", "0,-180", "0,-179"))
    )

    @ParameterizedTest
    @MethodSource("locationKeysTestData")
    fun testSetUpLocationKeys(bottomLeft: Location, topRight: Location, expected: List<String>) {
        val result = setUpLocationKeys(
            lat1 = bottomLeft.lat,
            lat2 = topRight.lat,
            lon1 = bottomLeft.lon,
            lon2 = topRight.lon
        )
        Assertions.assertEquals(expected, result)
    }
}
