package nl.teqplay.aisengine.stopmonitor.model

import nl.teqplay.skeleton.model.Location
import org.springframework.data.annotation.Id
import org.springframework.data.redis.core.RedisHash
import java.time.Instant

/**
 * State for a stop.
 */
@RedisHash("stop-trace-state")
data class StopTraceState(
    /**
     * [mmsi] of the ship.
     */
    @Id val mmsi: Int,

    /**
     * A list representing points in a [trace] of the ship.
     * It has a maximum size, since we can only store so much data.
     */
    val trace: MutableList<TracePoint>
) {
    data class TracePoint(
        val messageTime: Instant,
        val location: Location,
        val speedOverGround: Float
    )
}
