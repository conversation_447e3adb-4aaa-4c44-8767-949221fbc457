package nl.teqplay.aisengine.stopmonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.stopmonitor.datasource.StopStateRedisRepository
import nl.teqplay.aisengine.stopmonitor.datasource.StopTraceStateRedisRepository
import nl.teqplay.aisengine.stopmonitor.model.StopState
import nl.teqplay.aisengine.stopmonitor.model.StopTraceState
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

private val LOG = KotlinLogging.logger {}

@Component
class StopStateService(
    private val stopStateRedisRepository: StopStateRedisRepository,
    private val stopTraceStateRedisRepository: StopTraceStateRedisRepository
) {

    @Scheduled(fixedRate = 3_600_000)
    fun logBucketCounts() {
        val stopStateCount = stopStateRedisRepository.count()
        val stopTraceStateCount = stopTraceStateRedisRepository.count()
        LOG.info { "StopStateRepository contains $stopStateCount values" }
        LOG.info { "StopTraceStateRepository contains $stopTraceStateCount values" }
    }

    /**
     * Gets the [StopState] for this [mmsi] if it exists.
     */
    fun getStopState(mmsi: Int): StopState? {
        return stopStateRedisRepository.findById(mmsi).orElse(null)
    }

    /**
     * Gets the [StopTraceState] for this [mmsi] if it exists.
     */
    fun getStopTraceState(mmsi: Int): StopTraceState? {
        return stopTraceStateRedisRepository.findById(mmsi).orElse(null)
    }

    /**
     * Save the [state] in the [stopStateRedisRepository].
     */
    fun save(state: StopState) {
        stopStateRedisRepository.save(state)
    }

    /**
     * Save the [state] in the [stopTraceStateRedisRepository].
     */
    fun saveTrace(state: StopTraceState) {
        stopTraceStateRedisRepository.save(state)
    }

    /**
     * Clear the state for this [mmsi] from the [stopStateRedisRepository] and [stopTraceStateRedisRepository].
     */
    fun clear(mmsi: Int) {
        stopStateRedisRepository.deleteById(mmsi)
        clearTrace(mmsi)
    }

    /**
     * Clear the trace state for this from the [stopTraceStateRedisRepository].
     */
    fun clearTrace(mmsi: Int) {
        stopTraceStateRedisRepository.deleteById(mmsi)
    }
}
