package nl.teqplay.aisengine.stopmonitor.service

import io.micrometer.core.instrument.MeterRegistry
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsAisStreamOptions
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.stereotype.Component
import java.util.concurrent.atomic.AtomicLong

@Component
class EventHandlerService(
    private val eventStreamService: EventStreamService,
    private val stopEventProcessor: StopEventProcessor,
    private val diffConsumerStream: NatsConsumerStream<AisDiffMessage>,
    meterRegistry: MeterRegistry
) {

    private val registry = MetricRegistry.of<EventHandlerService>(meterRegistry, listOf("event-type"))
    private val aisDiffMessageInputMetric =
        registry.createGaugeWithClass(Metric.MESSAGE_COUNT_INPUT, AtomicLong(), AisDiffMessage::class)

    @PostConstruct
    fun receive() {
        eventStreamService.consume(
            stream = diffConsumerStream,
            revents = ReventsAisStreamOptions(
                includeServiceVessels = false
            )
        ) { aisDiffMessage, message ->
            aisDiffMessageInputMetric.incrementAndGet()
            val stopEvent = stopEventProcessor.process(aisDiffMessage)
            if (stopEvent != null) {
                eventStreamService.publish(stopEvent)
            }
            message.ack()
        }
    }
}
