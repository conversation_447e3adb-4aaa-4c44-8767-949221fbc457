package nl.teqplay.aisengine.stopmonitor.service

import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.event.interfaces.StopEvent
import nl.teqplay.aisengine.event.model.StopEndEvent
import nl.teqplay.aisengine.event.model.StopStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.stopmonitor.model.StopState
import nl.teqplay.aisengine.stopmonitor.model.StopTraceState
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.util.SpeedUnit
import nl.teqplay.skeleton.util.convertKnots
import nl.teqplay.skeleton.util.haversineDistance
import nl.teqplay.skeleton.util.speedBetweenLocations
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.util.UUID

@Component
class StopEventProcessor(
    private val stopStateService: StopStateService
) {

    companion object {
        /**
         * The amount of knots needed to start labeling this as a stop.
         */
        private const val STOP_START_SPEED_THRESHOLD = 0.5

        /**
         * The maximum allowed distance to start a new stop.
         */
        private const val STOP_START_DISTANCE_THRESHOLD = 25

        /**
         * The minimal amount of time a stop needs to take before we even consider ending it.
         */
        private val STOP_MIN_DURATION = Duration.ofMinutes(1)

        /**
         * The maximum amount of knots allowed to still determine the stop as ongoing.
         * The stop can potentially be ended when the vessel moves faster than this speed.
         */
        private const val STOP_END_SPEED_THRESHOLD = 1.0

        /**
         * The maximum amount of distance allowed to still determine the stop as ongoing.
         * This is set rather low to not trigger stop ends on jitters.
         */
        private const val STOP_END_DISTANCE_THRESHOLD = 1000

        /**
         * Total amount of points we need to "move" until we can create a stop end.
         */
        private const val STOP_END_TOTAL_POINT_THRESHOLD = 2

        private const val NORMAL_MOVING_SPEED_MIN = 4.0
        private const val NORMAL_MOVING_SPEED_MAX = 20.0

        /**
         * Range on how fast in knots a ship is seen as normal moving speed
         */
        private val NORMAL_MOVING_SPEED = NORMAL_MOVING_SPEED_MIN..NORMAL_MOVING_SPEED_MAX

        /**
         * Maximum size of [StopTraceState.trace] before being pruned.
         */
        private const val STOP_TRACE_MAX_SIZE = 25

        /**
         * Duration the stop's trace will be stored, after which [determineActualStopLocation] will be called.
         */
        private val STOP_TRACE_DURATION = Duration.ofHours(2)

        /**
         * Duration used to filter out trace points that happened in the first 30 minutes
         */
        private val STOP_TRACE_IGNORED_START_DURATION = Duration.ofMinutes(30)
    }

    /**
     * Process a [diff] message, optionally resulting in a [StopEvent].
     */
    fun process(diff: AisDiffMessage): StopEvent? {
        val speedOverGround = diff.speedOverGround.latest() ?: return null
        val stopState = stopStateService.getStopState(diff.mmsi)

        return if (stopState == null) {
            processMovingShip(diff, speedOverGround)
        } else {
            val stopTraceState = stopStateService.getStopTraceState(diff.mmsi)
            processStoppedShip(diff, speedOverGround, stopState, stopTraceState)
        }
    }

    private fun processMovingShip(
        diff: AisDiffMessage,
        speedOverGround: Float
    ): StopEvent? {
        // Ship is moving fast enough, return early.
        if (speedOverGround >= STOP_START_SPEED_THRESHOLD) {
            return null
        }

        val distanceBetweenLastLocation = diff.location.changed?.new?.let { newLocation ->
            haversineDistance(diff.location.old, newLocation)
        }

        // We either couldn't check the distance between the old and current location
        // or the amount of distance traveled was way too high to start a new stop.
        if (distanceBetweenLastLocation == null || distanceBetweenLastLocation >= STOP_START_DISTANCE_THRESHOLD) {
            val secondsBetweenLastLocation = Duration.between(diff.oldMessageTime, diff.messageTime).seconds
            val meterPerSecond = distanceBetweenLastLocation?.let { it / secondsBetweenLastLocation } ?: return null

            if (meterPerSecond > convertKnots(STOP_START_SPEED_THRESHOLD, SpeedUnit.METER_PER_SECOND)) {
                return null
            }
        }

        val stopStartEvent = StopStartEvent(
            _id = UUID.randomUUID().toString(),
            ship = AisShipIdentifier(diff.mmsi, diff.imo.latest()),
            location = diff.location.latest(),
            actualTime = diff.messageTime
        )

        // Add to trace.
        val trace = mutableListOf<StopTraceState.TracePoint>()
        recordPointInTrace(trace, diff, speedOverGround)

        // Persist state.
        val state = StopState(
            mmsi = diff.mmsi,
            startEventId = stopStartEvent._id,
            stoppedTime = diff.messageTime,
            stopLocation = null
        )
        stopStateService.save(state)

        val traceState = StopTraceState(
            mmsi = diff.mmsi,
            trace = trace
        )
        stopStateService.saveTrace(traceState)

        return stopStartEvent
    }

    private fun processStoppedShip(
        diff: AisDiffMessage,
        speedOverGround: Float,
        stopState: StopState,
        stopTraceState: StopTraceState?,
    ): StopEvent? {
        if (stopTraceState != null) {
            recordPointInTrace(stopTraceState.trace, diff, speedOverGround)
        }

        val currentStopDuration = Duration.between(stopState.stoppedTime, diff.messageTime)
        if (currentStopDuration < STOP_MIN_DURATION) {
            // Stop is so short, there is no possibility to end it
            return null
        }

        if (speedOverGround <= STOP_END_SPEED_THRESHOLD) {
            // Reset the state if we were counting points before
            val resetStopState = if (stopState.totalMovingTracePoints > 0) {
                stopState.copy(
                    totalMovingTracePoints = 0,
                    potentialStopEndLocation = null,
                    potentialStopEndTime = null
                )
            } else {
                stopState
            }

            // Reset the stop state
            stopStateService.save(resetStopState)

            if (stopTraceState != null) {
                // If the duration of the stops exceeds the STOP_TRACE_DURATION, the trace is cleared,
                // and the stop location is determined.
                // Keeping track for longer is unnecessary as the ship is stationary.
                if (Duration.between(resetStopState.stoppedTime, diff.messageTime) >= STOP_TRACE_DURATION) {
                    val traceEdge = resetStopState.stoppedTime.plus(STOP_TRACE_IGNORED_START_DURATION)
                    val stopLocation = determineActualStopLocationWithoutStart(stopTraceState.trace, traceEdge)
                    stopStateService.save(resetStopState.copy(stopLocation = stopLocation))
                    stopStateService.clearTrace(diff.mmsi)
                } else {
                    stopStateService.saveTrace(stopTraceState)
                }
            }
            return null
        }

        val stopLocation = stopState.stopLocation
            ?: stopTraceState?.let { determineActualStopLocation(it.trace) }
            ?: diff.location.latest()

        val distanceBetweenLastLocation = diff.location.changed?.new?.let { newLocation ->
            // Get the distance between the new location and currently known stop location to avoid comparing weird jitters
            haversineDistance(stopLocation, newLocation)
        }

        // Make sure this AIS point isn't jitter before trying to end the stop
        if (distanceBetweenLastLocation != null && distanceBetweenLastLocation > STOP_END_DISTANCE_THRESHOLD) {
            val timeBetweenPreviousLocation = diff.oldMessageTime?.let { oldMessageTime ->
                Duration.between(oldMessageTime, diff.messageTime)
            } ?: return null
            val speedBetweenPrevious = diff.location.changed?.new?.let { newLocation ->
                speedBetweenLocations(diff.location.old, newLocation, timeBetweenPreviousLocation, SpeedUnit.KNOTS)
            }

            // Check if we had an abnormal speed to be sure that this is really jitter
            if (speedBetweenPrevious == null || speedBetweenPrevious !in NORMAL_MOVING_SPEED) {
                // Don't save the trace, just skip this point
                return null
            }
        }

        if (stopState.totalMovingTracePoints == 0) {
            // This is our first point that can potentially be our stop end
            val newState = stopState.copy(
                totalMovingTracePoints = 1,
                potentialStopEndLocation = diff.location.latest(),
                potentialStopEndTime = diff.messageTime
            )
            stopStateService.save(newState)
            // We can't create an event yet because we first need some more AIS point that are indeed moving again.
            return null
        }

        if (stopState.totalMovingTracePoints < STOP_END_TOTAL_POINT_THRESHOLD) {
            // It is still too early to create a stop end event, keep counting
            val newState = stopState.copy(
                totalMovingTracePoints = stopState.totalMovingTracePoints + 1,
            )
            stopStateService.save(newState)
            return null
        }

        // We are above our total point threshold, meaning we can finally create our end event
        val stopEndEvent = StopEndEvent(
            _id = UUID.randomUUID().toString(),
            startEventId = stopState.startEventId,
            ship = AisShipIdentifier(diff.mmsi, diff.imo.latest()),
            location = stopState.potentialStopEndLocation!!,
            stopLocation = stopLocation,
            actualTime = stopState.potentialStopEndTime!!
        )
        stopStateService.clear(diff.mmsi)
        return stopEndEvent
    }

    private fun recordPointInTrace(
        trace: MutableList<StopTraceState.TracePoint>,
        diff: AisDiffMessage,
        speedOverGround: Float
    ) {
        trace.add(StopTraceState.TracePoint(diff.messageTime, diff.location.latest(), speedOverGround))

        if (trace.size > STOP_TRACE_MAX_SIZE) {
            pruneTrace(trace)
        }
    }

    private fun pruneTrace(
        trace: MutableList<StopTraceState.TracePoint>
    ) {
        // Get points closest together.
        val (currentTraceItem, nextTraceItem) = trace.zipWithNext()
            .minBy { Duration.between(it.first.messageTime, it.second.messageTime) }

        // Remove the closest point with the highest speed.
        // Keeping slower speeds are more useful for calculating the stopped location.
        val traceItem = when {
            currentTraceItem.speedOverGround >= nextTraceItem.speedOverGround -> currentTraceItem
            else -> nextTraceItem
        }
        trace.remove(traceItem)
    }

    /**
     * Helper function to determine the actual location of a stop based on the provided [trace].
     * Use the [trace] by removing any outliers for latitude and longitude separately.
     *
     * @return The location where the vessel most likely stopped.
     */
    private fun determineActualStopLocation(trace: List<StopTraceState.TracePoint>): Location {
        val latitudes = trace.map { it.location.lat }.sorted()
        val longitudes = trace.map { it.location.lon }.sorted()

        // Only take a selected amount of items to filter out outliers
        val (fromIndex, toIndex) = trace.getIndexesToFilterOutliers()

        val latitude = latitudes.subList(fromIndex, toIndex + 1).average()
        val longitude = longitudes.subList(fromIndex, toIndex + 1).average()

        return Location(
            lat = latitude,
            lon = longitude
        )
    }

    /**
     * Get the actual location without taking into account the locations that happened when initially starting the stop.
     * @param trace all traces.
     * @param traceEdge all traces happening before this time are not taking into account when determining the actual stop location.
     * @see determineActualStopLocation
     */
    private fun determineActualStopLocationWithoutStart(trace: List<StopTraceState.TracePoint>, traceEdge: Instant): Location {
        val filteredTraces = trace.filter { it.messageTime >= traceEdge }
        return determineActualStopLocation(filteredTraces)
    }

    private fun List<*>.getIndexesToFilterOutliers(): Pair<Int, Int> {
        val itemsToSkipEachSide = this.size * 0.90
        val fromIndex = (this.size - itemsToSkipEachSide).toInt().coerceAtLeast(0)
        val toIndex = this.lastIndex - fromIndex
        return fromIndex to toIndex
    }
}
