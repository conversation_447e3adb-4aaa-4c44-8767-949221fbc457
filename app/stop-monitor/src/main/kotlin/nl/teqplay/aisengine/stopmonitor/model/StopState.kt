package nl.teqplay.aisengine.stopmonitor.model

import nl.teqplay.aisengine.event.model.StopStartEvent
import nl.teqplay.skeleton.model.Location
import org.springframework.data.annotation.Id
import org.springframework.data.redis.core.RedisHash
import java.time.Instant

/**
 * State for a stop.
 */
@RedisHash("stop-state")
data class StopState(
    /**
     * [mmsi] of the ship.
     */
    @Id val mmsi: Int,

    /**
     * Identifier of the [StopStartEvent].
     */
    val startEventId: String,

    /**
     * Time when the ship stopped.
     */
    val stoppedTime: Instant = Instant.now(),

    /**
     * Actual stop location, determined by the trace of the ship.
     */
    val stopLocation: Location? = null,

    val totalMovingTracePoints: Int = 0,

    val potentialStopEndTime: Instant? = null,
    val potentialStopEndLocation: Location? = null
)
