buildscript {
    project.extra.set("baseVersion", "2.2.2")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":api:nats-stream-ais-consume-diff"))
    implementation(project(":api:nats-stream-event"))
    implementation("nl.teqplay.skeleton:util-location2:$skeletonVersion")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("io.lettuce:lettuce-core")
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
