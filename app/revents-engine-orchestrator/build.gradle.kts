buildscript {
    project.extra.set("baseVersion", "2.1.2")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation("nl.teqplay.skeleton:metrics:$skeletonVersion")
    implementation(project(":lib:revents-engine-common")) {
        // The CSI client bean was trying to start, crashing the application due to missing properties
        exclude(group = "nl.teqplay.skeleton", module = "csi-client")
    }
    implementation(project(":api:revents-profile"))

    implementation("nl.teqplay.skeleton:datasource2:$skeletonVersion")
    implementation("nl.teqplay.skeleton:actuator:$skeletonVersion")
    implementation("nl.teqplay.skeleton:nats:$skeletonVersion")
    implementation("nl.teqplay.skeleton:vesselvoyage-client:$skeletonVersion")

    implementation("com.amazonaws:aws-java-sdk:$awsVersion")
    implementation("io.fabric8:kubernetes-client:$fabric8KubernetesClientVersion")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")

    testImplementation("org.awaitility:awaitility:$awaitilityVersion")
    testImplementation("org.awaitility:awaitility-kotlin:$awaitilityVersion")
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
