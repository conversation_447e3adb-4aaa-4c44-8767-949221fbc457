package nl.teqplay.aisengine.reventsengine.properties

import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "kubernetes")
data class KubernetesProperties(
    /**
     * Namespace where jobs are running.
     */
    val namespace: String,

    /**
     * Kubernetes version 1.29+ supports sidercar containers, which is preferred
     * to have pre-requisite components to be spun up first.
     */
    val kubernetesVersionSupportsSidecarContainers: Boolean,

    /**
     * Maximum amount of jobs that can run.
     */
    val maxJobsCount: Int,

    /**
     * Where to pull the container images from.
     */
    val containerImages: ContainerImages,

    /**
     * Specified CPU/memory resources for all components.
     */
    val resources: Resources,

    /**
     * Whether to use the data environment configmap in the production cluster.
     */
    val useDataEnvironment: Boolean = false
) {
    data class ContainerImages(
        val nats: String,
        val mongo: String,
        val aisEngineBase: String,
        val vesselvoyage: String,
    )

    data class Resources(
        val reventsEngine: ResourceSpecification,
        val nats: ResourceSpecification,
        val apps: Map<ScenarioEvent, ResourceSpecification>,
        val vesselvoyage: ResourceSpecification,
    )

    data class ResourceSpecification(
        val requests: Requests,
        val limits: Limits = Limits()
    ) {
        data class Requests(val cpu: String, val memory: String)
        data class Limits(val memory: String? = null)
    }
}
