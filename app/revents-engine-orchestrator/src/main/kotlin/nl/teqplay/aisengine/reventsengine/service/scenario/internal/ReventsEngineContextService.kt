package nl.teqplay.aisengine.reventsengine.service.scenario.internal

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.reventsengine.common.datasource.VesselVoyageEntriesV2DataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.properties.KubernetesProperties
import nl.teqplay.aisengine.reventsengine.service.event.ActualEventsService
import nl.teqplay.skeleton.nats.NatsClientBuilder
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.stereotype.Component

@Component
class ReventsEngineContextService(
    private val kubernetesProperties: KubernetesProperties,
    private val kubernetesJobService: KubernetesJobService,
    private val restTemplateBuilder: RestTemplateBuilder,
    private val actualEventsService: ActualEventsService,
    private val vesselVoyageEntriesV2DataSource: VesselVoyageEntriesV2DataSource,
    private val natsClientBuilder: NatsClientBuilder,
    private val objectMapper: ObjectMapper
) {

    private val contexts = List(kubernetesProperties.maxJobsCount) { index ->
        ReventsEngineContext(
            kubernetesProperties = kubernetesProperties,
            restTemplateBuilder = restTemplateBuilder,
            actualEventsService = actualEventsService,
            vesselVoyageEntriesV2DataSource = vesselVoyageEntriesV2DataSource,
            natsClientBuilder = natsClientBuilder,
            objectMapper = objectMapper,
            index = index,
            serviceApiUri = kubernetesJobService.jobServiceApiUri(index),
            serviceNatsUri = kubernetesJobService.jobServiceNatsUri(index),
        )
    }

    fun assignToContext(scenario: ScenarioState): Int? {
        val context = contexts.find { it.scenario == null } ?: return null
        context.scenario = scenario.id
        return context.index
    }

    fun assignToContext(scenario: ScenarioState, contextIndex: Int): Int {
        val context = contexts.getOrNull(contextIndex)
            ?: throw Exception("Context $contextIndex not found")
        context.scenario = scenario.id
        return context.index
    }

    fun unassignFromContext(scenario: ScenarioState) {
        contexts
            .filter { it.scenario == scenario.id || scenario.status?.contextIndex == it.index }
            .forEach {
                it.scenario = null
            }
    }

    fun getContext(scenario: ScenarioState): ReventsEngineContext? {
        val index = scenario.status?.contextIndex ?: return null
        return contexts.getOrNull(index)
            ?: throw Exception("Context $index not found")
    }
}
