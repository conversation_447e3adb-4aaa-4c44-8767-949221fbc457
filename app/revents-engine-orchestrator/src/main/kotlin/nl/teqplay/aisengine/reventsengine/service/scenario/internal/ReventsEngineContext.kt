package nl.teqplay.aisengine.reventsengine.service.scenario.internal

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.github.oshai.kotlinlogging.KotlinLogging
import io.nats.client.FetchConsumeOptions
import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.StateEvent
import nl.teqplay.aisengine.revents.NATS_POISON_PILL_HEADER
import nl.teqplay.aisengine.reventsengine.common.datasource.VesselVoyageEntriesV2DataSource
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunProgress
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunStart
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing.VESSEL_VOYAGE_V2
import nl.teqplay.aisengine.reventsengine.properties.KubernetesProperties
import nl.teqplay.aisengine.reventsengine.service.event.ActualEventsService
import nl.teqplay.skeleton.common.config.NatsProperties
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsConsumerStream
import nl.teqplay.vesselvoyage.model.v2.NewChange
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpStatus
import org.springframework.web.client.postForEntity
import org.springframework.web.client.postForObject
import java.time.Duration
import java.util.concurrent.ConcurrentSkipListSet
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference
import kotlin.concurrent.thread
import kotlin.system.measureTimeMillis

private val LOG = KotlinLogging.logger { }

data class ReventsEngineContext(
    private val kubernetesProperties: KubernetesProperties,
    private val restTemplateBuilder: RestTemplateBuilder,
    private val actualEventsService: ActualEventsService,
    private val vesselVoyageEntriesV2DataSource: VesselVoyageEntriesV2DataSource,
    private val natsClientBuilder: NatsClientBuilder,
    private val objectMapper: ObjectMapper,

    val index: Int,
    val serviceApiUri: String,
    val serviceNatsUri: String,
    var scenario: String? = null
) {

    private val restTemplate = restTemplateBuilder.rootUri(serviceApiUri).build()

    private val eventStream = AtomicReference<NatsConsumerStream<Event?>>()
    private val consumeEventThread = AtomicReference<Thread>()

    private val vesselVoyageV2ChangeStream = AtomicReference<NatsConsumerStream<NewChange<*>?>>()
    private val consumeVesselVoyageV2ChangeThread = AtomicReference<Thread>()

    /**
     * Contains the events that are expected to be received by the scenario.
     */
    private val expectedEvents = ConcurrentSkipListSet<ScenarioEvent>()
    private val receivedVesselVoyageV2PoisonPill = AtomicBoolean(false)

    @Volatile
    private var running = false

    /**
     * Start the scenario by communicating the [interests] to the revents-engine.
     */
    fun startScenario(interests: InterestsRunStart): Boolean {
        return runCatching {
            val response = restTemplate.postForEntity<Unit>("/revents/run", interests)
            response.statusCode == HttpStatus.OK
        }.onFailure {
            LOG.warn(it) { "Start scenario not successful" }
        }.getOrNull() ?: false
    }

    /**
     * Request the progress of the scenario from the revents-engine.
     */
    fun requestProgress(): InterestsRunProgress? =
        runCatching {
            val done = hasReceivedAllExpectedEvents()
            restTemplate.postForObject<InterestsRunProgress>("/revents/progress?done=$done")
        }.onFailure {
            LOG.warn(it) { "Progress request failed" }
        }.getOrNull()

    /**
     * Start consuming events for the [scenario].
     */
    fun startConsuming(scenario: ScenarioState): Boolean {
        runCatching {
            if (running) {
                // already available, assuming same scenario
                return true
            }

            running = true

            val config = NatsProperties(
                enabled = true,
                url = serviceNatsUri,
                username = "revents-engine-orchestrator"
            )
            setupEventStream(scenario, config)
            setupVesselVoyageV2ChangeStream(scenario, config)
            return true
        }.onFailure {
            LOG.error(it) { "Couldn't start consuming from stream" }
            throw ScenarioCrashedException(ScenarioCrashReason.REVENTS_API_STREAMING_FAILED)
        }
        return false
    }

    private fun setupEventStream(
        scenario: ScenarioState,
        config: NatsProperties
    ) {
        expectedEvents.clear()

        val settings = scenario.settings ?: Scenario.Settings()
        if (!settings.persistEvents) {
            return
        }

        // set expected events to the events expected by the scenario
        expectedEvents.addAll(scenario.events)

        val stream = requireNotNull(
            natsClientBuilder.consumerStream<Event?>(
                config = config,
                stream = "event-stream",
                deserializer = {
                    if (it.isEmpty()) null
                    else objectMapper.readValue(it)
                }
            )
        )

        val consumer = stream.consumeFetch(
            subjects = scenario.events.flatMap { it.eventSubjects }.distinct() +
                ScenarioEvent.REVENTS_POISON_PILL_SUBJECT
        )
        val fetchConsumeOptions = FetchConsumeOptions.builder()
            .maxMessages(500)
            .expiresIn(Duration.ofSeconds(10).toMillis())
            .build()

        val newThread = thread {
            // keeps running, waiting to be interrupted
            while (running) {
                try {
                    val messages = consumer.fetch(fetchConsumeOptions)
                    val events = messages.mapNotNull { it.data }
                        .filterIsInstance<ActualEvent>()
                        .filter { it !is StateEvent }
                    val timeTakenInMs = measureTimeMillis {
                        // save the events
                        actualEventsService.saveMany(scenario.id, events)

                        // check for poison pills from the applications
                        val poisonPillApplicationNames = messages.mapNotNull {
                            it.message.headers?.getFirst(NATS_POISON_PILL_HEADER)
                        }
                        if (poisonPillApplicationNames.isNotEmpty()) {
                            // remove an expected event if the application signalled it finished
                            expectedEvents.removeIf { expectedEvent ->
                                poisonPillApplicationNames.any { it.contains(expectedEvent.app) }
                            }

                            LOG.info {
                                "[${scenario.id}] Received poison pill(s) for ${poisonPillApplicationNames.joinToString()}, " +
                                    "expecting ${expectedEvents.size} remaining poison pill(s)"
                            }
                        }
                    }

                    if (events.isNotEmpty()) {
                        LOG.info { "[${scenario.id}] Saved ${events.size} events in ${timeTakenInMs}ms" }
                    }

                    messages.forEach { (_, message) ->
                        message.ack()
                    }
                } catch (e: InterruptedException) {
                    // break out of the loop
                    break
                } catch (e: Exception) {
                    LOG.warn(e) { "Something went wrong while consuming events." }
                }
            }
        }

        // set new thread, and optionally interrupt the old thread if it's still around
        // this should never happen, but safeguard against it by terminating the old thread
        consumeEventThread.getAndSet(newThread)?.interrupt()
        eventStream.set(stream)
    }

    private fun setupVesselVoyageV2ChangeStream(
        scenario: ScenarioState,
        config: NatsProperties
    ) {
        if (VESSEL_VOYAGE_V2 !in scenario.postProcessing) {
            receivedVesselVoyageV2PoisonPill.set(true)
            return
        }

        // Expect to receive poison pill later.
        receivedVesselVoyageV2PoisonPill.set(false)

        val stream = requireNotNull(
            natsClientBuilder.consumerStream<NewChange<*>?>(
                config = config,
                stream = "vesselvoyage:change",
                deserializer = {
                    if (it.isEmpty()) null
                    else objectMapper.readValue(it)
                }
            )
        )

        val consumer = stream.consumeFetch()
        val fetchConsumeOptions = FetchConsumeOptions.builder()
            .maxMessages(500)
            .expiresIn(Duration.ofSeconds(10).toMillis())
            .build()

        val newThread = thread {
            // keeps running, waiting to be interrupted
            while (running) {
                try {
                    val messages = consumer.fetch(fetchConsumeOptions)
                    val changes = messages.mapNotNull { it.data }
                    val timeTakenInMs = measureTimeMillis {
                        // save the changes
                        vesselVoyageEntriesV2DataSource.saveMany(scenario.getStoreScenarioId(), changes)

                        // check for poison pill from VesselVoyage
                        val receivedPoisonPill =
                            messages.any { it.message.headers?.containsKey(NATS_POISON_PILL_HEADER) == true }
                        if (receivedPoisonPill) {
                            receivedVesselVoyageV2PoisonPill.set(true)
                            LOG.info { "[${scenario.id}] Received poison pill for VesselVoyage" }
                        }
                    }

                    if (changes.isNotEmpty()) {
                        LOG.info { "[${scenario.id}] Saved ${changes.size} VesselVoyage changes in ${timeTakenInMs}ms" }
                    }

                    messages.forEach { (_, message) ->
                        message.ack()
                    }
                } catch (e: InterruptedException) {
                    // break out of the loop
                    break
                } catch (e: Exception) {
                    LOG.warn(e) { "Something went wrong while consuming VesselVoyage changes." }
                }
            }
        }

        // set new thread, and optionally interrupt the old thread if it's still around
        // this should never happen, but safeguard against it by terminating the old thread
        consumeVesselVoyageV2ChangeThread.getAndSet(newThread)?.interrupt()
        vesselVoyageV2ChangeStream.set(stream)
    }

    /**
     * Returns whether all [expectedEvents] have been received, the list would then be empty.
     * Also checks whether VesselVoyage V2 poison pill is received. Or already 'true' if disabled.
     */
    private fun hasReceivedAllExpectedEvents(): Boolean {
        return expectedEvents.isEmpty() && receivedVesselVoyageV2PoisonPill.get()
    }

    /**
     * Stop consuming events, waiting for the subscription to drain.
     */
    fun stopConsuming(): Boolean {
        // run, but we can continue without crashing
        runCatching {
            expectedEvents.clear()
            receivedVesselVoyageV2PoisonPill.set(false)
            running = false

            consumeEventThread.getAndSet(null)?.interrupt()
            eventStream.getAndSet(null)?.natsClient?.drain()

            consumeVesselVoyageV2ChangeThread.getAndSet(null)?.interrupt()
            vesselVoyageV2ChangeStream.getAndSet(null)?.natsClient?.drain()
        }.onFailure {
            LOG.warn(it) { "Couldn't drain stream connection" }
        }
        return true
    }
}
