package nl.teqplay.aisengine.reventsengine.service.scenario

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCancelledException
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CANCELLED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CRASHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FINISHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FORKED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_ASSIGN_CONTEXT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_JOB_CREATE
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_JOB_WAIT_READY
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_QUEUED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_RUN_SCENARIO
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_STREAMING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_INIT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_TEARDOWN
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_VESSEL_VOYAGE
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_VESSEL_VOYAGE_V2
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_VESSEL_VOYAGE_WAIT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PRUNED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_INITIALIZE_INTERESTS
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_SCENARIO_NOTIFY_EXTERNAL
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_JOB
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_STREAMING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_UNASSIGN_CONTEXT
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosMetadataService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosPhaseService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosPhaseService.RunPhaseResult
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunStart
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunStart.OtherVessels
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunStart.Settings
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.REVENTS_API_INCORRECT_PHASE
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.REVENTS_API_NO_CONTEXT_ASSIGNED
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.REVENTS_ENGINE_CRASHED
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.REVENTS_ENGINE_INCORRECT_SCENARIO
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent.ENCOUNTER
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing.VESSEL_VOYAGE
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing.VESSEL_VOYAGE_V2
import nl.teqplay.aisengine.reventsengine.service.scenario.internal.KubernetesJobService
import nl.teqplay.aisengine.reventsengine.service.scenario.internal.PostProcessingService
import nl.teqplay.aisengine.reventsengine.service.scenario.internal.ReventsEngineContext
import nl.teqplay.aisengine.reventsengine.service.scenario.internal.ReventsEngineContextService
import nl.teqplay.aisengine.util.mergeTimeWindows
import org.springframework.stereotype.Component
import java.time.Instant

@Component
class ScenariosPhaseServiceImpl(
    private val kubernetesJobService: KubernetesJobService,

    private val scenariosInterestsService: ScenariosInterestsService,
    private val scenariosMetadataService: ScenariosMetadataService,
    private val reventsEngineContextService: ReventsEngineContextService,
    private val postProcessingService: PostProcessingService,
) : ScenariosPhaseService {
    private val LOG = KotlinLogging.logger {}

    private fun getContext(scenario: ScenarioState): ReventsEngineContext {
        return reventsEngineContextService.getContext(scenario)
            ?: throw ScenarioCrashedException(REVENTS_API_NO_CONTEXT_ASSIGNED)
    }

    @Throws(ScenarioCrashedException::class, ScenarioCancelledException::class)
    override fun runPhase(scenario: ScenarioState): RunPhaseResult = with(scenario) {
        when (phase) {
            /**
             * Nothing to do, just move to next phase.
             */
            QUEUED,
            FINISHED,
            CRASHED,
            FORKED,
            PRUNED -> status to true

            QUEUED_END,
            QUEUED_INITIALIZE_INTERESTS,
            QUEUED_SCENARIO_NOTIFY_EXTERNAL -> throw ScenarioCrashedException(REVENTS_API_INCORRECT_PHASE)

            CANCELLED -> throw ScenarioCancelledException()

            /**
             * Update timestamps.
             */
            INITIALIZING_QUEUED -> {
                val now = Instant.now()

                // If scenario inherits events for post-processing, skip to there.
                if (inherit != null) {
                    RunPhaseResult(
                        newStatus = ScenarioState.InternalStatus(
                            start = now,
                            initialized = now,
                            progressed = now,
                        ),
                        moveToNextPhase = true,
                        skipToPhase = POST_PROCESSING_INIT
                    )
                } else {
                    ScenarioState.InternalStatus(start = now) to true
                }
            }

            INITIALIZING_END -> status?.copy(initialized = Instant.now()) to true
            PROGRESSING_END -> status?.copy(progressed = Instant.now()) to true
            POST_PROCESSING_END -> status?.copy(postProcessed = Instant.now()) to true

            /**
             * Initialize/teardown context
             */
            INITIALIZING_ASSIGN_CONTEXT -> {
                val context = reventsEngineContextService.assignToContext(scenario)
                    ?: throw ScenarioCrashedException(REVENTS_API_NO_CONTEXT_ASSIGNED)
                status?.copy(contextIndex = context) to true
            }

            TEARDOWN_UNASSIGN_CONTEXT -> {
                reventsEngineContextService.unassignFromContext(scenario)
                status?.copy(contextIndex = null) to true
            }

            /**
             * Initialize/wait/teardown job
             */
            INITIALIZING_JOB_CREATE -> {
                val context = getContext(scenario)
                status to kubernetesJobService.initialize(context.index, events, postProcessing, scenario.id)
            }

            INITIALIZING_JOB_WAIT_READY -> {
                val context = getContext(scenario)
                status to kubernetesJobService.waitUntilReady(context.index)
            }

            TEARDOWN_JOB -> {
                val context = getContext(scenario)
                status to kubernetesJobService.teardown(context.index)
            }

            /**
             * Initialize/teardown the streaming of events
             */
            INITIALIZING_STREAMING,
            TEARDOWN_STREAMING -> {
                val context = getContext(scenario)
                val successful = when (phase) {
                    INITIALIZING_STREAMING -> context.startConsuming(this)
                    else -> context.stopConsuming()
                }
                status to successful
            }

            /**
             * Start the scenario itself, by communicating with the revents-engine.
             */
            INITIALIZING_RUN_SCENARIO -> {
                val source = getSourceScenarioId()
                val store = getStoreScenarioId()

                // only communicate interests unique to this scenario
                val interests = scenariosInterestsService
                    .getUniqueInterestsByScenario(source)
                    .groupBy { it.mmsi }
                    .map { (mmsi, mmsiInterests) ->
                        val windows = mergeTimeWindows(mmsiInterests.map { it.window })
                        windows.map { InterestsRunStart.RelevantShip(mmsi = mmsi, window = it) }
                    }
                    .flatten()
                val metadata = scenariosMetadataService.getMetadataByScenario(store)

                val otherVessels = if (ENCOUNTER in events) {
                    OtherVessels(
                        areas = metadata?.areas ?: emptyList()
                    )
                } else null

                val settings = scenario.settings ?: Scenario.Settings(scenario)
                val interestsRun = InterestsRunStart(
                    id = id,
                    events = scenario.events,
                    interests = interests,
                    otherVessels = otherVessels,
                    settings = Settings(
                        filterHistory = settings.filterHistory,
                    )
                )
                val context = getContext(scenario)
                val successful = context.startScenario(interestsRun)
                status to successful
            }

            /**
             * Keep watch on the revents-engine to see when it's finished.
             */
            PROGRESSING -> {
                val context = getContext(scenario)
                val progress = context.requestProgress()
                    ?: return@with status to false
                if (progress.id != id) {
                    LOG.error { "Crashing scenario id mismatch (expected $id, got ${progress.id})" }
                    throw ScenarioCrashedException(REVENTS_ENGINE_INCORRECT_SCENARIO)
                }

                if (progress.done && progress.crashed) {
                    // revents-engine indicated it finished but crashed, report it
                    LOG.error { "Crashing scenario done and crashed at the same time ($id)" }
                    throw ScenarioCrashedException(REVENTS_ENGINE_CRASHED)
                } else {
                    status?.copy(progress = progress) to progress.done
                }
            }

            /**
             * Skip post-processing step entirely if not enabled.
             */
            POST_PROCESSING_INIT -> {
                // skip if no post-processing
                if (postProcessing.isEmpty() || teardown) {
                    RunPhaseResult(status, true, POST_PROCESSING_TEARDOWN)
                } else {
                    status to true
                }
            }

            /**
             * post-processing: VesselVoyage
             */
            POST_PROCESSING_VESSEL_VOYAGE,
            POST_PROCESSING_VESSEL_VOYAGE_WAIT -> {
                // skip if not set
                if (VESSEL_VOYAGE !in postProcessing) {
                    return@with status to true
                }

                if (phase == POST_PROCESSING_VESSEL_VOYAGE_WAIT) {
                    return@with status to postProcessingService.isVesselVoyagePostProcessingFinished(id)
                }

                // start post-processing
                val source = getSourceScenarioId()
                val store = getStoreScenarioId()
                postProcessingService.startVesselVoyagePostProcessing(id, source, store)
                status to true
            }

            /**
             * post-processing: VesselVoyage V2
             */
            POST_PROCESSING_VESSEL_VOYAGE_V2,
            POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT -> {
                // skip if not set
                if (VESSEL_VOYAGE_V2 !in postProcessing) {
                    return@with status to true
                }

                if (phase == POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT) {
                    return@with status to postProcessingService.isVesselVoyagePostProcessingV2Finished(id)
                }

                // start post-processing
                val source = getSourceScenarioId()
                val store = getStoreScenarioId()
                postProcessingService.startVesselVoyagePostProcessingV2(id, source, store)
                status to true
            }

            POST_PROCESSING_TEARDOWN -> {
                // TODO: post-processing teardown
                status to true
            }
        }
    }
}
