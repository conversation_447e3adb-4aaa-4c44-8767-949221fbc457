package nl.teqplay.aisengine.reventsengine.service.scenario

import jakarta.annotation.PostConstruct
import org.springframework.stereotype.Component
import kotlin.concurrent.thread

@Component
class ScenariosSchedulerInitService(
    private val scenariosSchedulerService: ScenariosSchedulerService,
) {

    @PostConstruct
    fun init() {
        thread(name = "scheduler") {
            scenariosSchedulerService.loopOverScenarios()
        }
    }
}
