package nl.teqplay.aisengine.reventsengine.datasource

import com.mongodb.client.model.ReplaceOneModel
import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.reventsengine.common.datasource.ActualEventsBaseDataSource
import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.event.ActualEventWrapper
import nl.teqplay.skeleton.datasource.kmongo.eq
import org.springframework.stereotype.Component

@Component
class ActualEventsDataSource(
    mongoDatabase: MongoDatabase,
    scenariosDataSource: ScenariosDataSource,
) : ActualEventsBaseDataSource(mongoDatabase, scenariosDataSource) {

    fun saveMany(
        scenario: String,
        events: List<ActualEvent>,
    ) {
        if (events.isEmpty()) {
            return
        }
        val collection = getCollection(scenario)
        val writes = events.map { event ->
            val wrapper = ActualEventWrapper(event)
            ReplaceOneModel(
                /* filter = */ ActualEventWrapper::id eq wrapper.id,
                /* replacement = */ wrapper,
                /* options = */ ReplaceOptions().upsert(true)
            )
        }
        collection.bulkWrite(writes)
    }
}
