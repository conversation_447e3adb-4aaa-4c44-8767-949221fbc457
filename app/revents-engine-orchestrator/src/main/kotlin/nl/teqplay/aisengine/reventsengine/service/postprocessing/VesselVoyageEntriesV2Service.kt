package nl.teqplay.aisengine.reventsengine.service.postprocessing

import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageV2Service
import nl.teqplay.aisengine.reventsengine.model.TimeWindowPair
import org.springframework.stereotype.Component

@Component
class VesselVoyageEntriesV2Service(
    private val vesselVoyageStorageV2Service: VesselVoyageStorageV2Service,
) {

    data class InterestPair(
        val windowPair: TimeWindowPair,
        val partial: Boolean
    )

    fun save(
        scenario: String,
        imo: Int,
        interests: List<InterestPair>,
    ) {
        vesselVoyageStorageV2Service.save(
            scenario = scenario,
            interests = interests.map {
                ScenarioInterestVesselVoyage(
                    imo = imo,
                    window = it.windowPair.window,
                    windowNoMargin = it.windowPair.windowNoMargin,
                    scenario = scenario,
                    partial = it.partial
                )
            }
        )
    }
}
