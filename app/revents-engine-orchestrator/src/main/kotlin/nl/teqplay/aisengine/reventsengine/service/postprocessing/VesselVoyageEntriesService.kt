package nl.teqplay.aisengine.reventsengine.service.postprocessing

import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageService
import nl.teqplay.aisengine.reventsengine.common.util.retryHttp
import nl.teqplay.aisengine.reventsengine.model.TimeWindowPair
import nl.teqplay.skeleton.vesselvoyage.client.VesselVoyageRestTemplate
import nl.teqplay.vesselvoyage.model.Entry
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForObject

@Component
class VesselVoyageEntriesService(
    private val vesselVoyageStorageService: VesselVoyageStorageService,

    @VesselVoyageRestTemplate private val restTemplate: RestTemplate,
) {

    fun generateEntries(events: List<ActualEvent>): List<Entry> = retryHttp {
        restTemplate
            .postForObject<Array<Entry>>("/v1/events/processed/aisengine", events)
            .toList()
    }

    data class InterestPair(
        val windowPair: TimeWindowPair,
        val partial: Boolean
    )

    fun save(
        scenario: String,
        imo: Int,
        interests: List<InterestPair>,
        entries: List<Entry>,
    ) {
        vesselVoyageStorageService.save(
            scenario = scenario,
            interests = interests.map {
                ScenarioInterestVesselVoyage(
                    imo = imo,
                    window = it.windowPair.window,
                    windowNoMargin = it.windowPair.windowNoMargin,
                    scenario = scenario,
                    partial = it.partial
                )
            },
            entries = entries
        )
    }
}
