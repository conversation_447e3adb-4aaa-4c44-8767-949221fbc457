package nl.teqplay.aisengine.reventsengine.service.event

import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.reventsengine.common.service.event.ActualEventsBaseService
import nl.teqplay.aisengine.reventsengine.datasource.ActualEventsDataSource
import nl.teqplay.aisengine.reventsengine.service.scenario.ScenariosInterestsService
import org.springframework.stereotype.Component

@Component
class ActualEventsService(
    private val actualEventsDataSource: ActualEventsDataSource,
    scenariosInterestsService: ScenariosInterestsService,
) : ActualEventsBaseService(actualEventsDataSource, scenariosInterestsService) {

    /**
     * Save [events] generated as part of a [scenario].
     */
    fun saveMany(
        scenario: String,
        events: List<ActualEvent>
    ) {
        actualEventsDataSource.saveMany(scenario, events)
    }
}
