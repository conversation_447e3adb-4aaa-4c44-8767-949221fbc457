package nl.teqplay.aisengine.reventsengine.service.scenario

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.service.cleanup.ScenariosCleanupTaskService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService.SchedulerState.CONTINUE
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService.SchedulerState.CRASHED
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService.SchedulerState.FINISHED
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService.SchedulerState.RETRY
import nl.teqplay.aisengine.reventsengine.properties.KubernetesProperties
import nl.teqplay.aisengine.reventsengine.service.scenario.cleanup.ScenariosCleanupService
import nl.teqplay.aisengine.reventsengine.service.scenario.internal.ReventsEngineContextService
import org.springframework.stereotype.Component
import java.time.Duration
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

private val LOG = KotlinLogging.logger { }

@Component
class ScenariosSchedulerService(
    private val scenariosQueueService: ScenariosQueueService,
    private val scenariosCleanupService: ScenariosCleanupService,
    private val scenariosCleanupTaskService: ScenariosCleanupTaskService,
    private val scenariosBaseSchedulerService: ScenariosBaseSchedulerService,
    private val kubernetesProperties: KubernetesProperties,

    private val reventsEngineContextService: ReventsEngineContextService,
) {

    val running = AtomicBoolean(true)

    /**
     * Loop over scenarios, run their phases in a concurrent way and queue scenarios if there's room for them.
     */
    fun loopOverScenarios() {
        // set up where all scenarios are stored and a reference to the progressing scenarios
        val allScenarios = mutableMapOf<String, ScenarioState>()
        val progressingScenarios = mutableSetOf<String>()

        val startScenarios = scenariosQueueService.getCurrentScenarios()
        startScenarios.forEach { scenario ->
            allScenarios[scenario.id] = scenario
            progressingScenarios.add(scenario.id)

            // ensure we assign the context on startup
            val contextIndex = scenario.status?.contextIndex
            if (contextIndex != null) {
                reventsEngineContextService.assignToContext(scenario, contextIndex)
            }
        }

        // settings for scenarios that need to be retried
        val retryScenarios = mutableSetOf<String>()
        var retryStartTime = System.currentTimeMillis()
        val waitTime = Duration.ofSeconds(15)

        // run indefinitely, until we should stop running
        while (running.get()) {
            // reset the retry timer, if there are no scenarios to be retried
            if (retryScenarios.isEmpty()) {
                retryStartTime = System.currentTimeMillis()
            }

            // loop over an immutable copy of the scenarios, only running one phase per scenario and then continuing
            for (id in progressingScenarios.toList()) {
                // run phase for this scenario
                val scenario = requireNotNull(allScenarios[id])
                val (newScenario, state) = scenariosBaseSchedulerService.runPhase(scenario)
                allScenarios[id] = newScenario

                when (state) {
                    // scenario's phase ran successfully and can continue to the next phase
                    CONTINUE -> {}

                    // scenario failed, we need to retry later, but don't block the non-failed scenarios from continuing
                    RETRY -> {
                        progressingScenarios.remove(id)
                        retryScenarios.add(id)
                    }

                    // scenario finished, remove it
                    FINISHED -> {
                        allScenarios.remove(id)
                        progressingScenarios.remove(id)
                        LOG.info { "[$id] Ended scenario" }
                    }

                    // scenario crashed, ensure we introduce a teardown scenario
                    CRASHED -> {
                        allScenarios.remove(id)
                        progressingScenarios.remove(id)
                        LOG.info { "[$id] Crashed scenario" }

                        val teardownScenario = scenariosCleanupTaskService.createAndSaveTeardownScenario(newScenario)
                        allScenarios[teardownScenario.id] = teardownScenario
                        progressingScenarios.add(teardownScenario.id)
                    }
                }
            }

            // either we reset as we've waited the retry time, or we wait as we can't progress further for now
            val elapsedRetryWait = System.currentTimeMillis() - retryStartTime
            val retryWait = waitTime.toMillis() - elapsedRetryWait
            if (retryWait < 0 || progressingScenarios.isEmpty()) {
                // wait if we need to, and reset the scenarios
                TimeUnit.MILLISECONDS.sleep(retryWait)
                progressingScenarios.addAll(retryScenarios)
                retryScenarios.clear()

                // check if we can add queued scenarios
                var space = kubernetesProperties.maxJobsCount - progressingScenarios.size
                if (space > 0) {
                    val queuedScenarios = scenariosQueueService.getCurrentScenarios()
                        .filter { it.id !in allScenarios.keys }
                        .toMutableList()
                    while (space-- > 0 && queuedScenarios.isNotEmpty()) {
                        val queuedScenario = queuedScenarios.removeFirst()
                        allScenarios[queuedScenario.id] = queuedScenario
                        progressingScenarios.add(queuedScenario.id)
                        LOG.info { "[${queuedScenario.id}] Starting scenario" }
                    }
                }
            }
        }
    }
}
