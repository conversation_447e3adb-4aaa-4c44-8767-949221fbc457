package nl.teqplay.aisengine.reventsengine.service.postprocessing

import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.event.model.identifier.ShipIdentifier
import nl.teqplay.aisengine.reventsengine.model.TimeWindowPair
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing
import nl.teqplay.aisengine.reventsengine.service.event.ActualEventsService
import nl.teqplay.aisengine.reventsengine.service.scenario.ScenariosInterestsService
import nl.teqplay.aisengine.util.contains
import nl.teqplay.aisengine.util.mergeTimeWindows
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.stereotype.Component

@Component
class VesselVoyagePostProcessingService(
    private val scenariosInterestsService: ScenariosInterestsService,
    private val actualEventsService: ActualEventsService,
    private val vesselVoyageEntriesService: VesselVoyageEntriesService,
) {

    private val types = ScenarioPostProcessing.VESSEL_VOYAGE.eventTypeNames

    /**
     * Runs VesselVoyage post-processing for this [scenario], by:
     * - requesting the interests for this [scenario]
     * - grouping by IMO
     * - fetching the events
     * - generating VesselVoyage entries based on these events
     * - save these entries
     *
     * The [scenario] is used to request the interests and events.
     * The [storeScenario], is used to save the resulting entries.
     */
    fun run(
        scenario: String,
        storeScenario: String,
    ) {
        val groupedInterests = scenariosInterestsService.getUniqueInterestsByScenario(scenario)
            .filter { it.imo != null }
            .groupBy { it.imo }

        groupedInterests.entries.parallelStream().forEach { (imo, interests) ->
            if (imo == null) return@forEach
            val windows = mergeTimeWindows(interests.map { it.window })
            val maxWindow = TimeWindow(
                from = windows.minOf { it.from },
                to = windows.maxOf { it.to },
            )
            val events = actualEventsService
                .fetchEventsByInterests(interests, maxWindow, types)
                .correctImo(imo)
            val entries = vesselVoyageEntriesService.generateEntries(events)

            // Based on our interests, we reconstruct which windows without margin were used. Based on that we'll know
            // whether they were partial interests or not, and which window with margin is used.
            val windowsNoMargin = interests.map { it.windowNoMargin }
            val mergedWindowsNoMargin = mergeTimeWindows(windowsNoMargin)
            val interestPairs = mergedWindowsNoMargin.flatMap { windowNoMargin ->
                val innerInterests = interests.filter { interest -> windowNoMargin.contains(interest.windowNoMargin) }
                val mergedWindows = mergeTimeWindows(innerInterests.map { it.window })
                val partial = innerInterests.any { it.partial }
                mergedWindows.map { window ->
                    VesselVoyageEntriesService.InterestPair(
                        windowPair = TimeWindowPair(window, windowNoMargin),
                        partial = partial
                    )
                }
            }

            vesselVoyageEntriesService.save(storeScenario, imo, interestPairs, entries)
        }
    }

    /**
     * Corrects the [ShipIdentifier.imo] of each [ActualEvent], to be the specified [imo].
     */
    private fun List<ActualEvent>.correctImo(imo: Int): List<ActualEvent> = onEach { event ->
        ShipIdentifier::imo.set(event.ship, imo)
    }
}
