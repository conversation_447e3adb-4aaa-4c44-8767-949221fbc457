package nl.teqplay.aisengine.reventsengine.service.scenario.internal

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason
import nl.teqplay.aisengine.reventsengine.service.postprocessing.VesselVoyagePostProcessingService
import nl.teqplay.aisengine.reventsengine.service.postprocessing.VesselVoyagePostProcessingV2Service
import org.springframework.stereotype.Component
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentSkipListSet
import kotlin.concurrent.thread

@Component
class PostProcessingService(
    private val vesselVoyagePostProcessingService: VesselVoyagePostProcessingService,
    private val vesselVoyagePostProcessingV2Service: VesselVoyagePostProcessingV2Service
) {

    private val LOG = KotlinLogging.logger { }

    private val scenarioThreads = ConcurrentHashMap<String, Thread>()
    private val scenarioCrashed = ConcurrentSkipListSet<String>()

    /**
     * Returns whether the VesselVoyage post-processing of this [scenario] is finished.
     */
    @Throws(ScenarioCrashedException::class)
    fun isVesselVoyagePostProcessingFinished(scenario: String): Boolean {
        // If the scenario crashed in the other thread, we need to report back to our caller.
        if (scenarioCrashed.contains(scenario)) {
            scenarioCrashed.remove(scenario)
            throw ScenarioCrashedException(ScenarioCrashReason.POST_PROCESSING_VESSEL_VOYAGE_FAILED)
        }

        return !scenarioThreads.containsKey(scenario)
    }

    /**
     * Starts post-processing VesselVoyage data for this [scenario].
     * Where data is sourced from the [sourceScenario].
     * And data is saved for a [storeScenario].
     */
    fun startVesselVoyagePostProcessing(
        scenario: String,
        sourceScenario: String,
        storeScenario: String,
    ) {
        scenarioThreads.computeIfAbsent(scenario) {
            thread {
                try {
                    vesselVoyagePostProcessingService.run(sourceScenario, storeScenario)
                } catch (e: Exception) {
                    LOG.error(e) { "Something went wrong while post-processing VesselVoyage" }
                    scenarioCrashed.add(scenario)
                } finally {
                    scenarioThreads.remove(scenario)
                }
            }
        }
    }

    /**
     * Returns whether the VesselVoyage V2 post-processing of this [scenario] is finished.
     */
    @Throws(ScenarioCrashedException::class)
    fun isVesselVoyagePostProcessingV2Finished(scenario: String): Boolean {
        // If the scenario crashed in the other thread, we need to report back to our caller.
        if (scenarioCrashed.contains(scenario)) {
            scenarioCrashed.remove(scenario)
            throw ScenarioCrashedException(ScenarioCrashReason.POST_PROCESSING_VESSEL_VOYAGE_V2_FAILED)
        }

        return !scenarioThreads.containsKey(scenario)
    }

    /**
     * Starts post-processing VesselVoyage V2 data for this [scenario].
     * Where data is sourced from the [sourceScenario].
     * And data is saved for a [storeScenario].
     */
    fun startVesselVoyagePostProcessingV2(
        scenario: String,
        sourceScenario: String,
        storeScenario: String,
    ) {
        scenarioThreads.computeIfAbsent(scenario) {
            thread {
                try {
                    vesselVoyagePostProcessingV2Service.run(sourceScenario, storeScenario)
                } catch (e: Exception) {
                    LOG.error(e) { "Something went wrong while post-processing VesselVoyage" }
                    scenarioCrashed.add(scenario)
                } finally {
                    scenarioThreads.remove(scenario)
                }
            }
        }
    }
}
