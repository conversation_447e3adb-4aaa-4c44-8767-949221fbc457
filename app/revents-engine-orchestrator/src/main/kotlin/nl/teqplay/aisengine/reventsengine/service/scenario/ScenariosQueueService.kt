package nl.teqplay.aisengine.reventsengine.service.scenario

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.properties.KubernetesProperties
import org.springframework.stereotype.Component

@Component
class ScenariosQueueService(
    private val scenariosDataSource: ScenariosDataSource,
    private val kubernetesProperties: KubernetesProperties,
) {

    /**
     * Get the scenarios that are currently running or next in queue.
     */
    fun getCurrentScenarios(): List<ScenarioState> {
        val scenarios = scenariosDataSource.getAvailableScenarios()
            .filter { it.phase >= ScenarioState.InternalPhase.INITIALIZING_QUEUED }

        if (scenarios.isEmpty()) {
            return emptyList()
        }

        return scenarios
            .sortedWith(compareBy<ScenarioState> { it.queued }.thenByDescending { it.phase })
            .take(kubernetesProperties.maxJobsCount)
    }
}
