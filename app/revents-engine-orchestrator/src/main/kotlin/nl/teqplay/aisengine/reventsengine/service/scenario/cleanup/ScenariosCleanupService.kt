package nl.teqplay.aisengine.reventsengine.service.scenario.cleanup

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_QUEUED
import nl.teqplay.aisengine.reventsengine.common.service.cleanup.ScenariosCleanupTaskService
import nl.teqplay.aisengine.reventsengine.service.scenario.ScenariosQueueService
import org.springframework.stereotype.Component

@Component
class ScenariosCleanupService(
    private val scenariosQueueService: ScenariosQueueService,
    private val scenariosCleanupTaskService: ScenariosCleanupTaskService,
    private val scenariosDataSource: ScenariosDataSource
) {

    /**
     * Clean up scenarios by forcefully "crashing" scenarios that were still running,
     * ensure we tear down remaining resources.
     */
    fun cleanup() {
        val teardownScenarios = mutableListOf<ScenarioState>()

        // cleanup open scenarios, setting them to crashed
        while (true) {
            val scenarios = scenariosQueueService.getCurrentScenarios()
            val progressingScenarios = scenarios.filter { it.phase > INITIALIZING_QUEUED }
            if (progressingScenarios.isEmpty()) {
                break
            }
            progressingScenarios.forEach { scenario ->
                // set scenario to crashed
                scenariosCleanupTaskService.setToCrashedAfterRestart(scenario)
                teardownScenarios.add(scenariosCleanupTaskService.createTeardownScenario(scenario))
            }
        }

        teardownScenarios.forEach { scenario ->
            scenariosDataSource.save(scenario)
        }
    }
}
