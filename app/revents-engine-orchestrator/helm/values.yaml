global:
  storageClass: "gp2"
  namespaceOverride: "teqplay-app"

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/revents-engine-orchestrator

resources:
  requests:
    cpu: 0.1
    memory: 8Gi
  limits:
    memory: 8Gi

mongodb:
  enabled: false

terminationGracePeriodSeconds: 90

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

logs:
  - name: request-log
    file: /var/log/requests.log

env:
  - name: JAVA_TOOL_OPTIONS
    value: -XX:+UseParallelGC
