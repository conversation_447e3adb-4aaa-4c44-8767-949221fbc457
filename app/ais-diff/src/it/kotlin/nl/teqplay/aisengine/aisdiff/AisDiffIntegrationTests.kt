package nl.teqplay.aisengine.aisdiff

import com.fasterxml.jackson.module.kotlin.readValue
import io.github.oshai.kotlinlogging.KotlinLogging
import io.nats.client.Nats
import io.nats.client.PushSubscribeOptions
import io.nats.client.api.StorageType
import io.nats.client.api.StreamConfiguration
import io.nats.client.impl.NatsMessage
import nl.teqplay.aisengine.ObjectMapperConfiguration
import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.AisDestinationChangedEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.MongoDBContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.output.Slf4jLogConsumer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import org.testcontainers.utility.DockerImageName
import java.net.ServerSocket
import java.time.Duration
import java.time.Instant

private val LOG = KotlinLogging.logger {}
// Not sure if this logger works, as these tests don't work at all at the moment (docker image issues; SHAs not lining up)
private val slf4jLogger = LoggerFactory.getLogger("AisDiffIntegrationTests")

@Testcontainers
class AisDiffIntegrationTests {
    private lateinit var app: GenericContainer<*>
    private val network = Network.newNetwork()

    @Container
    private val mongo = MongoDBContainer(
        DockerImageName.parse("mongo:4.4.12")
    )
        .withNetwork(network)
        .withNetworkAliases("mongo")

    @Container
    private val nats = GenericContainer(
        DockerImageName.parse("nats:latest")
    )
        .withLogConsumer(Slf4jLogConsumer(slf4jLogger))
        .withNetwork(network)
        .withNetworkAliases("nats")
        .withExposedPorts(4222)
        .withCommand("-js")
        .waitingFor(Wait.forLogMessage(".*Server is ready.*", 1))
        .withEnv("NATS_EVENT_STREAM_ENABLED", "true")

    private lateinit var sock: ServerSocket
    private val mapper = ObjectMapperConfiguration().objectMapper()

    @BeforeEach
    fun setup() {
        sock = ServerSocket(15000)
        org.testcontainers.Testcontainers.exposeHostPorts(15000)
        app = GenericContainer(
            DockerImageName.parse("050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/ais-diff:latest")
        )
            .withLogConsumer(Slf4jLogConsumer(slf4jLogger))
            .withNetwork(network)
            .dependsOn(mongo)
            .dependsOn(nats)
            .waitingFor(Wait.forLogMessage(".*StartupInfoLogger: Started AisDiffKt in.*", 1))
            .withEnv("MONGODB_HOST", "mongo")
            .withEnv("MONGODB_PORT", "27017")
            .withEnv("NATS_AIS_STREAM_ENABLED", "true")
            .withEnv("NATS_AIS_STREAM_URL", "nats://nats:4222")
            .withEnv("NATS_EVENT_STREAM_URL", "nats://nats:4222")

        nats.start()
        val nc = Nats.connect("nats://${nats.host}:${nats.getMappedPort(4222)}")
        nc.jetStream()

        val jsm = nc.jetStreamManagement()
        val config = StreamConfiguration.builder()
            .name("ais-stream:diff")
            .subjects("ais-stream.diff.>")
            .storageType(StorageType.Memory)
            .build()
        jsm.addStream(config)

        nc.flush(Duration.ofSeconds(1))
        nc.close()

        app.start()
    }

    @AfterEach
    fun teardown() {
        sock.close()
        app.stop()
    }

    @Test
    fun `AisDestinationChangedEvent is fired when the ais destination is changed`() {
        assertTrue(app.isRunning)

        // This is the mmsi of the ship for which we are sending an update
        val mmsi = 123456789
        // Now we will make a message containing a destination change
        val diffMsg = diffMessage(
            mmsi = mmsi,
            messageTime = "2024-01-10T12:00:20Z",
            oldMessageTime = "2024-01-10T12:00:00Z",
            destinationChange = "HERE" to "THERE"
        )

        val nc = Nats.connect("nats://${nats.host}:${nats.getMappedPort(4222)}")
        val js = nc.jetStream()

        // Construct a message to send using the builder
        val natsMessage = NatsMessage.builder().subject("ais-stream.diff.$mmsi").data(mapper.writeValueAsBytes(diffMsg)).build()

        // Publish the message
        js.publish(natsMessage)

        // Get the stream and check that there is just one message sent
        val msgs = getStream()
        msgs.forEach {
            println(it)
        }
        assertEquals(msgs.size, 1)
        LOG.info { "We have received ${msgs.size} messages!!" }
        LOG.info { msgs }

        // Check that the message contains the correct mmsi, imo, old destination and new destination
        val msg = msgs[0] as AisDestinationChangedEvent
        assertEquals(msg.ship.mmsi.toString(), diffMsg.mmsi.toString())
        assertTrue(diffMsg.imo.toString().contains(msg.ship.imo.toString()))
        assertEquals(msg.oldValue.toString(), diffMsg.destination.old.toString())
        assertTrue(diffMsg.destination.changed.toString().contains(msg.newValue.toString()))
    }

    @Test
    fun `ShipMovingStartEvent is fired when a ship starts moving`() {
        assertTrue(app.isRunning)

        // This is the mmsi of the ship for which we are sending an update
        val mmsi = 123456789

        val nc = Nats.connect("nats://${nats.host}:${nats.getMappedPort(4222)}")
        val js = nc.jetStream()

        listOf(
            diffMessage(mmsi, "2024-01-10T12:01:00Z", "2024-01-10T12:00:00Z", speed = 0f),
            diffMessage(mmsi, "2024-01-10T12:02:00Z", "2024-01-10T12:01:00Z", speedChange = 0f to 15f),
            diffMessage(mmsi, "2024-01-10T12:04:00Z", "2024-01-10T12:02:00Z", speed = 15f),
            diffMessage(mmsi, "2024-01-10T12:06:00Z", "2024-01-10T12:04:00Z", speed = 15f),
            diffMessage(mmsi, "2024-01-10T12:08:00Z", "2024-01-10T12:06:00Z", speed = 15f),
            diffMessage(mmsi, "2024-01-10T12:10:00Z", "2024-01-10T12:08:00Z", speed = 15f)
        ).forEach { message ->
            js.publish(
                NatsMessage.builder().subject("ais-stream.diff.$mmsi")
                    .data(
                        mapper.writeValueAsBytes(message)
                    )
                    .build()
            )
        }

        // Get the stream and check that there is just one message sent
        val msgs = getStream()
        LOG.info { "We have received ${msgs.size} messages!!" }
        LOG.info { msgs }
        assertEquals(1, msgs.size)

        // Check that the message contains the correct mmsi, imo, old destination and new destination
        val msg = msgs[0]
        assertEquals(msg.ship.mmsi.toString(), mmsi.toString())
        assertTrue(msg is ShipMovingStartEvent)
    }

    private fun getStream(): List<Event> {
        val nc = Nats.connect("nats://${nats.host}:${nats.getMappedPort(4222)}")
        val js = nc.jetStream()
        val so = PushSubscribeOptions.builder()
            .stream("event-stream")
            .build()

        val sub = js.subscribe(">", so)
        nc.flush(Duration.ofSeconds(5))

        val msgs = mutableListOf<Event>()
        while (true) {
            val delay = if (msgs.isEmpty()) Duration.ofSeconds(120) else Duration.ofSeconds(7)
            val msg = sub.nextMessage(delay) ?: break

            val aismsg = mapper.readValue<Event>(msg.data)
            msgs.add(aismsg)
            msg.ack()
        }

        return msgs
    }

    private fun diffMessage(
        mmsi: Int,
        messageTime: String,
        oldMessageTime: String,
        speed: Float = 12.3f,
        speedChange: Pair<Float, Float>? = null,
        destination: String = "HERE",
        destinationChange: Pair<String, String>? = null
    ) = AisDiffMessage(
        mmsi = mmsi,
        messageTime = Instant.parse(messageTime),
        oldMessageTime = Instant.parse(oldMessageTime),
        sources = setOf("integration-test"),
        location = AisDiffField(Location(12.34, 56.78), null),
        heading = AisDiffField(123, null),
        positionAccuracy = AisDiffField(null, null),
        speedOverGround = when {
            speedChange != null -> AisDiffField(speedChange.first, AisDiffField.Change(speedChange.second))
            else -> AisDiffField(speed, null)
        },
        courseOverGround = AisDiffField(12.4f, null),
        status = AisDiffField(null, null),
        rateOfTurn = AisDiffField(null, null),
        specialManeuverStatus = AisDiffField(null, null),
        imo = AisDiffField(9483346, null),
        name = AisDiffField("EINSTEIN", null),
        callSign = AisDiffField(null, null),
        shipType = AisDiffField(null, null),
        draught = AisDiffField(null, null),
        eta = AisDiffField(null, null),
        destination = when {
            destinationChange != null -> AisDiffField(destinationChange.first, AisDiffField.Change(destinationChange.second))
            else -> AisDiffField(destination, null)
        },
        transponderPosition = AisDiffField(null, null),
        positionSensorType = AisDiffField(null, null),
        aisVersion = AisDiffField(null, null),
        usingDataTerminal = AisDiffField(null, null),
        eni = AisDiffField(null, null)
    )
}
