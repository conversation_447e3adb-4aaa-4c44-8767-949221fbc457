package nl.teqplay.aisengine.aisdiff.util

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.nats.client.Connection
import io.nats.client.JetStreamSubscription
import io.nats.client.PushSubscribeOptions
import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.skeleton.model.Location
import java.time.Duration
import java.time.Instant

private fun generateAisDiffBase(mmsi: Int): AisDiffMessage {
    return AisDiffMessage(
        mmsi = mmsi,
        messageTime = Instant.parse("2023-10-24T12:01:00Z"),
        oldMessageTime = Instant.parse("2023-10-24T12:00:00Z"),
        sources = setOf("integration-test"),
        location = AisDiffField(Location(12.34, 56.78), null),
        heading = AisDiffField(123, null),
        positionAccuracy = AisDiffField(null, null),
        speedOverGround = AisDiffField(12.3f, null),
        courseOverGround = AisDiffField(123.4f, null),
        status = AisDiffField(null, null),
        rateOfTurn = AisDiffField(null, null),
        specialManeuverStatus = AisDiffField(null, null),
        imo = AisDiffField(9483346, null),
        name = AisDiffField("EINSTEIN", null),
        callSign = AisDiffField(null, null),
        shipType = AisDiffField(null, null),
        draught = AisDiffField(null, null),
        eta = AisDiffField(Instant.parse("2023-10-24T12:01:00Z"), null),
        destination = AisDiffField("HERE", null),
        transponderPosition = AisDiffField(null, null),
        positionSensorType = AisDiffField(null, null),
        aisVersion = AisDiffField(null, null),
        usingDataTerminal = AisDiffField(null, null),
        eni = AisDiffField(null, null)
    )
}

fun generateAisDiffDestination(mmsi: Int, destinationChanged: String): AisDiffMessage {
    val aisDiffMessage = generateAisDiffBase(mmsi)
    return aisDiffMessage.copy(destination = AisDiffField("DESTINATION", AisDiffField.Change(destinationChanged)))
}

fun generateAisDiffDraught(mmsi: Int, draughtChanged: Float): AisDiffMessage {
    val aisDiffMessage = generateAisDiffBase(mmsi)
    return aisDiffMessage.copy(draught = AisDiffField(5000f, AisDiffField.Change(draughtChanged)))
}

fun generateAisDiffStatus(mmsi: Int, statusChanged: AisMessage.ShipStatus): AisDiffMessage {
    val aisDiffMessage = generateAisDiffBase(mmsi)
    return aisDiffMessage.copy(status = AisDiffField(AisMessage.ShipStatus.UNDER_WAY_USING_ENGINE, AisDiffField.Change(statusChanged)))
}

fun generateEtaDiffStatus(mmsi: Int, changedTime: Instant): AisDiffMessage {
    val aisDiffMessage = generateAisDiffBase(mmsi)
    return aisDiffMessage.copy(eta = AisDiffField(Instant.ofEpochMilli(0), AisDiffField.Change(changedTime)))
}

fun getStream(natsConnect: Connection): JetStreamSubscription {
    val nc = natsConnect
    val js = nc.jetStream()
    val so = PushSubscribeOptions.builder()
        .stream("event-stream")
        .build()

    nc.flush(Duration.ofSeconds(5))
    return js.subscribe(">", so)
}

inline fun <reified T : Event> getEventsFromStream(natsConnect: Connection, mapper: ObjectMapper): List<T> {
    val sub = getStream(natsConnect)
    val msgs = mutableListOf<T>()
    while (true) {
        val delay = if (msgs.isEmpty()) Duration.ofSeconds(120) else Duration.ofSeconds(7)
        val msg = sub.nextMessage(delay) ?: break
        val aismsg = mapper.readValue<T>(msg.data)
        msgs.add(aismsg)
        msg.ack()
    }
    return msgs
}
