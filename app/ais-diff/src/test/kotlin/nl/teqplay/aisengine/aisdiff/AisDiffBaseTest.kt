package nl.teqplay.aisengine.aisdiff

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.aisdiff.datasource.ShipMovementDatasource
import nl.teqplay.aisengine.aisdiff.model.ShipStartMovementData
import nl.teqplay.aisengine.aisdiff.service.AisShipMovementEventProcessor
import nl.teqplay.aisengine.aisdiff.service.EventHandlerService
import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration
import nl.teqplay.aisengine.nats.stream.EventStreamAutoConfiguration
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.NatsConsumerStream
import nl.teqplay.skeleton.nats.NatsProducerStream
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import java.time.Instant

@ContextConfiguration
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
open class AisDiffBaseTest : BaseTest() {

    companion object {
        const val MMSI = 999999999
        const val MMSI_WITH_START_MOVING_EVENT = 123456789
        const val OLD_IMO = 1111111
        const val NEW_IMO = 7777777
        const val START_EVENT_TEST_ID = "START_EVENT_TEST_ID"

        // February 9, 2023 12:00:00 GMT+01:00
        val OLD_TIME: Instant = Instant.ofEpochSecond(1675940400L)

        // February 9, 2023 12:05:00 GMT+01:00
        val NEW_TIME: Instant = Instant.ofEpochSecond(1675940700L)

        val OLD_LOCATION = Location(0.0, 0.1)
        val NEW_LOCATION = Location(0.1, 0.1)
    }

    private inline fun <reified T, reified U : T> emptyDiffField() = AisDiffField<T?, U>(null, null)
    protected fun createTestAisDiffMessage(): AisDiffMessage {
        return AisDiffMessage(
            mmsi = MMSI,
            messageTime = NEW_TIME,
            oldMessageTime = OLD_TIME,
            sources = setOf("TEST"),
            destination = emptyDiffField(),
            draught = emptyDiffField(),
            imo = AisDiffField(OLD_IMO, AisDiffField.Change(NEW_IMO)),
            location = AisDiffField(OLD_LOCATION, AisDiffField.Change(NEW_LOCATION)),
            eni = emptyDiffField(),
            name = emptyDiffField(),
            callSign = emptyDiffField(),
            eta = emptyDiffField(),
            speedOverGround = emptyDiffField(),
            courseOverGround = emptyDiffField(),
            rateOfTurn = emptyDiffField(),
            heading = emptyDiffField(),
            positionAccuracy = emptyDiffField(),
            transponderPosition = emptyDiffField(),
            positionSensorType = emptyDiffField(),
            aisVersion = emptyDiffField(),
            shipType = emptyDiffField(),
            status = emptyDiffField(),
            specialManeuverStatus = emptyDiffField(),
            usingDataTerminal = emptyDiffField(),
        )
    }

    @TestConfiguration
    class Config {
        @Bean
        fun natsAisDiffConsumerStream(
            natsClientMock: NatsClientMock,
            objectMapper: ObjectMapper
        ): NatsConsumerStream<AisDiffMessage> {
            return natsClientMock.consumerStream(
                stream = AisStreamNatsAutoConfiguration.AIS_STREAM_DIFF,
                deserializer = { objectMapper.readValue(it) }
            )
        }

        @Bean
        fun testNatsEventProducer(
            natsClientMock: NatsClientMock,
            objectMapper: ObjectMapper
        ): NatsProducerStream<Event> {
            return natsClientMock.producerStream(
                stream = EventStreamAutoConfiguration.EVENT_STREAM,
                subjects = listOf(EventStreamAutoConfiguration.EVENT_STREAM_SUBJECT),
                serializer = { objectMapper.writeValueAsBytes(it) }
            )
        }

        @Bean
        fun eventStreamService(producer: NatsProducerStream<Event>): EventStreamService = mock()

        @Bean
        fun eventHandlerService(
            movementEventProcessor: AisShipMovementEventProcessor,
            diffConsumerStream: NatsConsumerStream<AisDiffMessage>,
            eventStreamService: EventStreamService,
            registry: MeterRegistry,
        ) = EventHandlerService(
            eventStreamService,
            movementEventProcessor,
            diffConsumerStream,
            registry
        )

        @Bean
        fun aisEventShipMovementProcessor(shipMovementDatasource: ShipMovementDatasource) =
            AisShipMovementEventProcessor(shipMovementDatasource)

        @Bean
        fun shipMovementDatasource(): ShipMovementDatasource {
            val shipMovementDatasource = mock<ShipMovementDatasource>()
            whenever(shipMovementDatasource.getStartShipMovement(MMSI_WITH_START_MOVING_EVENT)).thenReturn(
                ShipStartMovementData(
                    mmsi = MMSI_WITH_START_MOVING_EVENT,
                    snapshotAtSufficientSpeed = ShipStartMovementData.MessageSnapshot(OLD_TIME, OLD_LOCATION),
                    snapshotAtNoSpeed = null,
                    startEventId = START_EVENT_TEST_ID
                )
            )
            return shipMovementDatasource
        }

        @Bean
        fun natsClientMock() = NatsClientMock()

        @Bean
        fun objectMapper(): ObjectMapper = jacksonObjectMapper()
            .registerModule(JavaTimeModule())

        @Bean
        fun testMeterRegistry(): MeterRegistry = SimpleMeterRegistry()
    }
}
