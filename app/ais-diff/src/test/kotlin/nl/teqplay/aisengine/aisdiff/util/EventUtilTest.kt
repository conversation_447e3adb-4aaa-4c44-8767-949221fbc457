package nl.teqplay.aisengine.aisdiff.util

import nl.teqplay.aisengine.aisdiff.AisDiffBaseTest
import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipStatus
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class EventUtilTest : AisDiffBaseTest() {
    private val testAisDiffMessage = createTestAisDiffMessage()

    @Test
    fun `should create AisDestinationChangedEvent as expected`() {
        val message = testAisDiffMessage.copy(destination = AisDiffField("old", AisDiffField.Change("new")))

        val result = createAisDestinationChangedEvent(message, "new")
        val expectedActualTime = message.messageTime
        val expectedShip = AisShipIdentifier(message.mmsi, message.imo.latest())
        val expectedLocation = message.location.latest()
        val expectedOldValue = "old"
        val expectedNewValue = "new"

        assertEquals(expectedActualTime, result.actualTime)
        assertEquals(expectedShip, result.ship)
        assertEquals(expectedLocation, result.location)
        assertEquals(expectedOldValue, result.oldValue)
        assertEquals(expectedNewValue, result.newValue)
    }

    @Test
    fun `should create AisDraughtChangedEvent as expected`() {
        val message = testAisDiffMessage.copy(draught = AisDiffField(1f, AisDiffField.Change(2f)))

        val result = createAisDraughtChangedEvent(message, 2f)
        val expectedActualTime = message.messageTime
        val expectedShip = AisShipIdentifier(message.mmsi, message.imo.latest())
        val expectedLocation = message.location.latest()
        val expectedOldValue = 1f
        val expectedNewValue = 2f

        assertEquals(expectedActualTime, result.actualTime)
        assertEquals(expectedShip, result.ship)
        assertEquals(expectedLocation, result.location)
        assertEquals(expectedOldValue, result.oldValue)
        assertEquals(expectedNewValue, result.newValue)
    }

    @Test
    fun `should create AisEtaChangedEvent as expected`() {
        val old = Instant.now()
        val new = old.plus(1, ChronoUnit.HOURS)
        val message = testAisDiffMessage.copy(eta = AisDiffField(old, AisDiffField.Change(new)))

        val result = createAisEtaChangedEvent(message, new)
        val expectedActualTime = message.messageTime
        val expectedShip = AisShipIdentifier(message.mmsi, message.imo.latest())
        val expectedLocation = message.location.latest()

        assertEquals(expectedActualTime, result.actualTime)
        assertEquals(expectedShip, result.ship)
        assertEquals(expectedLocation, result.location)
        assertEquals(old, result.oldValue)
        assertEquals(new, result.newValue)
    }

    @Test
    fun `should create AisStatusChangedEvent as expected`() {
        val message = testAisDiffMessage.copy(status = AisDiffField(ShipStatus.UNDEFINED, AisDiffField.Change(ShipStatus.AT_ANCHOR)))

        val result = createAisStatusChangedEvent(message, ShipStatus.AT_ANCHOR)
        val expectedActualTime = message.messageTime
        val expectedShip = AisShipIdentifier(message.mmsi, message.imo.latest())
        val expectedLocation = message.location.latest()
        val expectedOldValue = ShipStatus.UNDEFINED
        val expectedNewValue = ShipStatus.AT_ANCHOR

        assertEquals(expectedActualTime, result.actualTime)
        assertEquals(expectedShip, result.ship)
        assertEquals(expectedLocation, result.location)
        assertEquals(expectedOldValue, result.oldValue)
        assertEquals(expectedNewValue, result.newValue)
    }
}
