package nl.teqplay.aisengine.aisdiff.service

import nl.teqplay.aisengine.aisdiff.AisDiffBaseTest
import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipStatus
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.never
import org.mockito.kotlin.spy
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import java.time.Instant
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EventHandlerServiceTest(
    private val eventHandlerService: EventHandlerService
) : AisDiffBaseTest() {
    private val testAisDiffMessage = createTestAisDiffMessage()

    private fun aisDiffChanges(): Stream<AisDiffMessage> {
        return Stream.of(
            testAisDiffMessage.copy(destination = AisDiffField(null, AisDiffField.Change("New destination"))),
            testAisDiffMessage.copy(destination = AisDiffField("Old destination", AisDiffField.Change("New destination"))),
            testAisDiffMessage.copy(destination = AisDiffField("Old destination", AisDiffField.Change(null))),
            testAisDiffMessage.copy(draught = AisDiffField(0.0f, AisDiffField.Change(0.1f))),
            testAisDiffMessage.copy(draught = AisDiffField(null, AisDiffField.Change(0.2f))),
            testAisDiffMessage.copy(draught = AisDiffField(0.3f, AisDiffField.Change(null))),
            testAisDiffMessage.copy(eta = AisDiffField(null, AisDiffField.Change(Instant.now()))),
            testAisDiffMessage.copy(eta = AisDiffField(Instant.now(), AisDiffField.Change(null))),
            testAisDiffMessage.copy(eta = AisDiffField(Instant.MIN, AisDiffField.Change(Instant.now()))),
            testAisDiffMessage.copy(status = AisDiffField(null, AisDiffField.Change(ShipStatus.UNDEFINED))),
            testAisDiffMessage.copy(status = AisDiffField(ShipStatus.UNDEFINED, AisDiffField.Change(ShipStatus.TOWING_ALONGSIDE)))
        )
    }

    @ParameterizedTest
    @MethodSource("aisDiffChanges")
    fun `Should publish diff event diff field change`(aisDiffMessage: AisDiffMessage) {
        val spyService = spy(eventHandlerService)

        spyService.onMessage(aisDiffMessage)

        verify(spyService).publishDiffEvent(any())
    }

    @Test
    fun `Should not publish diff event when old and new value is the same`() {
        val unchangedAisDiffMessage = testAisDiffMessage.copy(destination = AisDiffField(null, AisDiffField.Change(null)))
        val spyService = spy(eventHandlerService)

        spyService.onMessage(unchangedAisDiffMessage)

        verify(spyService, never()).publishDiffEvent(any())
    }

    @Test
    fun `Should not publish diff event on no change`() {
        val spyService = spy(eventHandlerService)

        spyService.onMessage(testAisDiffMessage)

        verify(spyService, never()).publishDiffEvent(any())
    }

    @Test
    fun `Should publish multiple diff event on multiple diff field changes`() {
        val spyService = spy(eventHandlerService)

        val aisDiffMessage = testAisDiffMessage.copy(
            destination = AisDiffField("Old destination", AisDiffField.Change("New destination")),
            draught = AisDiffField(0.0f, AisDiffField.Change(0.1f)),
            eta = AisDiffField(null, AisDiffField.Change(Instant.now())),
            status = AisDiffField(ShipStatus.UNDEFINED, AisDiffField.Change(ShipStatus.AT_ANCHOR))
        )

        spyService.onMessage(aisDiffMessage)

        verify(spyService, times(4)).publishDiffEvent(any())
    }
}
