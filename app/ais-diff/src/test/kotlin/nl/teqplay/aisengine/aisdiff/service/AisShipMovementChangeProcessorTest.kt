package nl.teqplay.aisengine.aisdiff.service

import nl.teqplay.aisengine.aisdiff.AisDiffBaseTest
import nl.teqplay.aisengine.aisdiff.datasource.ShipMovementBackingDatasource
import nl.teqplay.aisengine.aisdiff.datasource.ShipMovementDatasource
import nl.teqplay.aisengine.aisdiff.model.ShipStartMovementData
import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.event.interfaces.ShipMovingEvent
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.never
import org.mockito.kotlin.spy
import org.mockito.kotlin.verify
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AisShipMovementChangeProcessorTest(
    private val aisShipMovementEventProcessor: AisShipMovementEventProcessor
) : AisDiffBaseTest() {
    private val testAisDiffMessage = createTestAisDiffMessage()

    @Test
    fun `Should create movement start event on speedOverGround change`() {
        val message = testAisDiffMessage.copy(speedOverGround = AisDiffField(4.5f, AisDiffField.Change(5.0f)))
        val event = aisShipMovementEventProcessor.onMessage(message)

        assertTrue(event is ShipMovingStartEvent)

        if (event is ShipMovingStartEvent) {
            val expected = ShipMovingStartEvent(
                _id = event._id, // The ID is generated, so we don't need to check this
                ship = AisShipIdentifier(MMSI, NEW_IMO),
                location = OLD_LOCATION,
                actualTime = OLD_TIME,
                createdTime = event.createdTime, // Created time should be "now", meaning no need to check like the id
                deleted = null,
                regenerated = null
            )

            assertEquals(expected, event)
        }
    }

    @Test
    fun `Should create movement start event on short time between last location but no speedOverGround change`() {
        val message = testAisDiffMessage.copy(speedOverGround = AisDiffField(4.5f, null))
        val event = aisShipMovementEventProcessor.onMessage(message)

        assertTrue(event is ShipMovingStartEvent)

        if (event is ShipMovingStartEvent) {
            val expected = ShipMovingStartEvent(
                _id = event._id, // The ID is generated, so we don't need to check this
                ship = AisShipIdentifier(MMSI, NEW_IMO),
                location = OLD_LOCATION,
                actualTime = OLD_TIME,
                createdTime = event.createdTime, // Created time should be "now", meaning no need to check like the id
                deleted = null,
                regenerated = null
            )

            assertEquals(expected, event)
        }
    }

    @Test
    fun `Should not create start event when already moving`() {
        val spyProcessor = spy(aisShipMovementEventProcessor)
        val message = testAisDiffMessage.copy(
            mmsi = MMSI_WITH_START_MOVING_EVENT,
            speedOverGround = AisDiffField(4.5f, AisDiffField.Change(5.0f))
        )

        val event = spyProcessor.onMessage(message)

        assertNull(event)
        verify(spyProcessor, never()).processStartEvent(anyOrNull(), any())
        verify(spyProcessor).processEndEvent(any(), any())
    }

    private fun noStartEvent(): Stream<AisDiffMessage> {
        return Stream.of(
            testAisDiffMessage.copy(speedOverGround = AisDiffField(null, null)),
            testAisDiffMessage.copy(speedOverGround = AisDiffField(1.0f, null)),
            testAisDiffMessage.copy(speedOverGround = AisDiffField(null, AisDiffField.Change(1.0f))),
            testAisDiffMessage.copy(speedOverGround = AisDiffField(1.0f, AisDiffField.Change(5.0f))),
            testAisDiffMessage.copy(speedOverGround = AisDiffField(5.0f, AisDiffField.Change(1.0f))),
        )
    }

    @Test
    fun `Should create movement end event on sustained speedOverGround change`() {
        val message = testAisDiffMessage.copy(
            mmsi = MMSI_WITH_START_MOVING_EVENT,
            speedOverGround = AisDiffField(1.0f, AisDiffField.Change(1.0f))
        )
        val event = aisShipMovementEventProcessor.onMessage(message)

        assertTrue(event is ShipMovingEndEvent)

        if (event is ShipMovingEndEvent) {
            val expected = ShipMovingEndEvent(
                _id = event._id, // The ID is generated, so we don't need to check this
                startEventId = START_EVENT_TEST_ID,
                ship = AisShipIdentifier(MMSI_WITH_START_MOVING_EVENT, NEW_IMO),
                location = OLD_LOCATION,
                actualTime = OLD_TIME,
                createdTime = event.createdTime, // Created time should be "now", meaning no need to check like the id
                deleted = null,
                regenerated = null
            )

            assertEquals(expected, event)
        }
    }

    @ParameterizedTest
    @MethodSource("noStartEvent")
    fun `Should not create movement start event`(message: AisDiffMessage) {
        val spyProcessor = spy(aisShipMovementEventProcessor)

        val event = spyProcessor.onMessage(message)

        assertNull(event)
        verify(spyProcessor).processStartEvent(anyOrNull(), any())
        verify(spyProcessor, never()).processEndEvent(any(), any())
    }

    private fun noEndEvent(): Stream<AisDiffMessage> {
        return Stream.of(
            testAisDiffMessage.copy(mmsi = MMSI_WITH_START_MOVING_EVENT, speedOverGround = AisDiffField(5.0f, null)),
            testAisDiffMessage.copy(mmsi = MMSI_WITH_START_MOVING_EVENT, speedOverGround = AisDiffField(5.0f, AisDiffField.Change(4.5f))),
            testAisDiffMessage.copy(mmsi = MMSI_WITH_START_MOVING_EVENT, speedOverGround = AisDiffField(5.0f, AisDiffField.Change(10f)))
        )
    }

    @ParameterizedTest
    @MethodSource("noEndEvent")
    fun `Should not create movement end event`(message: AisDiffMessage) {
        val spyProcessor = spy(aisShipMovementEventProcessor)

        val event = spyProcessor.onMessage(message)

        assertNull(event)
        verify(spyProcessor, never()).processStartEvent(anyOrNull(), any())
        verify(spyProcessor).processEndEvent(any(), any())
    }

    private fun mockEventProcessor(): AisShipMovementEventProcessor {
        val shipMovementBackingDatasource = object : ShipMovementBackingDatasource {
            private val map = mutableMapOf<Int, ShipStartMovementData>()

            override fun list(): Iterable<ShipStartMovementData> = map.values.asIterable()

            override fun save(shipStartMovementData: ShipStartMovementData) {
                map[shipStartMovementData.mmsi] = shipStartMovementData
            }

            override fun delete(mmsi: Int) {
                map.remove(mmsi)
            }
        }
        val shipMovementDataSource = ShipMovementDatasource(shipMovementBackingDatasource)
        return AisShipMovementEventProcessor(shipMovementDataSource)
    }

    private fun MutableList<ShipMovingEvent>.correctForTests() = map { event ->
        (event as? ShipMovingStartEvent)?.copy(
            _id = "start",
            createdTime = event.actualTime
        ) ?: (event as? ShipMovingEndEvent)?.copy(
            _id = "end",
            createdTime = event.actualTime,
            startEventId = "start"
        )
    }

    @Test
    fun `Should create movement start and end event`() {
        val processor = mockEventProcessor()

        val messages = listOf(
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH,
                messageTime = Instant.EPOCH.plus(1, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(1.0f, null)
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(1, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(2, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(1.0f, AisDiffField.Change(2.0f))
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(2, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(3, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(2.0f, AisDiffField.Change(4.5f))
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(3, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(4, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, null)
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(4, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(5, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, null)
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(5, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(30, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, null)
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(30, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(35, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, AisDiffField.Change(0.0f))
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(35, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(40, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(0.0f, null)
            ),
        )

        val events = mutableListOf<ShipMovingEvent>()
        messages.forEach { message ->
            val event = processor.onMessage(message)
            if (event != null && event is ShipMovingEvent) {
                events.add(event)
            }
        }

        assertEquals(
            listOf(
                ShipMovingStartEvent(
                    _id = "start",
                    ship = AisShipIdentifier(testAisDiffMessage.mmsi, testAisDiffMessage.imo.latest()),
                    location = testAisDiffMessage.location.old,
                    actualTime = Instant.EPOCH.plus(3, ChronoUnit.MINUTES),
                    createdTime = Instant.EPOCH.plus(3, ChronoUnit.MINUTES),
                ),
                ShipMovingEndEvent(
                    _id = "end",
                    ship = AisShipIdentifier(testAisDiffMessage.mmsi, testAisDiffMessage.imo.latest()),
                    location = testAisDiffMessage.location.old,
                    actualTime = Instant.EPOCH.plus(35, ChronoUnit.MINUTES),
                    createdTime = Instant.EPOCH.plus(35, ChronoUnit.MINUTES),
                    startEventId = "start",
                ),
            ),
            events.correctForTests()
        )
    }

    @Test
    fun `Should create movement end event after threshold`() {
        val processor = mockEventProcessor()

        val messages = listOf(
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH,
                messageTime = Instant.EPOCH.plus(5, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, null)
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(5, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(6, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, AisDiffField.Change(0.0f))
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(6, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(7, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(0.0f, AisDiffField.Change(4.5f))
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(7, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(8, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, AisDiffField.Change(0.0f))
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(8, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(13, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(0.0f, null)
            ),
        )

        val events = mutableListOf<ShipMovingEvent>()
        messages.forEach { message ->
            val event = processor.onMessage(message)
            if (event != null && event is ShipMovingEvent) {
                events.add(event)
            }
        }

        assertEquals(
            listOf(
                ShipMovingStartEvent(
                    _id = "start",
                    ship = AisShipIdentifier(testAisDiffMessage.mmsi, testAisDiffMessage.imo.latest()),
                    location = testAisDiffMessage.location.old,
                    actualTime = Instant.EPOCH,
                    createdTime = Instant.EPOCH,
                ),
                ShipMovingEndEvent(
                    _id = "end",
                    ship = AisShipIdentifier(testAisDiffMessage.mmsi, testAisDiffMessage.imo.latest()),
                    location = testAisDiffMessage.location.old,
                    actualTime = Instant.EPOCH.plus(8, ChronoUnit.MINUTES),
                    createdTime = Instant.EPOCH.plus(8, ChronoUnit.MINUTES),
                    startEventId = "start",
                ),
            ),
            events.correctForTests()
        )
    }

    @Test
    fun `Should create movement start and end event during slow transitions`() {
        val processor = mockEventProcessor()

        val messages = listOf(
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH,
                messageTime = Instant.EPOCH.plus(5, ChronoUnit.MINUTES),
                location = AisDiffField(OLD_LOCATION, null),
                speedOverGround = AisDiffField(4.5f, null)
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(5, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(6, ChronoUnit.MINUTES),
                location = AisDiffField(OLD_LOCATION, null),
                speedOverGround = AisDiffField(4.5f, AisDiffField.Change(0.0f))
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(6, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(11, ChronoUnit.MINUTES),
                location = AisDiffField(OLD_LOCATION, null),
                speedOverGround = AisDiffField(0.0f, null)
            ),
        )

        val events = mutableListOf<ShipMovingEvent>()
        messages.forEach { message ->
            val event = processor.onMessage(message)
            if (event != null && event is ShipMovingEvent) {
                events.add(event)
            }
        }

        assertEquals(
            listOf(
                ShipMovingStartEvent(
                    _id = "start",
                    ship = AisShipIdentifier(testAisDiffMessage.mmsi, testAisDiffMessage.imo.latest()),
                    location = testAisDiffMessage.location.old,
                    actualTime = Instant.EPOCH,
                    createdTime = Instant.EPOCH,
                ),
                ShipMovingEndEvent(
                    _id = "end",
                    ship = AisShipIdentifier(testAisDiffMessage.mmsi, testAisDiffMessage.imo.latest()),
                    location = testAisDiffMessage.location.old,
                    actualTime = Instant.EPOCH.plus(6, ChronoUnit.MINUTES),
                    createdTime = Instant.EPOCH.plus(6, ChronoUnit.MINUTES),
                    startEventId = "start",
                ),
            ),
            events.correctForTests()
        )
    }

    @Test
    fun `Should reset setup for start and end event if the condition is not met anymore`() {
        val processor = mockEventProcessor()

        val messages = listOf(
            // runs setup for start event, doesn't trigger it yet
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH,
                messageTime = Instant.EPOCH.plus(1, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, null)
            ),
            // should reset start event setup
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(1, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(2, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, AisDiffField.Change(0.0f))
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(2, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(3, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(0.0f, AisDiffField.Change(4.5f))
            ),
            // should actually trigger start event
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(3, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(10, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, null)
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(10, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(11, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, AisDiffField.Change(0.0f))
            ),
            // runs setup for end event, doesn't trigger it yet
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(11, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(12, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(0.0f, null)
            ),
            // should reset end event setup
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(12, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(13, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(0.0f, AisDiffField.Change(4.5f))
            ),
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(13, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(14, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(4.5f, AisDiffField.Change(0.0f))
            ),
            // should actually fire the end event
            testAisDiffMessage.copy(
                oldMessageTime = Instant.EPOCH.plus(14, ChronoUnit.MINUTES),
                messageTime = Instant.EPOCH.plus(20, ChronoUnit.MINUTES),
                speedOverGround = AisDiffField(0.0f, null)
            ),
        )

        val events = mutableListOf<ShipMovingEvent>()
        messages.forEach { message ->
            val event = processor.onMessage(message)
            if (event != null && event is ShipMovingEvent) {
                events.add(event)
            }
        }

        assertEquals(
            listOf(
                ShipMovingStartEvent(
                    _id = "start",
                    ship = AisShipIdentifier(testAisDiffMessage.mmsi, testAisDiffMessage.imo.latest()),
                    location = testAisDiffMessage.location.old,
                    actualTime = Instant.EPOCH.plus(3, ChronoUnit.MINUTES),
                    createdTime = Instant.EPOCH.plus(3, ChronoUnit.MINUTES),
                ),
                ShipMovingEndEvent(
                    _id = "end",
                    ship = AisShipIdentifier(testAisDiffMessage.mmsi, testAisDiffMessage.imo.latest()),
                    location = testAisDiffMessage.location.old,
                    actualTime = Instant.EPOCH.plus(14, ChronoUnit.MINUTES),
                    createdTime = Instant.EPOCH.plus(14, ChronoUnit.MINUTES),
                    startEventId = "start",
                ),
            ),
            events.correctForTests()
        )
    }
}
