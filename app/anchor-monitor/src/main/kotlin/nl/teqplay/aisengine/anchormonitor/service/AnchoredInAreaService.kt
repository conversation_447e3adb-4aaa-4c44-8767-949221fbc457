package nl.teqplay.aisengine.anchormonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.anchormonitor.model.AnchorState
import nl.teqplay.aisengine.anchormonitor.service.poma.PomaService
import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.StateAreaInsideEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import org.springframework.stereotype.Component
import java.util.UUID
import java.util.concurrent.atomic.AtomicLong

private val LOG = KotlinLogging.logger {}

/**
 * [AnchoredInAreaService] processes the start/stop events receives from [EventHandlerService],
 */
@Component
class AnchoredInAreaService(
    private val eventStreamService: EventStreamService,
    private val anchoredInAreaStateService: AnchoredInAreaStateService,
    private val pomaService: PomaService,
    meterRegistry: MeterRegistry,
) {

    companion object {

        /**
         * Ship should be sailing below this threshold to be considered anchored.
         */
        const val ANCHORED_THRESHOLD_SPEED = 1f

        /**
         * The max allowed speed when considering if a ship is still anchored.
         * More lenient than [ANCHORED_THRESHOLD_SPEED] to allow for drifting.
         */
        const val ANCHORED_MAX_SPEED = 4.5f
    }

    /**
     * Metric registry for promethium statistics, used to streamline metric naming and tag usage
     */
    private val registry = MetricRegistry.of<AnchoredInAreaService>(meterRegistry, listOf("event-type"))
    private val areaStartEventInputMetric = registry.createGaugeWithClass(Metric.MESSAGE_COUNT_INPUT, AtomicLong(), AreaStartEvent::class)
    private val areaEndEventInputMetric = registry.createGaugeWithClass(Metric.MESSAGE_COUNT_INPUT, AtomicLong(), AreaEndEvent::class)
    private val stateAreaInsideEventInputMetric = registry.createGaugeWithClass(Metric.MESSAGE_COUNT_INPUT, AtomicLong(), StateAreaInsideEvent::class)
    private val anchorEndEventOutputMetric = registry.createGaugeWithClass(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong(), AnchoredEndEvent::class)
    private val anchorStartEventOutputMetric = registry.createGaugeWithClass(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong(), AnchoredStartEvent::class)

    /**
     * filters out non ship movement events and publish the detected events.
     */
    fun process(event: Event) {
        val detectedEvents = detectAnchoredEvents(event)
        detectedEvents.forEach {
            eventStreamService.publish(it)
        }
    }

    /**
     * Pass event to correct processor by event type and ignore non ship movement events.
     * Includes fallback logic to check if ship is still in other anchor areas.
     */
    fun detectAnchoredEvents(event: Event): List<AnchoredEvent> {
        val events = mutableListOf<AnchoredEvent>()

        if (event is ShipMovingStartEvent) {
            // Gets all anchored start events that were triggered based on movement events (version 1).
            val anchorStartEvents = anchoredInAreaStateService.get(event.ship.mmsi).filter { it.version == 1 }
            events.addAll(processMovingStartEvent(event, anchorStartEvents))
            return events
        }

        val areaEvent = event as? AreaEvent ?: return emptyList()

        // Process the main event first
        if (areaEvent.area.type == AreaIdentifier.AreaType.ANCHOR) {
            val anchorStartEvent = anchoredInAreaStateService.get(event.ship.mmsi, event.area)

            if (areaEvent is AreaEndEvent) {
                areaEndEventInputMetric.incrementAndGet()
                if (anchorStartEvent != null) {
                    events.add(processAnchoredEndEvent(areaEvent, anchorStartEvent))
                }
            } else {
                val speedOverGround = when (areaEvent) {
                    is AreaStartEvent -> areaEvent.speedOverGround.also { areaStartEventInputMetric.incrementAndGet() }
                    is StateAreaInsideEvent -> areaEvent.speedOverGround.also { stateAreaInsideEventInputMetric.incrementAndGet() }
                    else -> null
                }

                if (speedOverGround != null) {
                    when {
                        // Already anchored, but moving now, stop anchoring.
                        anchorStartEvent != null && speedOverGround > ANCHORED_MAX_SPEED ->
                            events.add(processAnchoredEndEvent(areaEvent, anchorStartEvent))

                        // Not started yet, start anchoring.
                        anchorStartEvent == null && speedOverGround <= ANCHORED_THRESHOLD_SPEED ->
                            events.add(processAnchoredStartEvent(areaEvent))
                    }
                }
            }
        }

        // Fallback: check if this ship should be ended from other anchor areas
        events.addAll(checkFallbackForShip(areaEvent))

        return events
    }

    /**
     * Process an [AreaEvent], generating an anchorage start event.
     */
    private fun processAnchoredStartEvent(
        event: AreaEvent,
    ): AnchoredStartEvent {
        val areaIdentifier = event.area
        val anchorStartEvent = AnchoredStartEvent(
            _id = UUID.randomUUID().toString(),
            area = areaIdentifier,
            actualTime = event.actualTime,
            ship = event.ship,
            location = event.location
        )

        LOG.debug { "Publishing anchor start event ($anchorStartEvent)" }
        anchorStartEventOutputMetric.incrementAndGet()
        // Saving the start of the anchor event in kv store
        val state = AnchorState(event.ship, areaIdentifier, anchorStartEvent._id, version = 2)
        anchoredInAreaStateService.save(state)

        return anchorStartEvent
    }

    /**
     * Process an [AreaEvent], generating an anchorage end event.
     */
    private fun processAnchoredEndEvent(
        event: AreaEvent,
        anchorStartEvent: AnchorState,
    ): AnchoredEndEvent {
        val anchorEndEvent = AnchoredEndEvent(
            _id = UUID.randomUUID().toString(),
            startEventId = anchorStartEvent.startEventId,
            area = anchorStartEvent.areaIdentifier,
            actualTime = event.actualTime,
            ship = event.ship,
            location = event.location
        )

        LOG.debug { "Publishing anchor end event ($anchorEndEvent)" }
        anchorEndEventOutputMetric.incrementAndGet()
        // Delete from local cache and KV store
        anchoredInAreaStateService.clear(anchorStartEvent)
        return anchorEndEvent
    }

    /**
     * Process a moving start event, generating an anchorage end event.
     */
    private fun processMovingStartEvent(
        event: ShipMovingStartEvent,
        anchorStartEvents: List<AnchorState>,
    ): List<AnchoredEndEvent> {
        // We will close all if the vessel started moving again
        // Not depending on it being in the anchor area
        return anchorStartEvents.map { startEvent ->
            val anchorEndEvent = AnchoredEndEvent(
                _id = UUID.randomUUID().toString(),
                startEventId = startEvent.startEventId,
                area = startEvent.areaIdentifier,
                actualTime = event.actualTime,
                ship = event.ship,
                location = event.location
            )

            LOG.debug { "Publishing anchor end event ($anchorEndEvent)" }
            anchorEndEventOutputMetric.incrementAndGet()
            // Delete from local cache and KV store
            anchoredInAreaStateService.clear(startEvent)
            anchorEndEvent
        }
    }

    /**
     * Fallback mechanism: check if this specific ship should be ended from other anchor areas.
     * Only triggers on AreaStartEvent (ship entered new area).
     * Uses point-in-polygon check to verify if the ship is still in the anchor area.
     */
    private fun checkFallbackForShip(areaEvent: AreaEvent): List<AnchoredEndEvent> {
        // Only check fallback on area start events
        if (areaEvent !is AreaStartEvent) {
            return emptyList()
        }

        val fallbackEvents = mutableListOf<AnchoredEndEvent>()
        val shipAnchorStates = anchoredInAreaStateService.get(areaEvent.ship.mmsi)

        for (anchorState in shipAnchorStates) {
            val anchorId = anchorState.areaIdentifier.id

            if (anchorId == null) {
                anchoredInAreaStateService.clear(anchorState) // Still clean up the state
                continue
            }

            if (anchorState.areaIdentifier.id == areaEvent.area.id) continue

            // Check if the ship's current location is still within the specific anchor area using point-in-polygon
            if (pomaService.isLocationInAnchorage(anchorId, areaEvent.location)) {
                LOG.debug { "Ship ${anchorState.ship.mmsi} is still in anchor area ${anchorState.areaIdentifier.name}, no fallback needed" }
                continue
            } else {
                val anchorage = pomaService.getAnchorageById(anchorId)
                if (anchorage == null) {
                    LOG.warn { "Anchor area $anchorId not found in POMA, creating fallback end event" }
                }
            }

            // Skip if no start event was ever published (no startEventId means no AnchoredStartEvent was sent)
            if (anchorState.startEventId == null) {
                LOG.debug { "Skipping fallback for ship ${anchorState.ship.mmsi} in area ${anchorState.areaIdentifier.name} - no start event was published" }
                anchoredInAreaStateService.clear(anchorState) // Still clean up the state
                continue
            }

            // Create fallback end event for this anchor state
            val fallbackEvent = AnchoredEndEvent(
                _id = UUID.randomUUID().toString(),
                startEventId = anchorState.startEventId,
                area = anchorState.areaIdentifier,
                actualTime = areaEvent.actualTime,
                ship = anchorState.ship,
                location = areaEvent.location
            )

            LOG.info { "Fallback: ending anchor state for ship ${anchorState.ship.mmsi} in area ${anchorState.areaIdentifier.name} (ship now in ${areaEvent.area.name})" }
            anchorEndEventOutputMetric.incrementAndGet()
            anchoredInAreaStateService.clear(anchorState)
            fallbackEvents.add(fallbackEvent)
        }

        return fallbackEvents
    }
}
