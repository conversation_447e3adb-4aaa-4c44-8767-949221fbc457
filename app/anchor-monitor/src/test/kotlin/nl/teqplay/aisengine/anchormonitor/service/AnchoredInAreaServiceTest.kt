package nl.teqplay.aisengine.anchormonitor.service

import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.anchormonitor.model.AnchorState
import nl.teqplay.aisengine.anchormonitor.service.poma.PomaService
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.testing.event.createAnchoredEndEvent
import nl.teqplay.aisengine.testing.event.createAnchoredStartEvent
import nl.teqplay.aisengine.testing.event.createAreaEndEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.aisengine.testing.event.createShipMovingStartEvent
import nl.teqplay.aisengine.testing.event.createStateAreaInsideEvent
import nl.teqplay.aisengine.testing.event.defaultAisShipIdentifier
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.util.concurrent.atomic.AtomicLong
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AnchoredInAreaServiceTest {

    private val eventStreamService = mock<EventStreamService>()
    private val kvBucket = mock<NatsKeyValueBucket<AnchorState>>()
    private val pomaService = mock<PomaService>()
    private val anchoredInAreaStateService = AnchoredInAreaStateService(
        kvBucket
    )
    private val meterRegistry = mock<MeterRegistry>().apply {
        whenever(gauge(any(), any(), any<AtomicLong>())).thenReturn(mock())
    }
    private lateinit var anchoredInAreaService: AnchoredInAreaService

    private val anchor1 = AreaIdentifier(id = "anchor1", type = AreaType.ANCHOR)
    private val anchor2 = AreaIdentifier(id = "anchor2", type = AreaType.ANCHOR)
    private val anchor3 = AreaIdentifier(id = "anchor3", type = AreaType.ANCHOR)

    @BeforeEach
    fun setUp() {
        anchoredInAreaService = AnchoredInAreaService(
            eventStreamService,
            anchoredInAreaStateService,
            pomaService,
            meterRegistry
        )
    }

    companion object {
        const val LAYING_STILL = 0f
        const val MOVING_SPEED = AnchoredInAreaService.ANCHORED_MAX_SPEED + .1f
    }

    data class DetectAnchoredEventsTestData(
        val message: String,
        val input: List<Event>,
        val output: List<Event>,
    )

    @Test
    fun `detectAnchoredEvents - fallback ends anchor states when ship enters new area and is outside anchor polygon`() {
        // Given: Ship is anchored in anchor1 and location check returns false (outside polygon)
        whenever(pomaService.isLocationInAnchorage(any(), any())).thenReturn(false)
        val anchorState = AnchorState(defaultAisShipIdentifier, anchor1, "start-1", version = 2)
        anchoredInAreaStateService.save(anchorState)

        // When: Ship enters a different area outside the anchor area polygon
        val portArea = AreaIdentifier(id = "port1", type = AreaType.PORT)
        val locationOutsideAnchor = Location(55.0, 6.0) // Outside anchor polygon
        val areaStartEvent = createAreaStartEvent(
            ship = defaultAisShipIdentifier,
            area = portArea,
            location = locationOutsideAnchor,
            speedOverGround = 5f
        )

        val events = anchoredInAreaService.detectAnchoredEvents(areaStartEvent)

        // Then: Should create fallback end event for the anchor area
        assertThat(events).hasSize(1)
        val endEvent = events[0] as AnchoredEndEvent
        assertThat(endEvent.area).isEqualTo(anchor1)
        assertThat(endEvent.startEventId).isEqualTo("start-1")

        // And anchor state should be cleared
        assertThat(anchoredInAreaStateService.get(defaultAisShipIdentifier.mmsi)).isEmpty()
    }

    @Test
    fun `detectAnchoredEvents - no fallback when ship is still in anchor area polygon`() {
        // Given: Ship is anchored in anchor1 and location check returns true (still inside polygon)
        whenever(pomaService.isLocationInAnchorage(any(), any())).thenReturn(true)
        val anchorState = AnchorState(defaultAisShipIdentifier, anchor1, "start-1", version = 2)
        anchoredInAreaStateService.save(anchorState)

        // When: Ship enters a different area but is still within the anchor area polygon
        val portArea = AreaIdentifier(id = "port1", type = AreaType.PORT)
        val locationInsideAnchor = Location(52.05, 4.05) // Inside anchor1 polygon
        val areaStartEvent = createAreaStartEvent(
            ship = defaultAisShipIdentifier,
            area = portArea,
            location = locationInsideAnchor,
            speedOverGround = 5f
        )

        val events = anchoredInAreaService.detectAnchoredEvents(areaStartEvent)

        // Then: No fallback end event should be created
        assertThat(events).isEmpty()

        // And anchor state should still exist
        assertThat(anchoredInAreaStateService.get(defaultAisShipIdentifier.mmsi)).hasSize(1)
        assertThat(anchoredInAreaStateService.get(defaultAisShipIdentifier.mmsi)[0].areaIdentifier).isEqualTo(anchor1)
    }

    @Test
    fun `detectAnchoredEvents - no fallback on area end events to avoid duplicates`() {
        // Given: Ship is anchored in anchor1
        val anchorState = AnchorState(defaultAisShipIdentifier, anchor1, "start-1", version = 2)
        anchoredInAreaStateService.save(anchorState)

        // When: Ship leaves anchor1 (AreaEndEvent)
        val areaEndEvent = createAreaEndEvent(
            ship = defaultAisShipIdentifier,
            area = anchor1,
            startEventId = "start-1"
        )

        val events = anchoredInAreaService.detectAnchoredEvents(areaEndEvent)

        // Then: Should only create one end event (no fallback duplicates)
        assertThat(events).hasSize(1)
        val endEvent = events[0] as AnchoredEndEvent
        assertThat(endEvent.area).isEqualTo(anchor1)
        assertThat(endEvent.startEventId).isEqualTo("start-1")
    }

    @Test
    fun `detectAnchoredEvents - backward compatible, end anchored events when ship starts moving`() {
        val anchoredInAreaStateService = AnchoredInAreaStateService(
            kvBucket
        )
        val anchoredInAreaService = AnchoredInAreaService(
            eventStreamService,
            anchoredInAreaStateService,
            pomaService,
            meterRegistry
        )

        val ship = defaultAisShipIdentifier

        // Two events were generated based on movement events, these should trigger anchored end events.
        anchoredInAreaStateService.save(AnchorState(ship = ship, areaIdentifier = anchor1)) // Defaults to version=1.
        anchoredInAreaStateService.save(AnchorState(ship = ship, areaIdentifier = anchor2, version = 1))

        // One event was based on area events, so these should not trigger an end event based on movement.
        anchoredInAreaStateService.save(AnchorState(ship = ship, areaIdentifier = anchor3, version = 2))

        val movingStartEvent = createShipMovingStartEvent()
        val actual = anchoredInAreaService.detectAnchoredEvents(movingStartEvent)

        val expected = listOf(
            createAnchoredEndEvent(area = anchor1),
            createAnchoredEndEvent(area = anchor2),
        )
        assertThat(actual)
            .usingRecursiveComparison().ignoringFields("_id", "startEventId", "createdTime", "deleted")
            .isEqualTo(expected)
    }

    @Test
    fun `checkFallbackForShip - ship still in anchor area, no fallback created`() {
        // Given: Ship is anchored in anchor1 and location check returns true (still inside polygon)
        whenever(pomaService.isLocationInAnchorage(any(), any())).thenReturn(true)
        val anchorState = AnchorState(defaultAisShipIdentifier, anchor1, "start-1", version = 2)
        anchoredInAreaStateService.save(anchorState)

        // When: Ship enters a different area but is still within the anchor area polygon
        val portArea = AreaIdentifier(id = "port1", type = AreaType.PORT)
        val locationInsideAnchor = Location(52.05, 4.05) // Inside anchor1 polygon
        val areaStartEvent = createAreaStartEvent(
            ship = defaultAisShipIdentifier,
            area = portArea,
            location = locationInsideAnchor,
            speedOverGround = 5f
        )

        val events = anchoredInAreaService.detectAnchoredEvents(areaStartEvent)

        // Then: No fallback end event should be created
        assertThat(events).isEmpty()

        // And anchor state should still exist
        assertThat(anchoredInAreaStateService.get(defaultAisShipIdentifier.mmsi)).hasSize(1)
    }

    @Test
    fun `checkFallbackForShip - ship outside anchor area, fallback created`() {
        // Given: Ship is anchored in anchor1 and location check returns false (outside polygon)
        whenever(pomaService.isLocationInAnchorage(any(), any())).thenReturn(false)
        val anchorState = AnchorState(defaultAisShipIdentifier, anchor1, "start-1", version = 2)
        anchoredInAreaStateService.save(anchorState)

        // When: Ship enters a different area and is outside the anchor area polygon
        val portArea = AreaIdentifier(id = "port1", type = AreaType.PORT)
        val locationOutsideAnchor = Location(55.0, 6.0) // Outside anchor1 polygon
        val areaStartEvent = createAreaStartEvent(
            ship = defaultAisShipIdentifier,
            area = portArea,
            location = locationOutsideAnchor,
            speedOverGround = 5f
        )

        val events = anchoredInAreaService.detectAnchoredEvents(areaStartEvent)

        // Then: Should create fallback end event
        assertThat(events).hasSize(1)
        val endEvent = events[0] as AnchoredEndEvent
        assertThat(endEvent.area).isEqualTo(anchor1)
        assertThat(endEvent.startEventId).isEqualTo("start-1")
        assertThat(endEvent.location).isEqualTo(locationOutsideAnchor)

        // And anchor state should be cleared
        assertThat(anchoredInAreaStateService.get(defaultAisShipIdentifier.mmsi)).isEmpty()
    }

    @Test
    fun `checkFallbackForShip - anchor area not found in POMA, fallback created with warning`() {
        // Given: Ship is anchored in an area not available in POMA (location check returns false)
        whenever(pomaService.isLocationInAnchorage(any(), any())).thenReturn(false)
        whenever(pomaService.getAnchorageById(any())).thenReturn(null)
        val unknownAnchor = AreaIdentifier(id = "unknown-anchor", type = AreaType.ANCHOR)
        val anchorState = AnchorState(defaultAisShipIdentifier, unknownAnchor, "start-1", version = 2)
        anchoredInAreaStateService.save(anchorState)

        // When: Ship enters a different area
        val portArea = AreaIdentifier(id = "port1", type = AreaType.PORT)
        val areaStartEvent = createAreaStartEvent(
            ship = defaultAisShipIdentifier,
            area = portArea,
            speedOverGround = 4f
        )

        val events = anchoredInAreaService.detectAnchoredEvents(areaStartEvent)

        // Then: Should create fallback end event (area not found in POMA)
        val endEvent = events[1] as AnchoredEndEvent
        assertThat(endEvent.area).isEqualTo(unknownAnchor)
        assertThat(endEvent.startEventId).isEqualTo("start-1")

        // And anchor state should be cleared
        assertThat(anchoredInAreaStateService.get(defaultAisShipIdentifier.mmsi)).isEmpty()
    }

    @Test
    fun `checkFallbackForShip - multiple anchor states, mixed fallback behavior`() {
        // Given: Ship is anchored in two areas with different location check results
        // anchor1: ship is still inside (true), anchor2: ship is outside (false)
        whenever(pomaService.isLocationInAnchorage(eq("anchor1"), any())).thenReturn(true)
        whenever(pomaService.isLocationInAnchorage(eq("anchor2"), any())).thenReturn(false)
        val anchorState1 = AnchorState(defaultAisShipIdentifier, anchor1, "start-1", version = 2)
        val anchorState2 = AnchorState(defaultAisShipIdentifier, anchor2, "start-2", version = 2)
        anchoredInAreaStateService.save(anchorState1)
        anchoredInAreaStateService.save(anchorState2)

        // When: Ship enters a different area at a location that's still inside anchor1 but outside anchor2
        val portArea = AreaIdentifier(id = "port1", type = AreaType.PORT)
        val locationInsideAnchor1 = Location(52.05, 4.05) // Inside anchor1, outside anchor2
        val areaStartEvent = createAreaStartEvent(
            ship = defaultAisShipIdentifier,
            area = portArea,
            location = locationInsideAnchor1,
            speedOverGround = 4f
        )

        val events = anchoredInAreaService.detectAnchoredEvents(areaStartEvent)

        // Then: Should create fallback end event only for anchor2
        assertThat(events).hasSize(1)
        val endEvent = events[0] as AnchoredEndEvent
        assertThat(endEvent.area).isEqualTo(anchor2)
        assertThat(endEvent.startEventId).isEqualTo("start-2")

        // And only anchor1 state should remain
        val remainingStates = anchoredInAreaStateService.get(defaultAisShipIdentifier.mmsi)
        assertThat(remainingStates).hasSize(1)
        assertThat(remainingStates[0].areaIdentifier).isEqualTo(anchor1)
    }

    @Test
    fun `checkFallbackForShip - no fallback on non-AreaStartEvent`() {
        // Given: Ship is anchored in anchor1
        val anchorState = AnchorState(defaultAisShipIdentifier, anchor1, "start-1", version = 2)
        anchoredInAreaStateService.save(anchorState)

        // When: Ship has StateAreaInsideEvent (not AreaStartEvent)
        val stateEvent = createStateAreaInsideEvent(
            ship = defaultAisShipIdentifier,
            area = anchor1,
            speedOverGround = 4f
        )

        val events = anchoredInAreaService.detectAnchoredEvents(stateEvent)

        // Then: No fallback should be triggered (only on AreaStartEvent)
        // The event might trigger other logic but not fallback
        val fallbackEvents = events.filterIsInstance<AnchoredEndEvent>()
            .filter { it.startEventId == "start-1" }
        assertThat(fallbackEvents).isEmpty()
    }

    @Test
    fun `checkFallbackForShip - skip anchor state without startEventId`() {
        // Given: Ship has anchor state without published start event
        val anchorState = AnchorState(defaultAisShipIdentifier, anchor1, null, version = 2)
        anchoredInAreaStateService.save(anchorState)

        // When: Ship enters a different area
        val portArea = AreaIdentifier(id = "port1", type = AreaType.PORT)
        val locationOutsideAnchor = Location(55.0, 6.0)
        val areaStartEvent = createAreaStartEvent(
            ship = defaultAisShipIdentifier,
            area = portArea,
            location = locationOutsideAnchor,
            speedOverGround = 5f
        )

        val events = anchoredInAreaService.detectAnchoredEvents(areaStartEvent)

        // Then: No fallback end event should be created (no startEventId)
        assertThat(events).isEmpty()

        // And anchor state should be cleared anyway
        assertThat(anchoredInAreaStateService.get(defaultAisShipIdentifier.mmsi)).isEmpty()
    }

    @ParameterizedTest
    @MethodSource("detectAnchoredEventsTestData")
    fun detectAnchoredEvents(data: DetectAnchoredEventsTestData) {
        val actual = data.input.flatMap { anchoredInAreaService.detectAnchoredEvents(it) }

        assertThat(actual)
            .usingRecursiveComparison().ignoringFields("_id", "startEventId", "createdTime", "deleted")
            .isEqualTo(data.output)
    }

    private fun detectAnchoredEventsTestData() = Stream.of(
        DetectAnchoredEventsTestData(
            message = "no events",
            input = emptyList(),
            output = emptyList()
        ),
        DetectAnchoredEventsTestData(
            message = "simple area start/end, anchor events equal",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "simple area start/end, anchor events equal, even if the end event doesn't have speedOverGround",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = null,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "exiting the area with a low speed should not start an anchor event",
            input = listOf(
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = emptyList()
        ),
        DetectAnchoredEventsTestData(
            message = "missing speed doesn't cause flip-flop",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = null,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = null,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(3))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = null,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(3))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "multiple area start/end, anchor events equal",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                ),
                createAreaStartEvent(
                    area = anchor2,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(2))
                ),
                createAreaEndEvent(
                    area = anchor2,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(3))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                ),
                createAnchoredStartEvent(
                    area = anchor2,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(2))
                ),
                createAnchoredEndEvent(
                    area = anchor2,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(3))
                ),
            )
        ),
        DetectAnchoredEventsTestData(
            message = "interleaved area start/end, anchor events equal",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH
                ),
                createAreaStartEvent(
                    area = anchor2,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(2))
                ),
                createAreaEndEvent(
                    area = anchor2,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(3))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH
                ),
                createAnchoredStartEvent(
                    area = anchor2,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                ),
                // Creates fallback for first anchor
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                ),
                createAnchoredEndEvent(
                    area = anchor2,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(3))
                ),
            )
        ),
        DetectAnchoredEventsTestData(
            message = "anchored after area start, area end always ends",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "anchored during area start, starts moving before area end",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "anchored inside area start/end",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "anchored inside area start/end multiple times",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(8))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(9))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(8))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(9))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "anchored inside area, drifting should result in only one anchor event pair",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 1.5f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(4))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 1.1f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(6))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 1.5f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(7))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(8))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(9))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(4))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(9))
                ),
            )
        ),
        DetectAnchoredEventsTestData(
            message = "not anchored, drifting with ANCHORED_MAX_SPEED",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = 4.4f,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 4.3f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 1.1f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(6))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 4.5f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(7))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = emptyList()
        ),
        DetectAnchoredEventsTestData(
            message = "anchored inside area, lay still then drifting with more than ANCHORED_MAX_SPEED result to anchor events",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 4.3f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(6))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(7))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(6))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(7))
                ),
            )
        ),
    )
}
