package nl.teqplay.aisengine.anchormonitor.service.poma

import nl.teqplay.poma.api.v1.Anchorage
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.poma.client.PomaInfrastructureClient
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import nl.teqplay.poma.api.v1.Location as PomaLocation

class PomaServiceTest {

    private lateinit var pomaInfrastructureClient: PomaInfrastructureClient
    private lateinit var pomaService: PomaService

    @BeforeEach
    fun setUp() {
        pomaInfrastructureClient = mock()
        pomaService = PomaService(pomaInfrastructureClient)
    }

    @Test
    fun `should return null when no anchorages available`() {
        whenever(pomaInfrastructureClient.getAnchorages()).thenReturn(emptyArray())
        val testService = PomaService(pomaInfrastructureClient)

        assertNull(testService.getAnchorageById("test-id"))
    }

    @Test
    fun `should filter out anchorages without id`() {
        val anchorageWithId = createAnchorage(
            _id = "test-id",
            name = "Test Anchorage",
            area = listOf(
                PomaLocation(52.0, 4.0),
                PomaLocation(52.1, 4.0),
                PomaLocation(52.1, 4.1),
                PomaLocation(52.0, 4.1)
            )
        )
        val anchorageWithoutId = createAnchorage(
            _id = null,
            name = "Test Anchorage",
            area = listOf(
                PomaLocation(52.0, 4.0),
                PomaLocation(52.1, 4.0),
                PomaLocation(52.1, 4.1),
                PomaLocation(52.0, 4.1)
            )
        )

        whenever(pomaInfrastructureClient.getAnchorages()).thenReturn(arrayOf(anchorageWithoutId, anchorageWithId))
        val testService = PomaService(pomaInfrastructureClient)

        // Should find the anchorage with ID
        assertNotNull(testService.getAnchorageById("test-id"))
        // Should not find the anchorage without ID
        assertNull(testService.getAnchorageById("unknown-id"))
    }

    @Test
    fun `should correctly identify location inside anchorage area`() {
        val anchorage = createAnchorage(
            _id = "test-id",
            name = "Test Anchorage",
            area = listOf(
                PomaLocation(52.0, 4.0),
                PomaLocation(52.1, 4.0),
                PomaLocation(52.1, 4.1),
                PomaLocation(52.0, 4.1)
            )
        )

        whenever(pomaInfrastructureClient.getAnchorages()).thenReturn(arrayOf(anchorage))
        val testService = PomaService(pomaInfrastructureClient)

        // Location inside the polygon
        val insideLocation = Location(52.05, 4.05)
        assertTrue(testService.isLocationInAnchorage("test-id", insideLocation))

        // Location outside the polygon
        val outsideLocation = Location(53.0, 5.0)
        assertFalse(testService.isLocationInAnchorage("test-id", outsideLocation))
    }

    @Test
    fun `should return false for unknown anchorage id`() {
        whenever(pomaInfrastructureClient.getAnchorages()).thenReturn(emptyArray())
        val testService = PomaService(pomaInfrastructureClient)

        val location = Location(52.05, 4.05)
        assertFalse(testService.isLocationInAnchorage("unknown-id", location))
    }

    @Test
    fun `should return false for anchorage without area`() {
        val anchorage = createAnchorage(
            _id = "test-id",
            name = "Test Anchorage",
            area = emptyList()
        )

        whenever(pomaInfrastructureClient.getAnchorages()).thenReturn(arrayOf(anchorage))
        val testService = PomaService(pomaInfrastructureClient)

        val location = Location(52.05, 4.05)
        assertFalse(testService.isLocationInAnchorage("test-id", location))
    }

    private fun createAnchorage(
        _id: String?,
        name: String,
        area: List<PomaLocation>
    ): Anchorage {
        return Anchorage(
            name = name,
            ports = emptyList(),
            margin = 0.0,
            ownerId = null,
            maxLength = null,
            draught = null,
            location = PomaLocation(0.0, 0.0),
            area = area,
            areaSizeInM2 = null,
            manualOverriddenArea = false,
            humanReadableName = null,
            uniqueId = null,
            modelType = "",
            source = null,
            sourceType = null,
            _id = _id
        )
    }
}
