# ship-history-migrator

## Overview

![](./assets/diagram.png)

There are essentially 4 steps to the migration:

1. get the dates for the current month
2. read the data of the current month
3. write the data of the current month
4. progress to the next month

Repeating these steps until all data has been migrated.

NATS is used to store the progress over time, as well as to temporarily store the converted data before it's written to
S3.

S3 is used to list the files in the old bucket, read all the data and write the migrated data into a new bucket.

## Install

Create a node group with the proper taints and labels for the `ship-history-migrator`. Ensure a node is available of
instance type `m6a.4xlarge`, then install the apps.

```
helm upgrade -i -n teqplay-app nats-ship-history-migrator nats/nats -f helm/nats.yaml --version 0.19.17
helm upgrade -i -n teqplay-app ship-history-migrator teqplay/skeleton-mongo-app -f helm/values.yaml
```

## Start migration

Start a run to test for a single month:

```
nats kv put progress progress '{"from": "2023-06","toInclusive": "2023-06","current": "2023-06"}'
```

Start a run for multiple years in reverse order:

```
nats kv put progress progress '{"from": "2023-06","toInclusive": "2017-01","current": "2023-06"}'
```

## Pause/Stop migration

To pause or stop the migration, just scale down the `ship-history-migrator` instance.

Scale it back up to 1 replica to restart.

## Uninstall

```
helm uninstall -n teqplay-app nats-ship-history-migrator
helm uninstall -n teqplay-app ship-history-migrator
```
