buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
}

dependencies {
    implementation("nl.teqplay.skeleton:metrics:$skeletonVersion")
    implementation(project(":lib:bucketing-ship"))

    implementation("nl.teqplay.skeleton:nats:$skeletonVersion")
    testImplementation("nl.teqplay.skeleton:nats-test:$skeletonVersion")

    implementation("com.antkorwin:xsync:$xsyncVersion")

    implementation("org.awaitility:awaitility-kotlin:$awaitilityVersion")
}
