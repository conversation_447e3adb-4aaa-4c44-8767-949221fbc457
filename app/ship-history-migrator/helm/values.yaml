global:
  storageClass: "gp2"
  namespaceOverride: "teqplay-app"

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/ship-history-migrator
  pullPolicy: Always
  tag: latest

resources:
  requests:
    cpu: 14
    memory: 50Gi
  limits:
    memory: 50Gi

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

mongodb:
  enabled: false

nodeSelector:
  app.teqplay.nl/nodegroup: ship-history-migrator

tolerations:
  - key: nodegroup
    operator: Equal
    value: ship-history-migrator
    effect: NoSchedule

terminationGracePeriodSeconds: 90
