# revents-engine

> Gathers AIS data from storage and publishes diff events for various monitors to consume and store the resulting
> events.

- [Disclaimer](#markdown-header-disclaimer)
- [Endpoints](#markdown-header-endpoints)
- [Running revents](#markdown-header-running-revents)
- [Watching progress](#markdown-header-watching-progress)

---

## Disclaimer

Running revents is an involved process of spinning up various monitors and orchestrating them with the correct
ordering, etc.

That's why you don't need to interact with the revents-engine and the various monitors directly. You can use
the `revents-engine-api`, which will take care of all of this. See its README for docs.

## Endpoints

| Endpoint                | Description                            | Explanation                                |
|:------------------------|:---------------------------------------|:-------------------------------------------|
| `POST /revents/run`     | Run revents for specific interests.    | [here](#markdown-header-running-revents)   |
| `GET /revents/progress` | Watch the progress of the revents run. | [here](#markdown-header-watching-progress) |

## Running revents

The `POST /revents/run` endpoint, you'll need to supply your interests.

| Field                   | Description                                                                                                                                                                                                                                                              |
|:------------------------|:-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `id`                    | Identifier for this run.                                                                                                                                                                                                                                                 |
| `marginInDays`          | Based on the `interests` a `TimeWindow` is constructed based on the minimum and maximum timestamps. This margin enlarges that `TimeWindow`.                                                                                                                              |
| `interests`             | A list of ships you are interested in, along with their `TimeWindow` of being relevant. History of this ship will only be requested within this `TimeWindow` plus the applied margins.                                                                                   |
| `resolveServiceVessels` | If enabled, will request roles from CSI to determine all service vessels, after requesting history for all `interests` it will determine which service vessels have been near enough for their history to also be included. (Used primarily when encounters are enabled) |

## Watching progress

The `GET /revents/progress` endpoint, it returns the progress of the current run.

| Field     | Description                                                                                                                                                                                                                                       |
|:----------|:--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `id`      | Identifier for this run. You should expect to see the same `id` being returned as you used when you created your run. If not, then it's not running; another run was already running, so your run is dropped, or another run started after yours. |
| `window`  | The `TimeWindow` that's based on your `interests`, enlarged by the `marginInDays`.                                                                                                                                                                |
| `cursor`  | Where the revents-engine is within the `window`. Will always be `window.from <= cursor <= window.to`. It will equal `window.to` once finished. (If `cursor == window.to`, don't assume the revents-engine finished until `done` is set)           |
| `done`    | Whether the revents-engine is done with this run. If set, you can be sure the revents-engine is finished and is ready for a new revents run.                                                                                                      |
| `crashed` | Whether the revents-engine crashing during the run. If it did, `crashed` will first be set. When revents-engine is finished with the run, `done` will be set.                                                                                     |

> **IMPORTANT**: you'll need to check:
> - The `id` matches your run.
> - revents-engine indicates when it's `done`.
> - If revents-engine is `done`, check `crashed` to see if it crashed or not.
