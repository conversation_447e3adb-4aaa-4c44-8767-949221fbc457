buildscript {
    project.extra.set("baseVersion", "2.1.1")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":api:nats-stream-ais-publish"))
    implementation(project(":api:client-ship-history"))

    implementation("nl.teqplay.skeleton:metrics:$skeletonVersion")
    implementation(project(":lib:bucketing-ship"))
    implementation(project(":lib:revents-global"))
    implementation(project(":lib:revents-csi-common"))
    implementation(project(":lib:encounter-monitor-common"))

    implementation("nl.teqplay.skeleton:actuator:$skeletonVersion")
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:$springdocVersion")

    implementation("org.awaitility:awaitility:$awaitilityVersion")
    implementation("org.awaitility:awaitility-kotlin:$awaitilityVersion")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
