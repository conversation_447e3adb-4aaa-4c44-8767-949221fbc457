global:
  storageClass: "gp2"
  namespaceOverride: "teqplay-app"

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/revents-engine

resources:
  requests:
    cpu: 4
    memory: 12Gi
  limits:
    memory: 18Gi

mongodb:
  enabled: false

terminationGracePeriodSeconds: 90

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

logs:
  - name: request-log
    file: /var/log/requests.log