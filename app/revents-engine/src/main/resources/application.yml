nats:
  ais-stream:
    enabled: true
    url: nats://localhost:4222
    username: ais-stream-consume-revents-engine
    password:

internal-api:
  url: https://internalapi.teqplay.dev
  domain: keycloak.teqplay.nl
  realm: prod
  client-id: revents-engine
  client-secret: TODO-revents

csi:
  url: https://csibackend.teqplay.nl
  domain: keycloak.teqplay.nl
  realm: prod
  client-id: revents-engine
  client-secret: TODO-revents

bucket:
  sweeper:
    # matches the sweeper max age of ship-history
    max-age: P200D
  archive:
    credentials:
      access-key-id: TODO-revents
      secret-key: TODO-revents

    ship:
      mmsi:
        enabled: true
        name: ship-history
      area:
        enabled: true
        name: ship-history
      area-index:
        enabled: true
        name: ship-history

# Prometheus JVM stats exposing
management:
  metrics:
    enable:
      jvm: true
    tags:
      component: revents-engine
  endpoints:
    web:
      exposure:
        include:
          - health
          - prometheus
