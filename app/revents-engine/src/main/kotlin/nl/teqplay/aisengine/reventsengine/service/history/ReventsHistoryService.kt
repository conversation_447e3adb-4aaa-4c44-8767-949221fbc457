package nl.teqplay.aisengine.reventsengine.service.history

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import java.time.Duration
import java.time.LocalDate
import java.time.ZoneOffset

interface ReventsHistoryService {
    fun init(mmsis: Set<Int>)
    fun getAllVessels(): Set<Int>
    fun findHistory(date: LocalDate, mmsi: Int, filterHistory: Scenario.FilterHistory): Iterator<AisMessage>
    fun pruneHistory(mmsi: Int)
    fun findShipsNearAreaBuckets(date: LocalDate, locations: Sequence<Location>): Set<Int>

    /**
     * Buckets are defined on a per-day basis, so converting a [date] to a [TimeWindow] uses a timespan of 1 day.
     */
    fun toTimeWindow(date: LocalDate): TimeWindow {
        return TimeWindow(
            time = date.atStartOfDay().toInstant(ZoneOffset.UTC),
            duration = Duration.ofDays(1)
        )
    }
}
