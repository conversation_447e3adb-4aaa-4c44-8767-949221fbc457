package nl.teqplay.aisengine.reventsengine.service.history

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.bucketing.BUCKET_ID_SEPARATOR
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryAreaIndexArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveClientService
import nl.teqplay.aisengine.bucketing.storage.archive.readS3ObjectBytes
import nl.teqplay.aisengine.bucketing.storage.bucket.shared.BucketIdByArea
import nl.teqplay.aisengine.bucketing.storage.ship.area.implementation.ShipHistoryByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.ship.mmsi.implementation.ShipHistoryByMmsiReadCache
import nl.teqplay.aisengine.encountermonitor.distanceInMeters
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.util.withLimitedRetries
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.skeleton.util.createBoundingBoxFromCircle
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.YearMonth
import java.util.concurrent.ConcurrentHashMap
import java.util.zip.ZipInputStream

@Component
class ShipHistoryFromS3Service(
    private val shipHistoryByMmsiReadCache: ShipHistoryByMmsiReadCache,
    private val shipHistoryByAreaReadCache: ShipHistoryByAreaReadCache,

    bucketArchiveGlobal: BucketArchiveGlobalProperties,
    private val areaIndexProperties: ShipHistoryAreaIndexArchiveProperties,

    private val objectMapper: ObjectMapper
) {

    private val archiveStorageByMmsi = requireNotNull(shipHistoryByMmsiReadCache.archiveStorage)
    private val archiveClientByMmsiService = archiveStorageByMmsi.archiveClientService
    private val archiveByMmsiMap = ConcurrentHashMap<Int, CachedS3Object>()

    private val archiveClientIndexByAreaService = ArchiveClientService(areaIndexProperties, bucketArchiveGlobal)

    /**
     * Object storing cached data as [bytes], for a specific [yearMonth].
     * Used for caching, and is invalidated if the requested [yearMonth] differs.
     *
     * Overriding the [equals] and [hashCode] since [bytes] is an array.
     */
    data class CachedS3Object(
        val yearMonth: YearMonth,
        val bytes: ByteArray
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false
            other as CachedS3Object
            return yearMonth == other.yearMonth
        }

        override fun hashCode(): Int = yearMonth.hashCode()
    }

    /**
     * Object similar to [CachedS3Object], but storing the aggregated result instead of the raw data.
     */
    data class CachedS3AreaObject(
        val yearMonth: YearMonth,
        val data: Map<LocalDate, Set<Int>>
    )

    /**
     * Find history for the ship by [mmsi] for the given [date].
     * And caching the requested S3 object for later reuse.
     */
    fun findHistory(
        date: LocalDate,
        mmsi: Int
    ): Sequence<AisHistoricMessage> {
        val yearMonth = YearMonth.of(date.year, date.monthValue)
        val cachedS3Object = getCachedS3ObjectForMmsi(mmsi, date, yearMonth) ?: return emptySequence()
        return readDataFromS3ObjectForMmsi(date, cachedS3Object)
    }

    private fun getCachedS3ObjectForMmsi(
        mmsi: Int,
        date: LocalDate,
        yearMonth: YearMonth
    ): CachedS3Object? = archiveByMmsiMap.compute(mmsi) { _, currentValue ->
        if (currentValue?.yearMonth == yearMonth) {
            return@compute currentValue
        }

        val bucketIdentifier = shipHistoryByMmsiReadCache.factory.bucketIdentifier
        val bucketId = bucketIdentifier.id.getBucketId(date, mmsi)
        val archiveBucketId = bucketIdentifier.archive.getArchiveBucketId(bucketId)
        val archiveKey = archiveStorageByMmsi.getArchiveKey(archiveBucketId)
        val bytes = withLimitedRetries {
            readS3ObjectBytes(archiveClientByMmsiService, archiveKey)
        } ?: return@compute null
        return@compute CachedS3Object(yearMonth, bytes)
    }

    /**
     * Collects the history within the [cachedS3Object] for this [date].
     */
    private fun readDataFromS3ObjectForMmsi(
        date: LocalDate,
        cachedS3Object: CachedS3Object
    ): Sequence<AisHistoricMessage> {
        val zipInputStream = ZipInputStream(cachedS3Object.bytes.inputStream())
        var data = emptySequence<AisHistoricMessage>()
        do {
            val entry = zipInputStream.nextEntry
            if (entry != null) {
                val fileDate = LocalDate.parse(entry.name.removeSuffix(".json"))
                if (fileDate == date) {
                    val content = zipInputStream.readAllBytes()
                    val iterator = objectMapper
                        .readerFor(AisHistoricOrderedDiffMessage::class.java)
                        .readValues<AisHistoricOrderedDiffMessage>(content)
                    data = shipHistoryByMmsiReadCache.factory.bucketFormatter
                        .unzipAsSequence(iterator.asSequence())
                        .mapNotNull(shipHistoryByMmsiReadCache.factory.bucketFormatter::convertToOutput)
                }
            }
        } while (entry != null)

        // ensure to close the ZIP
        zipInputStream.close()

        return data
    }

    /**
     * Prune the cached history for this [mmsi].
     */
    fun prune(
        mmsi: Int
    ) {
        archiveByMmsiMap.remove(mmsi)
    }

    /**
     * Finds MMSIs near the [locations] for this [date].
     * First pruning the cache wherever necessary, and then fetching areas (that weren't already cached)
     * and collecting the MMSIs.
     */
    fun findShipsNearAreaBuckets(
        date: LocalDate,
        window: TimeWindow,
        locations: Sequence<Location>
    ): Set<Int> {
        val bucketId = shipHistoryByAreaReadCache.factory.bucketIdentifier.id as BucketIdByArea

        // encounter detection distance
        val radiusInMeters = distanceInMeters

        val areaIds = locations
            .map {
                // get bounding box from circle, then shrink it down to the bucket size
                // to reduce unique/preciseness of the bounding boxes
                val boundingBox = createBoundingBoxFromCircle(it, radiusInMeters)
                BoundingBox(
                    bottomleft = bucketId.floorToBucketSize(boundingBox.bottomleft),
                    topright = bucketId.ceilToBucketSize(boundingBox.topright)
                )
            }
            .flatMap { boundingBox ->
                // convert the bounding box in a set of archive bucket ids
                bucketId.getArchiveBucketIds(window, boundingBox).asSequence()
            }
            .map { it.substringAfter(BUCKET_ID_SEPARATOR) }
            .toSet()

        return getIndexedVesselsForArea(date, areaIds)
    }

    private fun getIndexedVesselsForArea(
        date: LocalDate,
        areaIds: Set<String>
    ): Set<Int> {
        val prefix = "${areaIndexProperties.prefix}${areaIndexProperties.prefixSeparator}"
        val bytes = withLimitedRetries {
            readS3ObjectBytes(archiveClientIndexByAreaService, "$prefix$date.zip")
        } ?: return emptySet()

        val mmsis = mutableSetOf<Int>()

        val zipInputStream = ZipInputStream(bytes.inputStream())
        do {
            val entry = zipInputStream.nextEntry
            if (entry != null) {
                val content = zipInputStream.readAllBytes()
                val iterator = objectMapper
                    .readerForMapOf(Array<Int>::class.java)
                    .readValue<Map<String, Array<Int>>>(content)
                iterator.forEach {
                    if (it.key in areaIds) {
                        mmsis += it.value
                    }
                }
            }
        } while (entry != null)
        zipInputStream.close()

        return mmsis
    }
}
