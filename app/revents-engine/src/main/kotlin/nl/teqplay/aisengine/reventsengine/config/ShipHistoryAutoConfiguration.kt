package nl.teqplay.aisengine.reventsengine.config

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryByMmsiArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.ship.area.implementation.ShipHistoryByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.ship.mmsi.implementation.ShipHistoryByMmsiReadCache
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class ShipHistoryAutoConfiguration {

    @Bean
    fun shipHistoryByMmsiReadCache(
        objectMapper: ObjectMapper,
        bucket: BucketProperties,
        archive: ShipHistoryByMmsiArchiveProperties,
        archiveGlobal: BucketArchiveGlobalProperties,
    ) = ShipHistoryByMmsiReadCache(
        bucketCacheFactory = BucketCacheFactory,
        archiveStorageFactory = ArchiveStorageFactory,
        mongoDatabase = null,
        objectMapper = objectMapper,
        bucket = bucket,
        archive = archive,
        archiveGlobal = archiveGlobal
    )

    @Bean
    fun shipHistoryByAreaReadCache(
        objectMapper: ObjectMapper,
        bucket: BucketProperties,
        archive: ShipHistoryByAreaArchiveProperties,
        archiveGlobal: BucketArchiveGlobalProperties,
    ) = ShipHistoryByAreaReadCache(
        bucketCacheFactory = BucketCacheFactory,
        archiveStorageFactory = ArchiveStorageFactory,
        mongoDatabase = null,
        objectMapper = objectMapper,
        bucket = bucket,
        archive = archive,
        archiveGlobal = archiveGlobal
    )
}
