package nl.teqplay.aisengine.reventsengine.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_DIFF
import nl.teqplay.aisengine.revents.publishBatchTimestamp
import nl.teqplay.aisengine.revents.publishPoisonPill
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunStart
import nl.teqplay.aisengine.reventsengine.model.data.AisDiffBatchTimestampMessage
import nl.teqplay.aisengine.reventsengine.model.data.AisDiffMessageMetadata
import nl.teqplay.aisengine.reventsengine.model.data.AisDiffMessageType
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.skeleton.nats.NatsClientConnection
import nl.teqplay.skeleton.nats.NatsProducerStream
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.io.IOException
import java.time.Duration
import java.time.Instant
import java.util.concurrent.CountDownLatch
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.atomic.AtomicReference
import kotlin.concurrent.thread

private val LOG = KotlinLogging.logger {}

/**
 * Service managing publishes into the [diffProducerStream].
 *
 * The message flow is as follows:
 * - messages are queued into the [publishQueue] by calling [queue]
 * - [queue] blocks when [publishQueue] is full
 * - the [publishQueue] will still hold a large amount of messages to keep the messages flowing after unblocking
 */
@Component
class DiffStreamPublishService(
    private val diffProducerStream: NatsProducerStream<AisDiffMessage>,
    natsClientConnection: NatsClientConnection
) {

    private val connection = natsClientConnection.getConnection(diffProducerStream.getConfig())

    companion object {
        private const val PUBLISH_CAPACITY = 100_000

        private const val EVENT_STREAM = "event-stream"
        private const val MAX_EVENT_STREAM_SIZE = 10_000
        private const val MAX_AIS_STREAM_DIFF_SIZE = 100_000
    }

    private val publishQueue = LinkedBlockingQueue<AisDiffMessageType>(PUBLISH_CAPACITY)

    @Volatile
    private var running = false
    private var startThreadTime = Instant.now()

    private val publishingFinished = AtomicBoolean()
    private val publishingLatch = AtomicReference<CountDownLatch>()

    @Scheduled(fixedRate = 5, timeUnit = TimeUnit.SECONDS)
    fun stats() {
        LOG.debug { "Publish queue=${publishQueue.size}" }
    }

    fun startStreamPublisher(interestsRun: InterestsRunStart): (() -> Unit)? {
        if (running || publishQueue.isNotEmpty()) return null

        // return callback which will start the publishing thread
        running = true
        return {
            publishingFinished.set(false)
            publishingLatch.set(CountDownLatch(1))
            thread(name = "revents-publishing") {
                streamPublisherThread(interestsRun.events)
            }
        }
    }

    fun queue(messages: List<AisDiffMessageMetadata>, timestamp: Instant) {
        messages.forEach {
            publishQueue.put(it)
        }
        publishQueue.put(AisDiffBatchTimestampMessage(batchTimestamp = timestamp))
    }

    private fun streamPublisherThread(expectedEvents: Set<ScenarioEvent>) {
        startThreadTime = Instant.now()
        val queue = LinkedBlockingQueue<AisDiffMessageMetadata>(PUBLISH_CAPACITY / 10)

        val pendingDiffMessages = AtomicLong()
        val publishedDiffMessages = AtomicLong()

        val maxMps = 50_000
        var targetMps = 10_000

        var initial = true
        var previousStreamCheck = System.currentTimeMillis()
        var previousMpsCheck = System.currentTimeMillis()
        var increaseMpsCount = 0

        fun decreaseThroughput(stepMps: Int, reason: String) {
            initial = false
            increaseMpsCount = 0
            if (targetMps > 0) {
                targetMps = (targetMps - stepMps).coerceAtLeast(0)
                LOG.info { "Decreasing throughput to $targetMps mps... ($reason)" }
            }
        }

        fun increaseThroughput(stepMps: Int) {
            increaseMpsCount = 0
            if (targetMps < maxMps) {
                targetMps += stepMps
                LOG.info { "Increasing throughput to $targetMps mps..." }
            }
        }

        while (running || publishQueue.isNotEmpty()) {
            try {
                // empty queue up to limit and timeout
                val maxTimeout = Duration.ofSeconds(5).toMillis()
                val startTime = System.currentTimeMillis()
                var batchTimestamp: Instant? = null
                while (queue.remainingCapacity() > 0) {
                    val timeout = startTime + maxTimeout - System.currentTimeMillis()
                    val message = publishQueue.poll(timeout, TimeUnit.MILLISECONDS) ?: break
                    when (message) {
                        is AisDiffBatchTimestampMessage -> {
                            batchTimestamp = message.batchTimestamp
                            break
                        }

                        is AisDiffMessageMetadata -> queue.put(message)
                    }
                }

                // we don't have anything to publish, just continue, we've already waited for the timeout
                if (queue.isEmpty()) {
                    LOG.info { "Waiting for messages to publish..." }
                    continue
                }

                // on an interval (and there are message to publish), check if we should update the message rate
                val streamCheckInterval = if (initial) 10 * 1000 else 15 * 1000
                val stepMps = if (initial) 5_000 else 1_000
                if ((queue.isNotEmpty() || publishQueue.isNotEmpty()) &&
                    System.currentTimeMillis() - previousStreamCheck >= streamCheckInterval
                ) {
                    previousStreamCheck = System.currentTimeMillis()
                    LOG.info { "Using $targetMps mps..." }

                    val eventStreamInfo = connection.jetStreamManagement().getStreamInfo(EVENT_STREAM)
                    val eventStreamMsgCount = eventStreamInfo.streamState.msgCount
                    LOG.info { "Currently having $eventStreamMsgCount events ready" }

                    // too many events, we're going too fast, slowing down immediately
                    if (eventStreamMsgCount > MAX_EVENT_STREAM_SIZE) {
                        decreaseThroughput(stepMps, reason = "too many unconsumed events")
                    } else {
                        val aisStreamDiffInfo = connection.jetStreamManagement().getStreamInfo(AIS_STREAM_DIFF)
                        val msgCount = aisStreamDiffInfo.streamState.msgCount
                        LOG.info { "Currently having $msgCount diff messages ready" }

                        // going too fast, slowing down immediately
                        if (msgCount > MAX_AIS_STREAM_DIFF_SIZE) {
                            decreaseThroughput(stepMps, reason = "consumers can't keep up")
                        }
                        // we're on pace, confirm multiple times that we can actually move faster
                        else if (++increaseMpsCount >= 3) {
                            increaseThroughput(stepMps)
                        }
                    }
                }

                // we've gone too fast for too long, slowing down
                if (targetMps <= 0) {
                    LOG.info { "Waiting for stream to empty..." }
                    TimeUnit.SECONDS.sleep(5)
                    continue
                }

                while (queue.isNotEmpty()) {
                    if (publishedDiffMessages.get() >= targetMps) {
                        publishedDiffMessages.set(0)
                        val mpsTime = System.currentTimeMillis() - previousMpsCheck
                        val sleep = 1000 - mpsTime
                        TimeUnit.MILLISECONDS.sleep(sleep)
                        previousMpsCheck = System.currentTimeMillis()
                    }

                    diffProducerStream.publishAsync(
                        timeout = null,
                        queue = queue,
                        pending = pendingDiffMessages,
                        published = publishedDiffMessages,
                        convert = { it.message },
                        subject = { metadata, converted ->
                            val mmsi = converted.mmsi
                            when {
                                metadata.isServiceVessel -> "ais-stream.diff.$mmsi.service"
                                else -> "ais-stream.diff.$mmsi.interest"
                            }
                        }
                    )
                }

                if (batchTimestamp != null) {
                    publishBatchTimestamp(
                        subject = "ais-stream.diff.batch-timestamp.interest",
                        timestamp = batchTimestamp,
                        expectedAppNames = expectedEvents.map { it.app }.toSet(),
                        properties = diffProducerStream.getConfig(),
                        stream = diffProducerStream
                    )
                }
            } catch (e: IOException) {
                LOG.error(e) { "Something went wrong while publishing" }
            } catch (e: InterruptedException) {
                // ignore
            }
        }

        publishPoisonPill(
            subject = "ais-stream.diff.poison.interest",
            properties = diffProducerStream.getConfig(),
            stream = diffProducerStream
        )

        publishingFinished.set(true)
    }

    fun signalDone(): Boolean {
        if (!publishingFinished.get()) {
            return false
        }
        publishingLatch.get().countDown()
        return true
    }

    fun await() {
        if (running) {
            running = false

            // await latch, but eventually timeout
            publishingLatch.get().await(30, TimeUnit.MINUTES)

            val duration = Duration.between(startThreadTime, Instant.now())
            LOG.info { "Finished running in $duration" }
        }
    }
}
