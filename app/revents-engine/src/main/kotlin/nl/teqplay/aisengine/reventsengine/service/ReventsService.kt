package nl.teqplay.aisengine.reventsengine.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunProgress
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunStart
import nl.teqplay.aisengine.reventsengine.model.data.AisDiffMessageMetadata
import nl.teqplay.aisengine.reventsengine.model.exception.InterestsRunCancelledException
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.service.history.ReventsHistoryService
import nl.teqplay.aisengine.util.floorToLocalDate
import nl.teqplay.aisengine.util.getAisDiff
import nl.teqplay.aisengine.util.isEmpty
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.skeleton.util.pointInPolygon
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicReference
import kotlin.concurrent.thread

private val LOG = KotlinLogging.logger { }

@Service
class ReventsService(
    private val reventsHistoryService: ReventsHistoryService,

    private val diffStreamPublishService: DiffStreamPublishService,

    private val relevantVesselsService: RelevantVesselsService
) {

    private val historyInterval = Duration.ofHours(1)

    private val interestsRunProgress = AtomicReference(
        InterestsRunProgress(
            id = "initial",
            window = TimeWindow(Instant.EPOCH, Instant.EPOCH),
            cursor = Instant.EPOCH
        )
    )

    fun run(
        interestsRun: InterestsRunStart,
    ) {
        val publisherThread = diffStreamPublishService.startStreamPublisher(interestsRun) ?: return
        var publisherThreadStarted = false

        val initialTimeWindow = interestsRun.interests.getFullTimeWindow()
        interestsRunProgress.set(
            InterestsRunProgress(
                id = interestsRun.id,
                window = initialTimeWindow,
                cursor = initialTimeWindow.from
            )
        )

        thread(name = "revents") {
            LOG.info { "Start running revents interest for ${interestsRun.interests.size} ships (service vessels loaded = ${interestsRun.otherVessels != null})" }
            try {
                val fullTimeWindow = getFullTimeWindow(interestsRun)
                    ?: throw InterestsRunCancelledException("Empty time window")

                interestsRunProgress.set(
                    InterestsRunProgress(
                        id = interestsRun.id,
                        window = fullTimeWindow,
                        cursor = fullTimeWindow.from
                    )
                )

                // initialize vessels if statically defined
                val initialVesselMmsis = interestsRun.interests.map { it.mmsi }.toSet()

                // Initialize all vessels
                val allVessels = if (interestsRun.otherVessels != null) {
                    reventsHistoryService.getAllVessels()
                } else emptySet()

                // initialize initial batch of vessels
                reventsHistoryService.init(initialVesselMmsis + allVessels)

                val state = State(
                    buckets = ConcurrentHashMap(),
                    history = ConcurrentHashMap(),
                    lastHistoricPoints = ConcurrentHashMap()
                )

                val dates = fullTimeWindow.getDates()
                dates.forEach { date ->
                    val dateAsInstant = date.atStartOfDay().toInstant(ZoneOffset.UTC)
                    val timeWindow = TimeWindow(dateAsInstant, Duration.ofDays(1))

                    val relevantVessels = relevantVesselsService.getRelevantVessels(interestsRun, timeWindow)
                    reventsHistoryService.init(relevantVessels.keys)

                    val additionalVessels =
                        loadAndPruneBuckets(date, interestsRun, relevantVessels, state)

                    // we've loaded the initial batch of data, start the publisher before pushing the history
                    if (!publisherThreadStarted) {
                        publisherThreadStarted = true
                        publisherThread.invoke()
                    }

                    traverseHistory(timeWindow, state, additionalVessels)
                }
            } catch (e: InterestsRunCancelledException) {
                // print, but no need to act on it, this exception is only used to cancel without crashing
                LOG.info(e) { "Run got cancelled" }
            } catch (e: Exception) {
                // we set the crashed flag here, but make sure to await for the publisher and done flag to be set
                // otherwise we might be shut down prematurely, or not be shut down at all
                LOG.error(e) { "Revents crashed due to an error" }
                interestsRunProgress.getAndUpdate { it.copy(crashed = true) }
            } finally {
                diffStreamPublishService.await()
                interestsRunProgress.getAndUpdate { it.copy(done = true, cursor = it.window.to) }
            }
        }
    }

    private fun List<InterestsRunStart.RelevantShip>.getFullTimeWindow(): TimeWindow = TimeWindow(
        from = minOfOrNull { it.window.from } ?: Instant.EPOCH,
        to = maxOfOrNull { it.window.to } ?: Instant.EPOCH.plusSeconds(1)
    )

    private fun getFullTimeWindow(
        interestsRun: InterestsRunStart,
    ): TimeWindow? {
        val windows = interestsRun.interests
            .map { it.window }
            .toMutableList()

        if (windows.isEmpty()) return null
        return TimeWindow(
            from = windows.minOf { it.from },
            to = windows.maxOf { it.to }
        )
    }

    private fun TimeWindow.getDates(): MutableList<LocalDate> {
        val now = Instant.now()
        val dates = mutableListOf<LocalDate>()
        var from = from.floorToLocalDate()
        do {
            dates.add(from)
            from = from.plusDays(1)
            val fromInstant = from.atStartOfDay().toInstant(ZoneOffset.UTC)
        } while (fromInstant < to && fromInstant.isBefore(now))
        return dates
    }

    data class State(
        val buckets: ConcurrentHashMap<Int, MutableList<AisMessage>>,
        val history: ConcurrentHashMap<Int, MutableList<AisMessage>>,
        val lastHistoricPoints: ConcurrentHashMap<Int, List<AisMessage>>,
    )

    /**
     * Loads and prunes buckets for the [date] and [relevantVessels].
     * @return (optional) service vessels that were included
     */
    private fun loadAndPruneBuckets(
        date: LocalDate,
        interestsRun: InterestsRunStart,
        relevantVessels: Map<Int, List<TimeWindow>>,
        state: State
    ): Set<Int> {
        val requestedServiceHistory = mutableMapOf<Int, List<TimeWindow>>()

        loadBuckets(state.buckets, date, relevantVessels, interestsRun.settings.filterHistory)

        // if service vessels should be resolved, then get all buckets that ships have passed,
        // and add service vessel history to those buckets
        val otherVessels = interestsRun.otherVessels

        if (otherVessels != null) {
            LOG.info { "Loading in vessels for ${otherVessels.areas.size} areas" }
            // get all MMSIs that are near ship's history
            var locations = state.buckets.values.asSequence().flatten().map { it.location }

            if (otherVessels.areas.isNotEmpty()) {
                locations = locations.filter { location ->
                    otherVessels.areas.any { pointInPolygon(it, location) }
                }
            }

            val nearVessels = reventsHistoryService.findShipsNearAreaBuckets(date, locations)

            // Associate with empty list to indicate it's always relevant.
            val allVesselMmsis = nearVessels - relevantVessels.keys
            requestedServiceHistory.putAll(allVesselMmsis.associateWith { emptyList() })

            // initialize ships and load history
            reventsHistoryService.init(requestedServiceHistory.keys)
            loadBuckets(state.buckets, date, requestedServiceHistory, interestsRun.settings.filterHistory)
        }

        // prune history for MMSIs that are not loaded in this new window
        val pruneHistoryMmsis = state.history.keys - state.buckets.keys
        pruneHistoryMmsis.forEach { mmsi ->
            // remove from history and add last historic points to state
            val remainingHistory = state.history.remove(mmsi)
            if (remainingHistory != null) {
                state.lastHistoricPoints[mmsi] = remainingHistory
            }
            reventsHistoryService.pruneHistory(mmsi)
        }

        // populate history
        state.buckets.forEach { (mmsi, bucket) ->
            // insert the last historic points before adding the new history in
            val lastHistoricPoints = state.lastHistoricPoints.remove(mmsi)
            if (lastHistoricPoints != null) {
                state.history.getOrPut(mmsi, ::mutableListOf).addAll(lastHistoricPoints)
            }
            if (bucket.isNotEmpty()) {
                state.history.getOrPut(mmsi, ::mutableListOf).add(bucket.removeFirst())
            }
            if (bucket.isEmpty()) {
                state.buckets.remove(mmsi)
            }
        }

        return requestedServiceHistory.keys
    }

    private fun loadBuckets(
        buckets: ConcurrentHashMap<Int, MutableList<AisMessage>>,
        date: LocalDate,
        vessels: Map<Int, List<TimeWindow>>,
        filterHistory: Scenario.FilterHistory,
    ) {
        var loaded = 0
        LOG.info { "Loading buckets for $date with ${vessels.size} vessels..." }
        vessels.entries.parallelStream().forEach { (mmsi, windows) ->
            buckets[mmsi] = reventsHistoryService.findHistory(date, mmsi, filterHistory)
                .asSequence()
                .filter { isTimestampWithinAnyTimeWindows(it.messageTime, windows) }
                .toMutableList()

            loaded++
            if (loaded % 50 == 0) {
                LOG.info { "Loaded $loaded/${vessels.size} buckets..." }
            }
        }
    }

    private fun isTimestampWithinAnyTimeWindows(
        messageTime: Instant,
        windows: List<TimeWindow>,
    ): Boolean {
        return windows.isEmpty() || windows.any { window -> window.from <= messageTime && messageTime < window.to }
    }

    fun getProgress(
        done: Boolean
    ): InterestsRunProgress {
        if (done) {
            diffStreamPublishService.signalDone()
        }
        return interestsRunProgress.get()
    }

    /**
     * Traverses the [State.history] in the given [timeWindow] and requests more data from [State.buckets] if required.
     */
    private fun traverseHistory(
        timeWindow: TimeWindow,
        state: State,
        additionalServiceVessels: Set<Int>,
    ) {
        var minTimestamp: Instant? = null

        var exceededOnce = false
        while (state.buckets.isNotEmpty() || state.history.isNotEmpty()) {
            if (state.history.isEmpty()) {
                // drain buckets before exiting loop
                drainBuckets(state)
                continue
            }

            // ensure that for all ships we have history for at least the maximum timestamp
            minTimestamp = calculateMinTimestamp(timeWindow, state.history, minTimestamp)
            var targetTimestamp = minTimestamp
                ?.plusMillis(historyInterval.toMillis())
                ?: Instant.MAX

            if (targetTimestamp >= timeWindow.to) {
                if (!exceededOnce) {
                    exceededOnce = true
                    targetTimestamp = timeWindow.to
                } else {
                    // need to gather more data, to ensure we've got everything before pruning
                    drainBuckets(state)
                    break
                }
            }

            gatherHistory(state, targetTimestamp)
            val diffMessages = generateDiffMessages(state.history, targetTimestamp, additionalServiceVessels)
            pruneHistory(state.history, targetTimestamp)
            queueDiffMessages(diffMessages, targetTimestamp)

            // update cursor
            interestsRunProgress.getAndUpdate { it.copy(cursor = targetTimestamp.coerceAtMost(it.window.to)) }
        }
    }

    /**
     * Calculates the minimum timestamp for the [timeWindow] and available [history].
     * Adhering to the [minTimestamp] if set.
     */
    private fun calculateMinTimestamp(
        timeWindow: TimeWindow,
        history: ConcurrentHashMap<Int, MutableList<AisMessage>>,
        minTimestamp: Instant?
    ): Instant? {
        return listOfNotNull(
            history.values.flatten().minOf { it.messageTime },
            timeWindow.from,
            // ensure the minimum timestamp keeps moving, even with outdated ship history
            minTimestamp?.plusMillis(historyInterval.toMillis())
        ).maxOrNull()
    }

    /**
     * Gathers history from the [State.buckets] up to the [targetTimestamp] and stores it in [State.history].
     */
    private fun gatherHistory(
        state: State,
        targetTimestamp: Instant
    ) {
        LOG.info { "Gathering history up to $targetTimestamp..." }
        state.history.forEach { (mmsi, messages) ->
            val bucket = state.buckets[mmsi]
            while (bucket != null && messages.last().messageTime <= targetTimestamp) {
                if (bucket.isNotEmpty()) {
                    messages.add(bucket.removeFirst())
                }
                if (bucket.isEmpty()) {
                    state.buckets.remove(mmsi)
                    break
                }
            }
        }
    }

    /**
     * Generates diff messages for the [history] up to the [targetTimestamp].
     */
    private fun generateDiffMessages(
        history: ConcurrentHashMap<Int, MutableList<AisMessage>>,
        targetTimestamp: Instant,
        additionalServiceVessels: Set<Int>
    ): List<AisDiffMessageMetadata> {
        // generate diff messages for all currently known history
        LOG.info { "Generating diff messages..." }
        return history.values
            .flatMap { messages ->
                messages.zipWithNext()
                    .map { (old, new) -> getAisDiff(old, new) }
                    .filter { it.messageTime <= targetTimestamp && !it.isEmpty() }
                    .map { message ->
                        AisDiffMessageMetadata(
                            message = message,
                            isServiceVessel = message.mmsi in additionalServiceVessels
                        )
                    }
            }
            .sortedWith(compareBy({ it.message.messageTime }, { it.message.oldMessageTime }, { it.message.mmsi }))
    }

    /**
     * Prunes the [history] up to the [targetTimestamp].
     */
    private fun pruneHistory(
        history: ConcurrentHashMap<Int, MutableList<AisMessage>>,
        targetTimestamp: Instant
    ) {
        // throw away all history, except for the last entry, to generate diff messages based on these later
        LOG.info { "Pruning history..." }
        history.forEach { (_, messages) -> // ensure we always keep at least 1 message
            while (messages.size >= 2 &&
                messages[0].messageTime <= targetTimestamp &&
                messages[1].messageTime <= targetTimestamp
            ) {
                messages.removeFirst()
            }
        }
    }

    /**
     * Publishes [diffMessages] to the [diffStreamPublishService].
     */
    private fun queueDiffMessages(
        diffMessages: List<AisDiffMessageMetadata>,
        timestamp: Instant
    ) {
        LOG.info { "Queueing ${diffMessages.size} messages into the stream..." }
        diffStreamPublishService.queue(diffMessages, timestamp)
    }

    /**
     * Drains the [State.buckets] and pushes remaining data to [State.history].
     */
    private fun drainBuckets(
        state: State,
    ) {
        LOG.info { "Draining ${state.buckets.size} buckets..." }
        state.buckets.toList().parallelStream().forEach { (mmsi, bucket) ->
            while (bucket.isNotEmpty()) {
                state.history.getOrPut(mmsi, ::mutableListOf).add(bucket.removeFirst())
            }
        }
        state.buckets.clear()
    }
}
