package nl.teqplay.aisengine.reventsengine.controller

import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunProgress
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunStart
import nl.teqplay.aisengine.reventsengine.service.ReventsService
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/revents")
class ReventsController(
    private val reventsService: ReventsService
) {

    @PostMapping("/run")
    fun run(
        @RequestBody interestsRun: InterestsRunStart
    ) {
        reventsService.run(interestsRun)
    }

    @PostMapping("/progress")
    fun getProgress(
        @RequestParam done: <PERSON>olean
    ): InterestsRunProgress = reventsService.getProgress(done)
}
