package nl.teqplay.aisengine.reventsengine.service

import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunStart
import nl.teqplay.aisengine.util.isOverlapping
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.stereotype.Component

@Component
class RelevantVesselsService {

    /**
     * Returns the interests of the [interestsRun], filtered to be within the [timeWindow],
     * as well as mapped from a MMSI as [Int] to a list of [TimeWindow] in which it's used.
     */
    fun getRelevantVessels(
        interestsRun: InterestsRunStart,
        timeWindow: TimeWindow,
    ): Map<Int, List<TimeWindow>> {
        return interestsRun.interests
            .filter { it.window.isOverlapping(timeWindow) }
            .groupBy({ it.mmsi }, { it.window })
    }
}
