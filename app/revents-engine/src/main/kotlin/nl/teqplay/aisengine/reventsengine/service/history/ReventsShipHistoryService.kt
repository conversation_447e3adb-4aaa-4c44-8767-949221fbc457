package nl.teqplay.aisengine.reventsengine.service.history

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.reventsengine.common.service.external.CsiService
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.shiphistory.client.ShipCurrentClient
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.util.withLimitedRetries
import nl.teqplay.skeleton.model.Location
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentSkipListSet

private val LOG = KotlinLogging.logger { }

@Service
class ReventsShipHistoryService(
    private val shipCurrentClient: ShipCurrentClient,
    private val csiService: CsiService,
    private val shipHistoryFromS3Service: ShipHistoryFromS3Service,
) : ReventsHistoryService {

    private val requestedMmsis = ConcurrentSkipListSet<Int>()
    private val shipTypeMap = ConcurrentHashMap<Int, AisMessage.ShipType>()
    private val transponderPositionMap = ConcurrentHashMap<Int, TransponderPosition>()

    /**
     * Old ship history doesn't contain a [AisMessage.shipType] or [AisMessage.transponderPosition],
     * so we'll need to fetch it separately to use it in [toAisMessage] if it's needed.
     */
    override fun init(mmsis: Set<Int>) {
        val requestMmsis = mmsis
            .filterNot { it in requestedMmsis }
            .toSet()
        if (requestMmsis.isEmpty()) {
            return
        }

        // ensure we don't request these MMSIs after
        requestedMmsis.addAll(requestMmsis)

        requestMmsis.chunked(10_000).forEach { chunkedMmsis ->
            withLimitedRetries {
                val ships = shipCurrentClient.findCurrentByMmsiList(chunkedMmsis)
                ships.forEach { aisCurrentMessage ->
                    val mmsi = aisCurrentMessage.mmsi

                    val shipType = aisCurrentMessage.shipType
                    if (shipType != null) {
                        shipTypeMap[mmsi] = shipType
                    }

                    val transponderPosition = aisCurrentMessage.transponderPosition
                    if (transponderPosition != null) {
                        transponderPositionMap[mmsi] = transponderPosition
                    }
                }
            }
        }
    }

    override fun getAllVessels(): Set<Int> {
        return csiService.getAllVessels()
    }

    override fun findHistory(
        date: LocalDate,
        mmsi: Int,
        filterHistory: Scenario.FilterHistory,
    ): Iterator<AisMessage> {
        return shipHistoryFromS3Service.findHistory(date, mmsi)
            .filter {
                when (filterHistory) {
                    Scenario.FilterHistory.ONLY_REAL_TIME -> !it.historic
                    Scenario.FilterHistory.USE_ALL_DATA -> true
                }
            }
            .map { it.toAisMessage() }
            .iterator()
    }

    override fun pruneHistory(mmsi: Int) {
        shipHistoryFromS3Service.prune(mmsi)
    }

    override fun findShipsNearAreaBuckets(date: LocalDate, locations: Sequence<Location>): Set<Int> {
        LOG.info { "Finding ships near area buckets at $date..." }
        val start = System.currentTimeMillis()

        val window = toTimeWindow(date)
        val mmsis = shipHistoryFromS3Service.findShipsNearAreaBuckets(date, window, locations)

        val elapsed = System.currentTimeMillis() - start
        LOG.info { "Found ${mmsis.size} ships near area buckets at $date in ${elapsed}ms" }
        return mmsis
    }

    private fun AisHistoricMessage.toAisMessage() = AisMessage(
        mmsi = mmsi,
        messageTime = messageTime,
        sources = setOf(source),
        /**
         * message contains [AisHistoricMessage.imo], but will always be `null` for platform data,
         * falling back to CSI's IMO/MMSI in that case
         */
        imo = imo ?: csiService.getImoAtTime(mmsi, messageTime),
        eni = null,
        name = null,
        callSign = null,
        location = location,
        destination = destination,
        eta = eta,
        speedOverGround = speedOverGround,
        courseOverGround = courseOverGround,
        rateOfTurn = null,
        heading = heading,
        draught = draught,
        positionAccuracy = null,
        /**
         * message contains [AisHistoricMessage.transponderPosition], but will always be `null` for platform data,
         * falling back to current transponder position from [transponderPositionMap] in that case
         */
        transponderPosition = transponderPosition ?: transponderPositionMap[mmsi],
        positionSensorType = null,
        aisVersion = null,
        /**
         * message contains [AisHistoricMessage.shipType], but will always be `null` for platform data,
         * falling back to current ship type from [shipTypeMap] in that case
         */
        shipType = shipType ?: shipTypeMap[mmsi],
        status = status,
        specialManeuverStatus = null,
        usingDataTerminal = null
    )
}
