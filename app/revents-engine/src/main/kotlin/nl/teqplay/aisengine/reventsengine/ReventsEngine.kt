package nl.teqplay.aisengine.reventsengine

import nl.teqplay.skeleton.common.logging.EnableIncomingRequestLogging
import nl.teqplay.skeleton.common.logging.EnableOutgoingRequestLogging
import nl.teqplay.skeleton.datasource.DataSourceAutoConfiguration
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication(exclude = [MongoAutoConfiguration::class, DataSourceAutoConfiguration::class])
@EnableIncomingRequestLogging
@EnableOutgoingRequestLogging
@EnableScheduling
@ConfigurationPropertiesScan
class ReventsEngine : SpringBootServletInitializer()

fun main(args: Array<String>) {
    runApplication<ReventsEngine>(*args)
}
