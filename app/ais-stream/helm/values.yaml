image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/ais-stream

resources:
  requests:
    cpu: 0.5
    memory: 4Gi
  limits:
    memory: 4Gi

mongodb:
  enabled: true
  nodeSelector:
    app.teqplay.nl/nodegroup: ais-engine

  tolerations:
    - key: nodegroup
      operator: Equal
      value: ais-engine
      effect: NoSchedule
  persistence:
    size: 10Gi
  auth:
    database: aisstream
    username: aisstream
  resources:
    requests:
      cpu: 0.1
      memory: 4Gi
    limits:
      memory: 4Gi

terminationGracePeriodSeconds: 90

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

nodeSelector:
  app.teqplay.nl/nodegroup: ais-engine

tolerations:
  - key: nodegroup
    operator: Equal
    value: ais-engine
    effect: NoSchedule
