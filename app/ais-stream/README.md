# ais-stream

> Gathers AIS data from various AIS sources, performs deduplication and filtering, and publishes historic and diff
> messages.

- [Published messages](#markdown-header-published-messages)
    - [Diff messages](#markdown-header-diff-messages)
    - [Historic messages](#markdown-header-historic-messages)
- [Manual update endpoints](#markdown-header-manual-update-endpoints)
- [Local testing](#markdown-header-local-testing)
    - [Connecting an AIS source](#markdown-header-connecting-an-ais-source)
    - [Setting up local NATS](#markdown-header-setting-up-local-nats)

---

## Published messages

### Diff messages

For all real-time data, diff messages are sent. A diff message contains all values for all fields, and contains which
fields changed.

These diff messages are used for the implementation of stateless event services. These can rely on detecting a
difference and sending events based on these, for example.

### Historic messages

For all data, both real-time and historic/old, historic messages are published. The combination of these contains all
available data for all vessels, and can be used to reconstruct diff messages.

## Manual update endpoints

**IMPORTANT:** be cautious when using these endpoints, as they will skip all filtering and go straight to applying and
propagating the data.

ais-stream exposes some endpoints that can be used to manually propagate specific values through the system.

The following endpoints exist to update their respective message parts:

- `POST /v1/ais/update/station`
- `POST /v1/ais/update/static`
- `POST /v1/ais/update/position`
- `POST /v1/ais/update/longrange`

When using these endpoints you should fill in most if not all fields for that specific message, since it will fully
overwrite that part of the data for that ship.

## Local testing

This app can be run locally by setting up a local NATS instance to publish messages on, and use an AIS source.

### Connecting an AIS source

Add a source in the properties. Either by adding a source itself, or using a TCP reflector from another ais-stream
instance.

**Using a source itself:**

```yaml
ais-stream:
  sources:
    - name: SPIRE
      hostname: streamingv2.ais.spire.com
      type: tcp
      port: 56784
      parseSubSource: 'true'
      token: "..."
```

**Using a reflector from ais-stream, where the `port` equals the `reflectorPort` of ais-stream:**

```yaml
ais-stream:
  sources:
    - name: AISHUB
      hostname: ais-stream-dev.teqplay-app.svc.cluster.local
      type: tcp
      port: 12345
```

### Setting up local NATS

Only the following command needs to be run, other configuration is set up by the app itself.

```sh
docker run --rm --network host nats -js
```