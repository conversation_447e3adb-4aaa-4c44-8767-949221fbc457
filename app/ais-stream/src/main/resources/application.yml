mongodb:
  host: localhost
  port: 27017
  auth-db: admin
  username:
  password:
  db: aisstream

nats:
  ais-stream:
    enabled: true
    url: nats://localhost:4222
    username: ais-stream
    password:

spring.task.scheduling.pool.size: 4

# Prometheus JVM stats exposing
management:
  metrics:
    enable:
      jvm: true
    tags:
      component: ais-stream
    logging:
      enabled: true
      metric-names: message.count.aisreceiver, message.count.shipstateservice
  endpoints:
    web:
      exposure:
        include:
          - health
          - prometheus

ais-stream:
  ghost-ship-filter-enabled: true
  transponder-filter-enabled: true
  sync-interval: PT5M
  dedupe-window: PT3M
  ignore-change-relevancy-age: PT15M
  restart-check-interval: PT3M
  history:
    rate-limit-interval: PT20S
    processing-interval: PT10S
    delay: PT1M
    buffer-size: 1000000
  state-age: P1D
