package nl.teqplay.aisengine.aisstream.model

import java.time.Instant

/**
 * Data class for the last-known state of a ship.
 */
data class State(
    /**
     * The combined data that was last published. This is used to determine what has changed in the next update.
     */
    var combined: AisMessage? = null,

    /** Last-known AIS position message. */
    override val position: AisPositionWrapper? = null,

    /** Last-known AIS static message. */
    override val static: AisStaticWrapper? = null,

    /** Last-known AIS station message. */
    override val station: AisStationWrapper? = null,

    /** Last-known AIS long range message. */
    override val longrange: AisLongRangeWrapper? = null
) : AisState<State> {
    override fun apply(
        position: AisPositionWrapper?,
        static: AisStaticWrapper?,
        station: AisStationWrapper?,
        longrange: AisLongRangeWrapper?
    ): State = copy(
        position = position,
        static = static,
        station = station,
        longrange = longrange
    )

    /**
     * Get the timestamp by the type of [wrapper].
     * Returning timestamps for either positional, static or station types.
     *
     * If the [wrapper] is of type [AisStaticWrapper] or [AisStationWrapper] we take the
     * timestamp of [static] or [station] respectively.
     *
     * If the [wrapper] is a positional type, such as [AisPositionWrapper] or [AisLongRangeWrapper], we return
     * whichever timestamp is largest between [position] and [longrange].
     */
    fun getTimestampByType(wrapper: AisWrapper<out BaseAisMessage>): Instant? = when (wrapper) {
        is AisPositionWrapper,
        is AisLongRangeWrapper -> listOfNotNull(position?.timestamp, longrange?.timestamp).maxOrNull()

        is AisStaticWrapper -> static?.timestamp
        is AisStationWrapper -> station?.timestamp
    }
}
