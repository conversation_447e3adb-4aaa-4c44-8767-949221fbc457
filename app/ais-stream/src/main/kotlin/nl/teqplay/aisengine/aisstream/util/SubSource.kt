package nl.teqplay.aisengine.aisstream.util

import dk.dma.ais.message.AisMessage

/**
 * Function taking an [AisMessage] and returning the correct subsource by looking for the relevant comment.
 */
fun AisMessage.getSubSource(): String {
    val comment = this.vdm?.commentBlock?.getString("s")

    return when {
        // Terrestrial source or missing source
        comment == "terrestrial" || comment == null -> "t"

        // Satellite source, append satellite number in this case
        comment.startsWith("FM") -> "s" + comment.slice(2..comment.lastIndex)

        // Dynamic source
        comment == "dynamic" -> "d"

        // Unknown source
        else -> "u"
    }
}
