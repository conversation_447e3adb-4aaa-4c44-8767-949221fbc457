package nl.teqplay.aisengine.aisstream.service

import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.Instant
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.ConcurrentSkipListSet

/**
 * Service that keeps track of previously sent messages and can perform de-duplication,
 * based on the provided [Config.dedupeWindow].
 */
@Component
class AisMessageDedupeService(
    private val config: AisStreamProperties,
    private val clock: Clock
) {

    private val uniqueMessages = ConcurrentSkipListSet<Int>()
    private val receptionTimeAndMessage = ConcurrentLinkedQueue<Pair<Instant, Int>>()

    /**
     * Entry point to determine if the [wrapper] contains a duplicate message
     */
    fun isDuplicate(wrapper: AisWrapper<out BaseAisMessage>): Boolean {
        cleanup()

        // if previously added, it's a duplicate
        val hash = getHash(wrapper)
        if (hash in uniqueMessages) {
            return true
        }

        add(hash, wrapper)
        return false
    }

    /**
     * Cleans up the set by looping through all past messages in-order, removing them
     * if they fall outside the [Config.dedupeWindow]
     */
    private fun cleanup() {
        val time = clock.instant()
        val dedupeWindow = time.minusMillis(config.dedupeWindow.toMillis())

        do {
            val data = receptionTimeAndMessage.firstOrNull() ?: break
            val (receptionTime, message) = data
            if (receptionTime.isBefore(dedupeWindow)) {
                uniqueMessages.remove(message)
                receptionTimeAndMessage.remove(data)
            } else {
                break
            }
        } while (true)
    }

    /**
     * Adds [wrapper] to [uniqueMessages] for later de-duplication, being given a [hash] specific to this [wrapper].
     */
    private fun add(hash: Int, wrapper: AisWrapper<out BaseAisMessage>) {
        uniqueMessages.add(hash)
        receptionTimeAndMessage.add(wrapper.receptionTimestamp to hash)
    }

    /**
     * Returns the [BaseAisMessage.hashCode] of the [AisWrapper.message]
     */
    private fun getHash(wrapper: AisWrapper<out BaseAisMessage>) = wrapper.message.hashCode()
}
