package nl.teqplay.aisengine.aisstream.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.aisstream.model.FinnishAisMessage
import nl.teqplay.aisengine.aisstream.model.wrapIt
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties.Source
import nl.teqplay.aisengine.aisstream.util.convert
import org.eclipse.paho.client.mqttv3.IMqttMessageListener
import org.eclipse.paho.client.mqttv3.MqttClient
import org.eclipse.paho.client.mqttv3.MqttConnectOptions
import org.eclipse.paho.client.mqttv3.MqttException
import org.eclipse.paho.client.mqttv3.MqttMessage
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence
import java.time.Clock
import java.time.Instant

private val LOG = KotlinLogging.logger {}
private const val locationTopic = "vessels-v2/+/location"
private const val FINNISH_SOURCE = "DIGITRAFFIC"

/**
 * https://www.digitraffic.fi/en/marine-traffic/#/vessel-locations
 * https://www.digitraffic.fi/en/marine-traffic/ais/
 *
 * Uses the MQTT datasource of digitraffic to fetch the ais locations of vessels
 */
class FinnishAisReceiver(
    private val shipStateService: ShipStateService,
    source: Source,
    private val clock: Clock,
    registry: MeterRegistry
) : AisReceiver(registry, source), IMqttMessageListener {

    private var mqttClient: MqttClient? = null
    private val objectMapper = ObjectMapper().registerKotlinModule()
    private val lastUpdate: Long = 0

    override fun startup() {
        LOG.info { "${source.name}: AIS receiver connecting to ${source.hostname}" }
        val persistence = MemoryPersistence()
        try {
            mqttClient = MqttClient(
                source.hostname,
                "teqplay_" + System.currentTimeMillis(),
                persistence
            )
            val mqttConnectOptions = MqttConnectOptions().apply {
                userName = source.username
                password = source.password?.toCharArray()
            }
            mqttClient?.connect(mqttConnectOptions)
            mqttClient?.subscribe(locationTopic, this)
        } catch (e: MqttException) {
            LOG.error(e) { "Failed to start the receiver" }
        }
    }

    override fun shutdown() {
        try {
            if (mqttClient != null) {
                mqttClient!!.disconnect()
                LOG.info { "${source.name}: AIS receiver disconnected from ${source.hostname}" }
            }
        } catch (e: MqttException) {
            LOG.error(e) { "Failed to stop the receiver" }
        }
    }

    override fun isHealthy(): Boolean {
        return mqttClient?.isConnected == true && super.isHealthy()
    }

    /**
     * Triggered on receiving a MQTT message from the subscribed queue
     * Read message, parse it to a wrapped AisPositionMessage and persist it to the ship state
     */
    override fun messageArrived(topic: String, mqttMessage: MqttMessage) {
        try {
            val vesselLocation = objectMapper.readValue<FinnishAisMessage>(mqttMessage.toString())

            val timestamp = getTimestamp(vesselLocation.time)
            val mmsiFromTopic = getMmsiFromTopic(topic)

            if (mmsiFromTopic == null) {
                // There is no way of processing the finnish AIS when we don't have a MMSI. Drop it.
                LOG.debug { "Could not get MMSI from topic, dropping message. (topic = $topic)" }
                return
            }

            update(timestamp, null)

            shipStateService.update(
                wrapIt(
                    timestamp = timestamp,
                    receptionTimestamp = clock.instant(),
                    source = FINNISH_SOURCE,
                    subSource = null,
                    message = vesselLocation.convert(mmsiFromTopic)
                )
            )
        } catch (e: Exception) {
            LOG.error(e) { "An error occurred while processing a message" }
        }
    }

    /**
     * Helper function to ensure that the timestamp is either valid or substituted by the current time.
     *
     * @param time An epoch timestamp known from the message.
     * @return The parsed timestamp.
     */
    fun getTimestamp(time: Long): Instant {
        val currentTime = Instant.now()
        val messageTime = Instant.ofEpochSecond(time)

        return if (messageTime.isAfter(currentTime)) {
            currentTime
        } else {
            messageTime
        }
    }

    /**
     * Helper function that extracts the MMSI from the topic.
     *
     * @param topic The topic used in the MQTT message.
     * @return The MMSI used in this message, or null when something went wrong.
     */
    fun getMmsiFromTopic(topic: String): Int? {
        val topicParts = topic.split('/')
        val mmsiTopicPart = topicParts.getOrNull(1)

        return mmsiTopicPart?.toIntOrNull()
    }
}
