package nl.teqplay.aisengine.aisstream.service

import com.google.common.net.HostAndPort
import dk.dma.ais.message.AisMessage
import dk.dma.ais.reader.AisReader
import dk.dma.ais.reader.TokenAisTcpReader
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.aisstream.model.wrapIt
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties.Source
import nl.teqplay.aisengine.aisstream.util.convert
import nl.teqplay.aisengine.aisstream.util.getSubSource
import java.time.Clock
import java.time.Instant
import java.util.function.Consumer

private val LOG = KotlinLogging.logger {}

class AisTcpReceiver(
    private val shipStateService: ShipStateService,
    source: Source,
    private val clock: Clock,
    meterRegistry: MeterRegistry
) : AisReceiver(meterRegistry, source), Consumer<AisMessage> {
    private lateinit var reader: TokenAisTcpReader

    private val reflector = source.reflectorPort?.let { AisTcpReflector(it) }

    override fun startup() {
        LOG.info { "${source.name}: AIS receiver connecting to ${source.hostname}:${source.port}" }
        /** Creating new TCP reader everytime to ensure a new thread */
        reader = createTcpReader()
        reader.start()

        // If a reflector port is set, start the tcp data reflector and register it as a packet handler
        if (reflector != null) {
            reflector.start()
            reader.registerPacketHandler(reflector)
        }
    }

    override fun shutdown() {
        reflector?.stop()

        reader.stopReader()
        LOG.info { "${source.name}: AIS receiver disconnected from ${source.hostname}:${source.port}" }
    }

    override fun isHealthy(): Boolean {
        return reader.status == AisReader.Status.CONNECTED && super.isHealthy()
    }

    override fun accept(message: AisMessage) {
        val converted = message.convert()

        if (converted != null) {
            val subSource = if (source.parseSubSource) {
                message.getSubSource()
            } else {
                null
            }

            val timestamp = message.getComment("c")?.let { Instant.ofEpochSecond(it.toLong()) } ?: clock.instant()
            update(timestamp, subSource)

            shipStateService.update(
                wrapIt(
                    timestamp = timestamp,
                    receptionTimestamp = clock.instant(),
                    source = source.name,
                    subSource = subSource,
                    message = converted
                )
            )
        }
    }

    private fun createTcpReader() = TokenAisTcpReader(HostAndPort.fromParts(source.hostname, source.port), source.token).apply {
        LOG.info { "${source.name}: Creating AIS receiver for ${source.hostname}:${source.port} " + (if (source.token == null) "without token" else "with token") }
        name = source.name
        registerHandler(this@AisTcpReceiver)
    }
}

/**
 * Convenience function to access a comment prefix for an AIS message. Gets the comment identified by [id], or `null`
 * if that comment is not present.
 */
private fun AisMessage.getComment(id: String): String? = vdm?.commentBlock?.getString(id)
