package nl.teqplay.aisengine.aisstream.service

import dk.dma.ais.packet.AisPacket
import io.github.oshai.kotlinlogging.KotlinLogging
import java.io.IOException
import java.net.ServerSocket
import java.net.Socket
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.LinkedBlockingQueue
import java.util.function.Consumer
import kotlin.concurrent.thread

private val LOG = KotlinLogging.logger {}

/**
 * Very simple TCP server that listens on the given [port] and forwards all AIS packets to any connected consumers. If
 * a consumer is slow, data will be dropped to ensure that the normal functioning of the system is not impacted.
 */
class AisTcpReflector(private val port: Int) : Consumer<AisPacket> {
    private var socket = ServerSocket(port)

    @Volatile
    private var running: Boolean = false

    private var acceptThread: Thread? = null

    private val connections = CopyOnWriteArrayList<AisTcpReflectorConnection>()

    /**
     * Create a thread that waits for incoming connections. For every connection an [AisTcpReflectorConnection] is
     * created to handle the sending of the actual data.
     */
    fun start() {
        running = true

        if (socket.isClosed) {
            socket = ServerSocket(port)
        }

        acceptThread = thread(
            start = true,
            name = "tcp reflector $port"
        ) {
            LOG.info { "Starting AIS reflector on port $port" }

            while (running) {
                val s = try {
                    socket.accept()
                } catch (e: IOException) {
                    LOG.info { "Stopping AIS reflector on port $port" }
                    return@thread
                }

                val reflector = AisTcpReflectorConnection(s, this)

                connections.add(reflector)

                reflector.start()
            }
        }
    }

    /**
     * Close all client connections and stop processing data.
     */
    fun stop() {
        running = false
        connections.forEach { it.close() }
        socket.close()
    }

    /**
     * Remove a closed connection from the list of connections to which data is sent. Called from within an
     * [AisTcpReflectorConnection] when the connection is closed.
     */
    fun remove(connection: AisTcpReflectorConnection) = synchronized(connections) {
        connections.remove(connection)
    }

    /**
     * Queue a new [AisPacket] for delivery to all connected clients.
     */
    override fun accept(packet: AisPacket) {
        connections.forEach { it.offer(packet) }
    }
}

/**
 * Handler thread for an individual TCP reflector connection.
 */
class AisTcpReflectorConnection(
    private val s: Socket,
    private val parent: AisTcpReflector
) : Thread() {
    private val queue = LinkedBlockingQueue<AisPacket>(500)

    private val outputStream = s.getOutputStream()

    /**
     * Try to send a packet to the client. If the buffer is full, the packet will be dropped.
     */
    fun offer(packet: AisPacket) {
        queue.offer(packet)
    }

    /**
     * Main thread that forwards any data that is offered to the client.
     */
    override fun run() {
        LOG.info { "Connection opened to ${s.remoteSocketAddress}" }

        try {
            while (s.isConnected) {
                val msg = queue.take()
                outputStream.write(msg.toByteArray())
                outputStream.write('\n'.code)
            }
        } catch (e: Exception) {
            parent.remove(this)
        }

        LOG.info { "Connection to ${s.remoteSocketAddress} closed" }
    }

    fun close() {
        s.close()
    }
}
