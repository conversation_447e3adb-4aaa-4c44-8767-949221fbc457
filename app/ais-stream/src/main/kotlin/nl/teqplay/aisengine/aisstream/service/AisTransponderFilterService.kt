package nl.teqplay.aisengine.aisstream.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisTransponderFilterState
import nl.teqplay.aisengine.aisstream.model.State
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.aisstream.util.isValidImo
import org.springframework.stereotype.Service
import java.util.concurrent.ConcurrentHashMap

private typealias Mmsi = Int

@Service
class AisTransponderFilterService {

    companion object {
        const val TRANSPONDER_EXPIRES_AFTER = 20
    }

    private val LOG = KotlinLogging.logger {}
    private val currentTransponders = ConcurrentHashMap<Mmsi, AisTransponderFilterState>()
    private val counters = ConcurrentHashMap<Mmsi, Int>()

    /**
     * Load in the existing transponder state and counters at 0 for the [AisTransponderFilterService] to work properly
     */
    fun onStartup(state: Map<Mmsi, State>) {
        val mappedTransponders = state.mapNotNull { (mmsi, state) ->
            val lastStaticMessage = state.static?.message ?: return@mapNotNull null

            mmsi to createAisTransponderFilterState(lastStaticMessage)
        }.toMap()

        currentTransponders.putAll(mappedTransponders)
        counters.putAll(mappedTransponders.mapValues { 0 })

        LOG.info { "Starting the AisTransponderFilter (transponders loaded: ${currentTransponders.size})" }
    }

    fun initializeTransponderFilterForShip(state: State?) {
        val lastStaticMessage = state?.static?.message
            // We can't initialize the transponder filter for this ship as we never got a static message for this
            ?: return

        val mmsi = lastStaticMessage.mmsi
        val transponderFilterState = createAisTransponderFilterState(lastStaticMessage)

        currentTransponders[mmsi] = transponderFilterState
        counters[mmsi] = 0
        LOG.debug { "Loaded the transponder state for $mmsi" }
    }

    /**
     * Validate if we should allow the provided [AisStaticMessage] using the transponder information from the message
     *
     * @param staticMessage The [AisStaticMessage] we check if the transponder defined in the message is valid
     * @return True when the [AisStaticMessage] is seen as valid
     */
    fun validate(staticMessage: AisStaticMessage): Boolean {
        val currentTransponder = currentTransponders[staticMessage.mmsi]
        val messageTransponder = createAisTransponderFilterState(staticMessage)

        // We don't have any known transponder yet for this mmsi, meaning we can allow this message
        if (currentTransponder == null) {
            return if (isValidNewTransponder(messageTransponder)) {
                LOG.debug { "MMSI is not yet known, allowing static message" }
                onNewTransponder(staticMessage.mmsi, messageTransponder)
            } else {
                false
            }
        }

        return isValidTransponder(staticMessage.mmsi, currentTransponder, messageTransponder)
    }

    /**
     * Add or replace the entry on [currentTransponders] with the [messageTransponder] and the [counters] to 0
     *
     * @param mmsi The MMSI of provided in the [AisStaticMessage]
     * @param messageTransponder The transponder from the provided [AisStaticMessage]
     * @return Always true as we allow the message from the new transponder
     */
    private fun onNewTransponder(mmsi: Mmsi, messageTransponder: AisTransponderFilterState): Boolean {
        currentTransponders[mmsi] = messageTransponder
        counters[mmsi] = 0
        return true
    }

    /**
     * @return true when the [messageTransponder] has a valid imo and transponder position
     */
    private fun isValidNewTransponder(messageTransponder: AisTransponderFilterState): Boolean {
        val validImo = messageTransponder.imo?.let { isValidImo(it.toLong()) } ?: true
        val validTransponderPosition = messageTransponder.position?.hasValidDimensions() ?: true

        return validImo && validTransponderPosition
    }

    /**
     * @param mmsi The MMSI of provided in the [AisStaticMessage]
     * @param currentTransponder The transponder that is currently seen as the one we allow messages from
     * @param messageTransponder The transponder from the provided [AisStaticMessage]
     * @return true when the [messageTransponder] is marked as valid
     */
    private fun isValidTransponder(
        mmsi: Mmsi,
        currentTransponder: AisTransponderFilterState,
        messageTransponder: AisTransponderFilterState,
    ): Boolean {
        if (messageTransponder.position?.hasValidDimensions() == false) {

            // Don't allow the static message from the message transponder when our current transponder has valid dimensions
            if (currentTransponder.position?.hasValidDimensions() != false) {
                LOG.debug { "Invalid ship sizes provided, skipping static message (mmsi: $mmsi)" }

                return false
            }
        }

        if (isDifferentTransponder(currentTransponder, messageTransponder)) {
            val currentCount = counters[mmsi] ?: 0
            val newCount = currentCount + 1

            if (newCount >= TRANSPONDER_EXPIRES_AFTER) {
                LOG.debug { "Old transponder expired, allowing message and replacing the transponder (mmsi: $mmsi)" }

                // The currentTransponder hasn't been used for quite a bit, replace with the message transponder
                return onNewTransponder(mmsi, messageTransponder)
            }

            LOG.debug { "Different transponder detected, skipping static message (mmsi: $mmsi)" }
            counters[mmsi] = newCount

            // Skip the static message
            return false
        } else {
            LOG.trace { "Same transponder was used, allowing static message (mmsi: $mmsi)" }
            counters[mmsi] = 0

            // We are using the same transponder, just allow the message
            return true
        }
    }

    /**
     * Check if the [TransponderPosition.length] is bigger then the [TransponderPosition.width]
     *
     * @return True when the dimensions provided in the [TransponderPosition] are valid
     */
    private fun TransponderPosition.hasValidDimensions(): Boolean {
        return this.length >= this.width
    }

    private fun isDifferentTransponder(currentTransponder: AisTransponderFilterState, messageTransponder: AisTransponderFilterState): Boolean {
        val hasDifferentImo = currentTransponder.imo != messageTransponder.imo
        val hasDifferentPosition = messageTransponder.position != currentTransponder.position
        val hasDifferentAisVersion = messageTransponder.aisVersion != currentTransponder.aisVersion

        return hasDifferentImo || hasDifferentPosition || hasDifferentAisVersion
    }

    private fun createAisTransponderFilterState(staticMessage: AisStaticMessage): AisTransponderFilterState {
        return AisTransponderFilterState(
            imo = staticMessage.imo,
            position = staticMessage.transponderPosition,
            aisVersion = staticMessage.aisVersion
        )
    }
}
