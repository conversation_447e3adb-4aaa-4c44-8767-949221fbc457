package nl.teqplay.aisengine.aisstream.model

data class SingaporeAisMessage(
    val vesselParticulars: VesselParticulars,
    val latitude: Double,
    val longitude: Double,
    val latitudeDegrees: Double,
    val longitudeDegrees: Double,
    val speed: Float,
    val course: Float,
    val heading: Double,
    val timeStamp: String
) {
    data class VesselParticulars(
        val vesselName: String,
        val callSign: String,
        val imoNumber: String,
        val flag: String,
        val mmsiNumber: String,
        val vesselType: String,
        val vesselLength: Double,
        val vesselBreadth: Double,
        val vesselDepth: Double,
        val grossTonnage: Double,
        val netTonnage: Double,
        val deadweight: Double,
        val yearBuilt: Int
    )
}
