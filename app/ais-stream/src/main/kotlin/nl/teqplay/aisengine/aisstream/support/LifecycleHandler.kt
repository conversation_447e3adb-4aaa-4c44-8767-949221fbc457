package nl.teqplay.aisengine.aisstream.support

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.service.ShipStateService
import org.springframework.context.event.ContextClosedEvent
import org.springframework.context.event.ContextRefreshedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

private val LOG = KotlinLogging.logger {}

@Component
class LifecycleHandler(
    private val aisReceiverHandler: AisReceiverHandler,
    private val shipStateService: ShipStateService
) {
    @EventListener(ContextRefreshedEvent::class)
    fun startup(event: ContextRefreshedEvent) {
        LOG.info { "Starting up" }

        shipStateService.startup()
        aisReceiverHandler.startup()
    }
    /**
     * Function that is called to shut down the application orderly, closing the receivers first and then flushing the
     * state. Note that @PreDestroy doesn't work, since we can't rely on MongoDB still being available then!
     */
    @EventListener(ContextClosedEvent::class)
    fun shutdown(event: ContextClosedEvent) {
        LOG.info { "Shutting down" }

        try {
            aisReceiverHandler.shutdown()
        } catch (e: Throwable) {
            LOG.warn(e) { "Error shutting down AIS receivers" }
        }

        try {
            shipStateService.shutdown()
        } catch (e: Throwable) {
            LOG.warn(e) { "Error shutting down ship state service" }
        }

        LOG.info { "Shutdown complete" }
    }
}
