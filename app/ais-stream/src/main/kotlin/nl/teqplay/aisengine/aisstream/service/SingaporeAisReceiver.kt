package nl.teqplay.aisengine.aisstream.service

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.aisstream.model.SingaporeAisMessage
import nl.teqplay.aisengine.aisstream.model.wrapIt
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties.Source
import nl.teqplay.aisengine.aisstream.util.convert
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.web.client.getForObject
import java.time.Clock
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.concurrent.TimeUnit

private val LOG = KotlinLogging.logger {}
private const val MDH_VESSEL_POSITIONS_CALL = "/v1/vessel/positions/snapshot"
private const val TIME_PATTERN = "yyyy-MM-dd HH:mm:ss"
private const val TIME_ZONE = "Asia/Singapore"
private val formatter = DateTimeFormatter.ofPattern(TIME_PATTERN)
private val timeZone = ZoneId.of(TIME_ZONE)

/**
 * https://sg-mdh.mpa.gov.sg/vessel-positions/apis/get/snapshot
 *
 * This receiver retrieves vessel position data from Singapore Maritime Data Hub
 */
class SingaporeAisReceiver(
    private val shipStateService: ShipStateService,
    source: Source,
    private val clock: Clock,
    registry: MeterRegistry,
    restTemplateBuilder: RestTemplateBuilder
) : AisReceiver(registry, source) {
    private var ranSuccessfully: Boolean? = null
    private var enabled = false
    private val restTemplate = restTemplateBuilder
        .rootUri(source.hostname)
        .defaultHeader("apikey", source.token)
        .build()

    override fun startup() {
        LOG.info { "${source.name}: AIS receiver starting requests to ${source.hostname}" }
        enabled = true
    }

    override fun shutdown() {
        LOG.info { "${source.name}: AIS receiver halting requests to ${source.hostname}" }
        enabled = false
    }

    override fun restart() {
        LOG.info { "${source.name}: Restarting Receiver is not possible" }
    }

    override fun isHealthy(): Boolean {
        return ranSuccessfully == true && super.isHealthy()
    }

    /**
     * Poll Singapore Maritime Data Hub every three minutes to get the latest updates to their vessel position dataset.
     * Requests will only fire if the receiver is [enabled].
     */
    @Scheduled(fixedRate = 3, timeUnit = TimeUnit.MINUTES)
    fun retrieveMessages() {
        if (enabled) {
            ranSuccessfully = runCatching { restTemplate.getForObject<Array<SingaporeAisMessage>>(MDH_VESSEL_POSITIONS_CALL).map { messageArrived(it) } }
                .onFailure { e -> LOG.error(e) { "${source.name}: AIS receiver had issues when fetching from Singapore Maritime Data Hub" } }
                .isSuccess
        }
    }

    internal fun messageArrived(message: SingaporeAisMessage) {
        val converted = message.convert()

        if (converted != null) {
            val timestamp = LocalDateTime
                .parse(message.timeStamp, formatter)
                .atZone(timeZone)
                .toInstant()

            val now = clock.instant()
            val maxAge = now.minus(3, ChronoUnit.HOURS)

            // Ignore any updates that happened in the past
            if (timestamp.isAfter(maxAge)) {
                shipStateService.update(
                    wrapIt(
                        timestamp = timestamp,
                        receptionTimestamp = now,
                        source = source.name,
                        subSource = null,
                        message = converted
                    )
                )

                // Only update the counters if the message got processed as others are skipped
                update(timestamp, null)
            }
        }
    }
}
