package nl.teqplay.aisengine.aisstream.controller

import io.swagger.v3.oas.annotations.Operation
import nl.teqplay.aisengine.aisstream.model.AisLongRangeMessage
import nl.teqplay.aisengine.aisstream.model.AisLongRangeWrapper
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.AisStationMessage
import nl.teqplay.aisengine.aisstream.model.AisStationWrapper
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.model.State
import nl.teqplay.aisengine.aisstream.model.TypeNames
import nl.teqplay.aisengine.aisstream.service.ShipStateService
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@RestController
@RequestMapping("/v1/ais/update")
class AisUpdateController(
    private val shipStateService: ShipStateService,
) {

    private val source = "USER"

    companion object {
        private const val IMPORTANT_NOTICE =
            "IMPORTANT: be cautious when using these endpoints, as they will skip all filtering in ais-stream and go straight to applying and propagating the data."
        private const val USE_ALL_FIELDS = "Fill in most if not all of the fields since they will overwrite other data."
    }

    @PostMapping("/${TypeNames.POSITION}")
    @Operation(
        description = "$IMPORTANT_NOTICE Update the position of a ship. $USE_ALL_FIELDS"
    )
    fun updatePosition(@RequestBody position: AisPositionMessage) = update(
        AisPositionWrapper(
            timestamp = Instant.now(),
            source = source,
            subSource = null,
            message = position
        )
    )

    @PostMapping("/${TypeNames.LONGRANGE}")
    @Operation(
        description = "$IMPORTANT_NOTICE Update the long-range position of a ship. $USE_ALL_FIELDS"
    )
    fun updateLongRange(@RequestBody longRange: AisLongRangeMessage) = update(
        AisLongRangeWrapper(
            timestamp = Instant.now(),
            source = source,
            subSource = null,
            message = longRange
        )
    )

    @PostMapping("/${TypeNames.STATIC}")
    @Operation(
        description = "$IMPORTANT_NOTICE Update the static info of a ship. $USE_ALL_FIELDS"
    )
    fun updateStatic(@RequestBody static: AisStaticMessage) = update(
        AisStaticWrapper(
            timestamp = Instant.now(),
            source = source,
            subSource = null,
            message = static
        )
    )

    @PostMapping("/${TypeNames.STATION}")
    @Operation(
        description = "$IMPORTANT_NOTICE Update the station info of a ship. $USE_ALL_FIELDS"
    )
    fun updateStation(@RequestBody station: AisStationMessage) = update(
        AisStationWrapper(
            timestamp = Instant.now(),
            source = source,
            subSource = null,
            message = station
        )
    )

    /**
     * Applies the update from the [wrapper]
     * @return the current(/updated) [State]
     */
    private fun update(wrapper: AisWrapper<out BaseAisMessage>): State? {
        shipStateService.update(wrapper, forceRealTimeUpdate = true)
        return shipStateService.getCurrentState(wrapper.message.mmsi)
    }
}
