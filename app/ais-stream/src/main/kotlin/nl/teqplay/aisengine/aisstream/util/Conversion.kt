package nl.teqplay.aisengine.aisstream.util

import dk.dma.ais.message.AisMessage18
import dk.dma.ais.message.AisMessage27
import dk.dma.ais.message.AisMessage4
import dk.dma.ais.message.AisMessage5
import dk.dma.ais.message.AisPosition
import nl.teqplay.aisengine.aisstream.model.AisLongRangeMessage
import nl.teqplay.aisengine.aisstream.model.AisMessage.AisVersionIndicator
import nl.teqplay.aisengine.aisstream.model.AisMessage.PositionAccuracy
import nl.teqplay.aisengine.aisstream.model.AisMessage.PositionSensorType
import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipStatus
import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipType
import nl.teqplay.aisengine.aisstream.model.AisMessage.SpecialManeuverStatus
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStationMessage
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.model.FinnishAisMessage
import nl.teqplay.aisengine.aisstream.model.SingaporeAisMessage
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.skeleton.model.Location
import java.time.Instant
import kotlin.math.pow
import dk.dma.ais.message.AisMessage as DmaAisMessage
import dk.dma.ais.message.AisPositionMessage as DmaAisPositionMessage

/**
 * Convert an AIS message as received by AisLib to our internal data class format, if we support the given message.
 * Returns `null` for messages that we do not support.
 */
fun DmaAisMessage.convert(): BaseAisMessage? {
    when (this) {
        // Class A position update
        is DmaAisPositionMessage ->
            return AisPositionMessage(
                mmsi = userId,
                location = if (pos.isValid()) Location(pos.latitudeDouble, pos.longitudeDouble) else null,
                heading = if (trueHeading in 0..359) trueHeading else null,
                positionAccuracy = if (posAcc == 1) PositionAccuracy.HIGH else PositionAccuracy.LOW,
                speedOverGround = if (sog != 1023) sog / 10.0f else null,
                courseOverGround = if (cog < 3600) cog / 10.0f else null,
                status = getShipStatus(navStatus),
                rateOfTurn = getRateOfTurn(rot),
                specialManeuverStatus = when (specialManIndicator) {
                    1 -> SpecialManeuverStatus.NOT_ENGAGED_IN_SPECIAL_MANEUVER
                    2 -> SpecialManeuverStatus.ENGAGED_IN_SPECIAL_MANEUVER
                    else -> null
                }
            )

        // Class B position update
        is AisMessage18 ->
            return AisPositionMessage(
                mmsi = userId,
                location = if (pos.isValid()) Location(pos.latitudeDouble, pos.longitudeDouble) else null,
                heading = if (trueHeading in 0..359) trueHeading else null,
                positionAccuracy = if (posAcc == 1) PositionAccuracy.HIGH else PositionAccuracy.LOW,
                speedOverGround = if (sog != 1023) sog / 10.0f else null,
                courseOverGround = if (cog < 3600) cog / 10.0f else null,
            )

        // Static update
        is AisMessage5 ->
            return AisStaticMessage(
                mmsi = userId,
                imo = getImo(this.imo),
                name = this.name?.replace("@", " ")?.trim(),
                callSign = callsign?.replace("@", " ")?.trim(),
                shipType = getShipType(shipType),
                draught = if (this.draught > 0) 0.1f * this.draught else null,
                eta = this.etaDate?.let { Instant.ofEpochMilli(it.time) },
                destination = this.dest?.replace("@", " ")?.trim(),
                transponderPosition = TransponderPosition(dimBow, dimStern, dimPort, dimStarboard),
                positionSensorType = getPositionSensorType(this.posType),
                aisVersion = when (this.version) {
                    0 -> AisVersionIndicator.ITU_R_M1371_1
                    1 -> AisVersionIndicator.ITU_R_M1371_3
                    2 -> AisVersionIndicator.ITU_R_M1371_5
                    else -> AisVersionIndicator.FUTURE
                },
                usingDataTerminal = this.dte == 0 // This field is set to 0 if there is a DTE, 1 otherwise, confusingly
            )

        // Base station
        is AisMessage4 ->
            return AisStationMessage(
                mmsi = userId,
                location = if (pos.isValid()) Location(pos.latitudeDouble, pos.longitudeDouble) else null,
                positionAccuracy = if (posAcc == 1) PositionAccuracy.HIGH else PositionAccuracy.LOW,
                positionSensorType = getPositionSensorType(posType)
            )

        // Long range message
        is AisMessage27 ->
            return AisLongRangeMessage(
                mmsi = userId,
                location = if (pos.isValid()) Location(pos.latitudeDouble, pos.longitudeDouble) else null,
                positionAccuracy = if (posAcc == 1) PositionAccuracy.HIGH else PositionAccuracy.LOW,
                speedOverGround = if (sog != 63) sog.toFloat() else null,
                courseOverGround = if (cog < 3600) cog / 10.0f else null,
            )

        else -> return null
    }
}

fun FinnishAisMessage.convert(mmsi: Int): AisPositionMessage {
    val positionAccuracy = if (this.posAcc) {
        PositionAccuracy.HIGH
    } else {
        PositionAccuracy.LOW
    }

    val shipStatus = getShipStatus(this.navStat)

    return AisPositionMessage(
        mmsi = mmsi,
        location = Location(
            lat = this.lat,
            lon = this.lon
        ),
        heading = this.heading,
        positionAccuracy = positionAccuracy,
        speedOverGround = this.sog,
        courseOverGround = this.cog,
        status = shipStatus,
        rateOfTurn = this.rot
    )
}

fun SingaporeAisMessage.convert(): AisPositionMessage? {
    // Singapore AIS messages are prone to some corruption, so check the validity of the MMSI here
    val mmsi = vesselParticulars.mmsiNumber.toIntOrNull()
        ?.takeIf { vesselParticulars.mmsiNumber.length == 9 }
        ?: return null

    return AisPositionMessage(
        mmsi = mmsi,
        location = Location(latitudeDegrees, longitudeDegrees),
        heading = heading.toInt(),
        positionAccuracy = PositionAccuracy.LOW,
        speedOverGround = speed,
        courseOverGround = course
    )
}

/**
 * Convenience function to check if both the latitude and the longitude of an [AisPosition] are within the expected
 * bounds.
 */
private fun AisPosition.isValid() =
    latitudeDouble <= 90.0 && latitudeDouble >= -90.0 &&
        longitudeDouble <= 180.0 && longitudeDouble >= -180

/**
 * Convert the [status] as encoded in an AIS message to a [ShipStatus] enum value.
 */
private fun getShipStatus(status: Int): ShipStatus = when (status) {
    0 -> ShipStatus.UNDER_WAY_USING_ENGINE
    1 -> ShipStatus.AT_ANCHOR
    2 -> ShipStatus.NOT_UNDER_COMMAND
    3 -> ShipStatus.RESTRICTED_MANEUVERABILITY
    4 -> ShipStatus.CONSTRAINED_BY_HER_DRAUGHT
    5 -> ShipStatus.MOORED
    6 -> ShipStatus.AGROUND
    7 -> ShipStatus.ENGAGED_IN_FISHING
    8 -> ShipStatus.UNDER_WAY_SAILING
    9 -> ShipStatus.RESERVED_FOR_DG
    10 -> ShipStatus.RESERVED_FOR_DG2
    11 -> ShipStatus.TOWING_ASTERN
    12 -> ShipStatus.TOWING_ALONGSIDE
    13 -> ShipStatus.RESERVED_FUTURE
    14 -> ShipStatus.ASI_SART
    else -> ShipStatus.UNDEFINED
}

private fun getPositionSensorType(type: Int): PositionSensorType? = when (type) {
    1 -> PositionSensorType.GPS
    2 -> PositionSensorType.GLONASS
    3 -> PositionSensorType.COMBINED_GPS_GLONASS
    4 -> PositionSensorType.LORAN_C
    5 -> PositionSensorType.CHAYKA
    6 -> PositionSensorType.INTEGRATED_NAVIGATION_SYSTEM
    7 -> PositionSensorType.SURVEYED
    8 -> PositionSensorType.GALILEO
    15 -> PositionSensorType.INTERNAL_GNSS
    else -> null
}

/**
 * Converts the AIS two's complement encoded [rateOfTurn] into a signed integer. Returns `null` if the rate of turn is
 * not specified (this corresponds to an input value of 128).
 */
private fun getRateOfTurn(rateOfTurn: Int): Int? = when {
    rateOfTurn == 128 -> null
    rateOfTurn > 128 -> {
        val converted = -128 + (rateOfTurn - 128) // convert unsigned to two's complement
        -1 * (converted.toDouble() / 4.733).pow(2.0).toInt()
    }
    else -> (rateOfTurn.toDouble() / 4.733).pow(2.0).toInt()
}

/**
 * Converts the AIS [imo] into a string if it is a valid IMO number. The number is deemed valid if it has 7 or more
 * digits. If the [imo] is 0 a -1 is returned (no IMO). For an invalid input, `null` is returned.
 */
private fun getImo(imo: Long?): Int? = when {
    imo == null -> null
    // 9-digit IMO, chop off the trailing two and see if this yields a valid IMO
    imo > 99999999L -> {
        val chopped = imo / 100L
        // If the IMO number is valid we use it ...
        if (isValidImo(chopped)) chopped.toInt()
        // ... otherwise we remove it
        else null
    }
    // IMO is in the proper range, check if it's valid, otherwise reset
    imo > 999999L -> if (isValidImo(imo)) imo.toInt() else -1
    imo == 0L -> -1
    else -> null
}

/**
 * Converts the ship [type] as encoded in an AIS message to a [ShipType] enum value.
 */
private fun getShipType(type: Int): ShipType = when (type) {
    0 -> ShipType.NOT_AVAILABLE

    21 -> ShipType.WING_IN_GROUND_HAZCAT_A
    22 -> ShipType.WING_IN_GROUND_HAZCAT_B
    23 -> ShipType.WING_IN_GROUND_HAZCAT_C
    24 -> ShipType.WING_IN_GROUND_HAZCAT_D
    in 25..29 -> ShipType.WING_IN_GROUND

    30 -> ShipType.FISHING
    31 -> ShipType.TOWING
    32 -> ShipType.TOWING_BIG
    33 -> ShipType.DREDGING_UNDERWATER_OPS
    34 -> ShipType.DIVING_OPS
    35 -> ShipType.MILITARY_OPS
    36 -> ShipType.SAILING
    37 -> ShipType.PLEASURE_CRAFT
    in 38..39 -> ShipType.SPARE

    40 -> ShipType.HIGHSPEED
    41 -> ShipType.HIGHSPEED_HAZCAT_A
    42 -> ShipType.HIGHSPEED_HAZCAT_B
    43 -> ShipType.HIGHSPEED_HAZCAT_C
    44 -> ShipType.HIGHSPEED_HAZCAT_D
    in 45..49 -> ShipType.HIGHSPEED

    in 50..55 -> ShipType.values().get(type - 50)
    58 -> ShipType.MEDICAL_TRANSPORT
    59 -> ShipType.NOT_PARTIES_TO_AN_ARMED_CONFLICT

    60 -> ShipType.PASSENGER
    61 -> ShipType.PASSENGER_HAZCAT_A
    62 -> ShipType.PASSENGER_HAZCAT_B
    63 -> ShipType.PASSENGER_HAZCAT_C
    64 -> ShipType.PASSENGER_HAZCAT_D
    in 65..69 -> ShipType.PASSENGER

    70 -> ShipType.CARGO
    71 -> ShipType.CARGO_HAZCAT_A
    72 -> ShipType.CARGO_HAZCAT_B
    73 -> ShipType.CARGO_HAZCAT_C
    74 -> ShipType.CARGO_HAZCAT_D
    in 75..79 -> ShipType.CARGO

    80 -> ShipType.TANKER
    81 -> ShipType.TANKER_HAZCAT_A
    82 -> ShipType.TANKER_HAZCAT_B
    83 -> ShipType.TANKER_HAZCAT_C
    84 -> ShipType.TANKER_HAZCAT_D
    in 85..89 -> ShipType.TANKER

    90 -> ShipType.OTHER_TYPE
    91 -> ShipType.OTHER_TYPE_HAZCAT_A
    92 -> ShipType.OTHER_TYPE_HAZCAT_B
    93 -> ShipType.OTHER_TYPE_HAZCAT_C
    94 -> ShipType.OTHER_TYPE_HAZCAT_D
    in 95..99 -> ShipType.OTHER_TYPE

    else -> ShipType.UNDEFINED
}
