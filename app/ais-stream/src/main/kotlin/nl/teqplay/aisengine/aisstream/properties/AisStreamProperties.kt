package nl.teqplay.aisengine.aisstream.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "ais-stream")
data class AisStreamProperties(
    val syncInterval: Duration,
    val ghostShipFilterEnabled: Boolean,
    val transponderFilterEnabled: Boolean,
    val sources: List<Source> = emptyList(),
    val history: HistorySettings,

    val dedupeWindow: Duration,
    val ignoreChangeRelevancyAge: Duration,
    /** Interval to check for health status of AIS receivers and restart when unhealthy */
    val restartCheckInterval: Duration,
    /** Ships older than this age will be purged from the state and have to re-pass the ghost ship check */
    val stateAge: Duration,
    /** Minimum interval with no data after which we will prefer a lower prio source. */
    val sourceSwitchInterval: Duration = Duration.ofMinutes(5)
) {
    data class Source(
        val name: String,
        val type: String,
        val hostname: String,
        val port: Int,
        val parseSubSource: Boolean = false,
        val token: String? = null,
        val username: String? = null,
        val password: String? = null,
        val reflectorPort: Int? = null,
        val maxNewestMessageAge: Duration = Duration.ofMinutes(1),
        /** Priority of this source. Lower prio sources are ignored for a ship if we have data from a high prio source. */
        val priority: Int = Int.MAX_VALUE
    )

    data class HistorySettings(
        /** Minimal age between messages of the same mmsi and type that we send on to the history stream. */
        val rateLimitInterval: Duration,

        /** Interval between processing runs of the history service. */
        val processingInterval: Duration,

        /** The delay before sending on history data. The delay is used to gather enough data to be able to rate limit. */
        val delay: Duration,

        /** The amount of data to keep in memory before blocking incoming data, used to apply back pressure. */
        val bufferSize: Int
    )
}
