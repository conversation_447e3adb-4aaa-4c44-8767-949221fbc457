package nl.teqplay.aisengine.aisstream.support

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties
import nl.teqplay.aisengine.aisstream.service.AisReceiver
import nl.teqplay.aisengine.aisstream.service.AisTcpReceiver
import nl.teqplay.aisengine.aisstream.service.FinnishAisReceiver
import nl.teqplay.aisengine.aisstream.service.ShipStateService
import nl.teqplay.aisengine.aisstream.service.SingaporeAisReceiver
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Clock
import kotlin.concurrent.thread

private val LOG = KotlinLogging.logger {}

/**
 * Wrapper class that uses [PostConstruct] and [PreDestroy] hooks to start and stop the configured AIS readers.
 */
@Configuration
class AisReceiverConfiguration(
    private val shipStateService: ShipStateService,
    private val config: AisStreamProperties,
    private val beanFactory: ConfigurableListableBeanFactory,
    private val restTemplateBuilder: RestTemplateBuilder,
    private val registry: MeterRegistry,
    private val clock: Clock
) {

    @Bean
    fun receivers(): List<AisReceiver> = config.sources.mapNotNull {
        when (it.type) {
            "tcp" -> AisTcpReceiver(shipStateService, it, clock, registry)
            "mqtt-digitraffic" -> FinnishAisReceiver(shipStateService, it, clock, registry)
            "https-singapore" -> SingaporeAisReceiver(shipStateService, it, clock, registry, restTemplateBuilder)
            else -> null
        }
    }.onEachIndexed { index, receiver ->
        beanFactory.initializeBean(receiver, "receiver_${receiver.javaClass.name}_$index")
    }
}

@Component
class AisReceiverHandler(
    private val receivers: List<AisReceiver>
) {
    fun startup() {
        LOG.info { "Starting all AIS receivers" }
        receivers.forEach { thread(block = it::startup) }
    }

    fun shutdown() {
        LOG.info { "Stopping all AIS receivers" }
        receivers.forEach { it.shutdown() }
    }

    @Scheduled(fixedRate = 30_000)
    fun stats() {
        receivers.forEach { it.stats() }
    }
}
