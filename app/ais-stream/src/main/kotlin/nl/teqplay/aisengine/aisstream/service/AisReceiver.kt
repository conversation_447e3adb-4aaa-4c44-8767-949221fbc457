package nl.teqplay.aisengine.aisstream.service

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties.Source
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.concurrent.atomic.AtomicLong

private val LOG = KotlinLogging.logger {}

abstract class AisReceiver(
    meterRegistry: MeterRegistry,
    val source: Source
) {

    /**
     * Registry for prometheus statistics, used to streamline metric naming and tag usage
     */
    private val registry = MetricRegistry.of<AisReceiver>(meterRegistry, listOf("source", "sub-source"))

    private val messagesReceivedCounter = AtomicLong()
    private val messagesReceivedPreviousCounter = AtomicLong()
    private val latestMessagesDiff = AtomicLong()

    private var lastStats = Instant.now()

    /** Exponential weighted moving average of the message age */
    private var messageAge = Duration.ofSeconds(0)

    private val ewmaFactor = 0.9

    private var newestAge = Instant.EPOCH

    abstract fun startup()

    abstract fun shutdown()

    open fun restart() {
        LOG.info { "${source.name}: Restarting service" }
        shutdown()
        startup()
    }

    open fun isHealthy(): Boolean {
        return Duration.between(newestAge, Instant.now()) < source.maxNewestMessageAge
    }

    fun getLatestMessagesDiff(): Long = latestMessagesDiff.get()

    fun getNewestMessageAge(): Instant = newestAge

    fun stats() {
        val now = Instant.now()

        // calculate difference between previous and current time
        val timeDiff = Duration.between(lastStats, now)
        lastStats = now

        // calculate difference between previous and current message count
        val currentMessagesCount = messagesReceivedCounter.get()
        val previousMessagesCount = messagesReceivedPreviousCounter.getAndSet(currentMessagesCount)
        val messagesDiff = currentMessagesCount - previousMessagesCount
        latestMessagesDiff.set(messagesDiff)

        val rate = String.format("%.1f", messagesDiff.toDouble() / (timeDiff.toMillis().toDouble() / 1000.0))
        val ageString = String.format(
            "%d:%02d:%02d",
            messageAge.toHours(),
            messageAge.toMinutesPart(),
            messageAge.toSecondsPart()
        )
        val newestAgeStr = ZonedDateTime.ofInstant(newestAge, ZoneOffset.UTC)
        LOG.info { "${source.name}: $rate msgs/s; age $ageString; newest msg received: $newestAgeStr" }
    }

    protected fun update(timestamp: Instant, subSource: String?) {
        if (timestamp.isAfter(newestAge)) {
            newestAge = timestamp
        }
        messageAge = Duration.ofSeconds(
            (ewmaFactor * messageAge.seconds + (1 - ewmaFactor) * (Instant.now().epochSecond - timestamp.epochSecond)).toLong()
        )
        messagesReceivedCounter.incrementAndGet()
        registry.getOrCreateGauge(Metric.MESSAGE_COUNT_INPUT, source.name, subSource).incrementAndGet()
    }
}
