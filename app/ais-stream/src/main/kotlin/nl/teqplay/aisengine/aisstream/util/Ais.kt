package nl.teqplay.aisengine.aisstream.util

import nl.teqplay.aisengine.aisstream.model.AisLongRangeWrapper
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.AisStationWrapper
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.model.TransponderPosition

/**
 * Corrects a provided [AisWrapper], by overwriting values that could be set
 * to the previous value captured in the current [combined] state.
 *
 * Also, overwriting the [historic] state, now we know whether it was received in real-time or not.
 */
fun AisWrapper<out BaseAisMessage>.correct(
    combined: AisMessage?,
    historic: Boolean,
): AisWrapper<out BaseAisMessage> {
    return when (this) {
        is AisPositionWrapper -> copy(
            historic = historic,
            message = message.copy(
                // these should always stay set
                location = message.location ?: combined?.location,
                speedOverGround = message.speedOverGround ?: combined?.speedOverGround,
                courseOverGround = message.courseOverGround ?: combined?.courseOverGround,
                // AIS position update type B doesn't contain status, and we don't want to flip-flop
                status = message.status ?: combined?.status
            )
        )

        is AisLongRangeWrapper -> copy(
            historic = historic,
            message = message.copy(
                // these should always stay set
                location = message.location ?: combined?.location,
                speedOverGround = message.speedOverGround ?: combined?.speedOverGround,
                courseOverGround = message.courseOverGround ?: combined?.courseOverGround
            )
        )

        is AisStaticWrapper -> copy(
            historic = historic,
            message = message.copy(
                imo = when (message.imo) {
                    -1 -> null // no imo, reset
                    null -> combined?.imo // default
                    else -> message.imo
                },

                transponderPosition = combineTransponderPositions(combined?.transponderPosition, message.transponderPosition),
                // these are nullable, default if blank
                callSign = if (message.callSign?.isBlank() == true) combined?.callSign else message.callSign,
                destination = if (message.destination?.isBlank() == true) combined?.destination else message.destination,

                // these should always stay set
                name = if (message.name.isNullOrBlank()) combined?.name else message.name,
                draught = message.draught ?: combined?.draught
            )
        )

        is AisStationWrapper -> copy(
            historic = historic,
        )
    }
}

/**
 * Combines the new and the old [TransponderPosition] to avoid overwriting the existing with 0 or null.
 */
fun combineTransponderPositions(
    oldTransponderPosition: TransponderPosition?,
    newTransponderPosition: TransponderPosition?
): TransponderPosition? {
    // When there is no new transponder position, return the old one
    if (newTransponderPosition == null) return oldTransponderPosition
    // when there is no old transponder position always use the new one
    if (oldTransponderPosition == null) return newTransponderPosition

    // The width is calculated by combining the distanceToStarboard and distanceToPort from the TransponderPosition
    val newWidth = newTransponderPosition.width
    // The length is calculated by combining the distanceToBow and distanceToStern from the TransponderPosition
    val newLength = newTransponderPosition.length

    // overwrite the value with the newly set value if the combination of that value and it's counterpart are higher than 0 impelling that the value is set
    return oldTransponderPosition.copy(
        distanceToStarboard = if (newWidth > 0) newTransponderPosition.distanceToStarboard else oldTransponderPosition.distanceToStarboard,
        distanceToPort = if (newWidth > 0) newTransponderPosition.distanceToPort else oldTransponderPosition.distanceToPort,
        distanceToBow = if (newLength > 0) newTransponderPosition.distanceToBow else oldTransponderPosition.distanceToBow,
        distanceToStern = if (newLength > 0) newTransponderPosition.distanceToStern else oldTransponderPosition.distanceToStern
    )
}
