package nl.teqplay.aisengine.aisstream.service

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.aisstream.datasource.ShipStateDataSource
import nl.teqplay.aisengine.aisstream.datasource.ShipStateWrapper
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.aisstream.model.AisLongRangeMessage
import nl.teqplay.aisengine.aisstream.model.AisLongRangeWrapper
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.AisStationMessage
import nl.teqplay.aisengine.aisstream.model.AisStationWrapper
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.model.PositionalAisMessage
import nl.teqplay.aisengine.aisstream.model.State
import nl.teqplay.aisengine.aisstream.model.isNullOrEmpty
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties
import nl.teqplay.aisengine.aisstream.util.correct
import nl.teqplay.aisengine.aisstream.util.landJitter
import nl.teqplay.aisengine.aisstream.util.validDistance
import nl.teqplay.aisengine.aisstream.util.validHeading
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM
import nl.teqplay.aisengine.service.StateService
import nl.teqplay.aisengine.util.QueueWrapper
import nl.teqplay.aisengine.util.getAisDiff
import nl.teqplay.aisengine.util.getAisMessage
import nl.teqplay.aisengine.util.isEmpty
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.skeleton.util.haversineDistance
import org.apache.commons.lang3.time.DurationUtils
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.util.concurrent.BlockingQueue
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicLong
import kotlin.concurrent.thread
import kotlin.math.abs
import kotlin.reflect.KProperty1

private val LOG = KotlinLogging.logger {}

/**
 * Service that keeps track of the state of all ships and the latest messages received. This service receives updates
 * from all configured [AisReceiver]s and merges them into a common picture. Two threads do further processing based on
 * the common picture. The publisher thread will publish any changed fields for every ship based on
 * [AisStreamProperties.HistorySettings.rateLimitInterval], and the sync thread will update the stored state in the database after [AisStreamProperties.syncInterval]
 * time has elapsed.
 */
@Component
class ShipStateService(
    stateDataSource: ShipStateDataSource,
    meterRegistry: MeterRegistry,
    private val historyProducerStream: NatsProducerStream<AisWrapper<out BaseAisMessage>>,
    private val diffProducerStream: NatsProducerStream<AisDiffMessage>,
    private val aisMessageInvalidationService: AisMessageInvalidationService,
    private val aisMessageDedupeService: AisMessageDedupeService,
    private val ghostShipFilterService: GhostShipFilterService,
    private val aisTransponderFilterService: AisTransponderFilterService,
    private val config: AisStreamProperties,
    private val clock: Clock = Clock.systemDefaultZone()
) : StateService<State, ShipStateWrapper>(stateDataSource, config.syncInterval) {
    companion object {
        private const val QUEUE_CAPACITY = 5000
    }

    /**
     * Queue containing MMSIs for which updated information is available. The AIS receiver threads insert the MMSI for
     * every ship for which an update is received, and the publisher thread removes the MMSI and checks if the update
     * should be published. The queue has a limited size, to throttle message ingestion if we can't publish fast enough.
     */
    private val diffUpdates = LinkedBlockingQueue<QueueWrapper<Int>>(QUEUE_CAPACITY)

    /**
     * Queue containing full updates, excluding historical updates. This queue also has a limited size, to throttle
     * message ingestion if we can't publish fast enough.
     */
    private val fullUpdates = LinkedBlockingQueue<QueueWrapper<AisWrapper<out BaseAisMessage>>>(QUEUE_CAPACITY)

    private val historicDataService = HistoricDataService(clock, config)

    /**
     * Queue containing only historical updates. This queue also has a limited size, to throttle
     * message ingestion if we can't publish fast enough.
     */
    private val historicUpdates = LinkedBlockingQueue<QueueWrapper<AisWrapper<out BaseAisMessage>>>(historicDataService.batchSize)

    /**
     * Registry for promethium statistics, used to streamline metric naming and tag usage
     */
    private val registry = MetricRegistry.of<ShipStateService>(meterRegistry, listOf("nats-subject"))

    private val pendingDiffMessages = registry.createGauge(Metric.MESSAGE_COUNT_PENDING, AtomicLong(), "diff")
    private val publishedDiffMessages = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong(), "diff")
    private val pendingHistoricMessages = registry.createGauge(Metric.MESSAGE_COUNT_PENDING, AtomicLong(), "historic")
    private val publishedHistoricMessages = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong(), "historic")
    private val stateSize = registry.createGauge(Metric.STATE_SIZE, ::size, "state")

    /**
     * Map to be able to access source configs by name
     */
    private val sources = config.sources.associateBy { it.name }

    private var diffPublisherThread: Thread? = null
    private var historyPublisherThread: Thread? = null
    private var fullUpdatePublisherThread: Thread? = null

    init {
        aisTransponderFilterService.onStartup(getStateAsMap())
    }

    /**
     * When the application has fully initialized, this function must be called to start the publisher threads.
     */
    override fun startup() {
        super.startup()
        thread(start = true, name = "AIS diff publisher", block = ::diffPublisher)
        thread(start = true, name = "AIS history publisher", block = ::historyPublisher)
        thread(start = true, name = "AIS full update publisher", block = ::fullUpdatePublisher)
    }

    override fun shutdown() {
        LOG.info { "Shutting down publisher threads..." }
        diffUpdates.put(QueueWrapper.poisonPill())
        historicUpdates.put(QueueWrapper.poisonPill())
        fullUpdates.put(QueueWrapper.poisonPill())
        super.shutdown()
        diffPublisherThread?.join(Duration.ofMinutes(1).toMillis())
        historyPublisherThread?.join(Duration.ofMinutes(1).toMillis())
        fullUpdatePublisherThread?.join(Duration.ofMinutes(1).toMillis())
    }

    /**
     * Entry point for all updates received by the configured [AisReceiver]s.
     */
    override fun update(wrapper: AisWrapper<out BaseAisMessage>) = update(wrapper, forceRealTimeUpdate = false)

    /**
     * Apply update of [wrapper].
     *
     * @param forceRealTimeUpdate forces a real-time/diff update to be sent, also skips filters
     */
    fun update(wrapper: AisWrapper<out BaseAisMessage>, forceRealTimeUpdate: Boolean) {
        // remove AIS messages with invalid MMSIs
        if (aisMessageInvalidationService.isInvalid(wrapper)) {
            if (forceRealTimeUpdate) LOG.info { "Force updated for MMSI ${wrapper.message.mmsi} is ignored by isInvalid check" }
            return
        }

        // remove duplicate AIS messages
        if (aisMessageDedupeService.isDuplicate(wrapper)) {
            if (forceRealTimeUpdate) LOG.info { "Force updated for MMSI ${wrapper.message.mmsi} is ignored by isDuplicate check" }
            return
        }

        val current = getStateByMmsi(wrapper.message.mmsi, aisTransponderFilterService::initializeTransponderFilterForShip)

        // check if position update is valid
        if (!forceRealTimeUpdate && wrapper is AisPositionWrapper && !validUpdate(wrapper)) {
            return
        }

        val isGhostShip = if (!forceRealTimeUpdate && config.ghostShipFilterEnabled && current.isNullOrEmpty()) {
            ghostShipFilterService.isGhostShip(wrapper)
        } else {
            false
        }

        if (isGhostShip) {
            return
        }

        val latestMessage =
            listOfNotNull(current?.position, current?.static, current?.station, current?.longrange)
                .maxByOrNull { it.timestamp }

        if (latestMessage != null && wrapper.source != latestMessage.source) {
            // New point has a different source, let's check the priorities to see if we want to switch
            val interval = Duration.between(latestMessage.timestamp, wrapper.timestamp)

            val oldPriority = sources[latestMessage.source]?.priority ?: Int.MAX_VALUE
            val newPriority = sources[wrapper.source]?.priority ?: Int.MAX_VALUE

            if (!forceRealTimeUpdate && interval < config.sourceSwitchInterval && oldPriority < newPriority) {
                // We're inside the "don't switch" interval, and the old message has higher prio: drop this one
                return
            }
        }

        // Push an update to the state and historic queue if it doesn't belong to a ghost ship
        val oldTimestamp = current?.getTimestampByType(wrapper)

        val diffTimestamp = oldTimestamp?.let { Duration.between(it, wrapper.timestamp) }
        if (forceRealTimeUpdate || diffTimestamp == null || diffTimestamp >= config.history.rateLimitInterval) {
            // we've identified that this message should (most likely) be used, correct data based on known state
            val corrected = wrapper.correct(current?.combined, historic = false)

            // only update state and send a full update, if a relevant change occurred
            if (forceRealTimeUpdate ||
                current == null ||
                hasRelevantChanges(current, corrected) ||
                diffTimestamp == null ||
                diffTimestamp >= config.ignoreChangeRelevancyAge
            ) {
                // Only apply the transponder filter to static messages
                if (!forceRealTimeUpdate && corrected is AisStaticWrapper && config.transponderFilterEnabled) {
                    val isValidStaticMessage = aisTransponderFilterService.validate(corrected.message)

                    // Skip the message if it is not valid
                    if (!isValidStaticMessage) {
                        return
                    }
                }

                // Update the state if the message is live, if the message is older than the state data it will not update
                updateState(corrected) {
                    // Add the MMSI to the queues for the publisher thread
                    diffUpdates.put(QueueWrapper(corrected.message.mmsi))
                }
                fullUpdates.put(QueueWrapper(corrected))
                if (forceRealTimeUpdate) LOG.info { "Force updated for MMSI ${wrapper.message.mmsi} was succesfully passed to update state" }
            }
        } else if (wrapper.timestamp.isBefore(oldTimestamp)) {
            // correct, but don't take known state into account
            val corrected = wrapper.correct(null, historic = true)
            historicUpdates.put(QueueWrapper(corrected))
        }
    }

    /**
     * Returns whether the [wrapper] introduces relevant changes to the [state]
     */
    private fun hasRelevantChanges(
        state: State,
        wrapper: AisWrapper<out BaseAisMessage>
    ): Boolean {
        when (val new = wrapper.message) {
            is AisStationMessage -> {
                return true
            }

            is AisLongRangeMessage -> {
                val old = state.longrange?.message ?: return true
                return hasPositionalChanges(old, new)
            }

            is AisPositionMessage -> {
                val old = state.position?.message ?: return true
                return hasPositionalChanges(old, new) ||
                    // status changes
                    changes(old, new, AisPositionMessage::status) { o, n -> o != n } ||
                    // heading changes any degree
                    changes(old, new, AisPositionMessage::heading) { o, n -> o != n }
            }

            is AisStaticMessage -> {
                val old = state.static?.message ?: return true
                // eta or destination changes
                return changes(old, new, AisStaticMessage::eta) { o, n -> o != n } ||
                    changes(old, new, AisStaticMessage::destination) { o, n -> o != n }
            }
        }
    }

    private inline fun <T, B> changes(
        old: T,
        new: T,
        property: KProperty1<T, B?>,
        test: (B, B) -> Boolean = { _, _ -> true }
    ): Boolean {
        val oldValue = property.get(old)
        val newValue = property.get(new)
        return oldValue != null && newValue != null && test(oldValue, newValue)
    }

    private fun hasPositionalChanges(
        old: PositionalAisMessage,
        new: PositionalAisMessage
    ): Boolean {
        // travels some distance
        return changes(old, new, PositionalAisMessage::location) { o, n -> haversineDistance(o, n) > 50 } ||
            // speed change exceeds one knot
            changes(old, new, PositionalAisMessage::speedOverGround) { o, n -> abs(o - n) > 1 } ||
            // course change with speed
            changes(old, new, PositionalAisMessage::courseOverGround) { o, n ->
                val speed = new.speedOverGround
                abs(o - n) >= 0.25 && speed != null && speed > 1
            }
    }

    override fun initialState(wrapper: AisWrapper<out BaseAisMessage>) = when (wrapper) {
        is AisPositionWrapper -> State(position = wrapper)
        is AisStaticWrapper -> State(static = wrapper)
        is AisStationWrapper -> State(station = wrapper)
        is AisLongRangeWrapper -> State(longrange = wrapper)
    }

    @Scheduled(fixedRate = 30_000)
    fun stats() {
        LOG.info { "Queue sizes: diffUpdates=${diffUpdates.size} fullUpdates=${fullUpdates.size} historicUpdates=${historicUpdates.size}" }
        LOG.info { "Pending messages: diff=${pendingDiffMessages.get()} historic=${pendingHistoricMessages.get()}" }
    }

    fun getCurrentState(mmsi: Int) = getStateByMmsi(mmsi, aisTransponderFilterService::initializeTransponderFilterForShip)

    /**
     * Periodically clean up old ships from the state.
     */
    @Scheduled(fixedRate = 6, timeUnit = TimeUnit.HOURS, initialDelay = 6)
    fun cleanup() {
        val count = expire(Instant.now() - config.stateAge)
        LOG.info { "Expired $count old ships from state" }
    }
    /**
     * Thread that listens on [diffUpdates] and publishes AIS message updates on the queue when required.
     */
    private fun diffPublisher() {
        var running = true

        LOG.info { "Starting update publisher thread" }

        while (running || diffUpdates.isNotEmpty()) {
            try {
                val lastElement = processDiff()
                if (lastElement) {
                    // Set running to false if we processed the poison pill, which is the last element in the queue.
                    // Note that we might call processDiff again if the queue is not empty, so we can't simply assign
                    // the return value to running, because that would overwrite it after we set running to false
                    running = false
                }
            } catch (_: InterruptedException) {
                break
            }
        }

        LOG.info { "Stopping update publisher thread" }
    }

    internal fun processDiff(): Boolean {
        return diffProducerStream.publishAsync(
            queue = diffUpdates,
            pending = pendingDiffMessages,
            published = publishedDiffMessages,
            convert = { wrapper ->
                var diff: AisDiffMessage? = null
                val mmsi = wrapper.data

                if (mmsi != null) {
                    getStateByMmsi(mmsi, aisTransponderFilterService::initializeTransponderFilterForShip) { state ->
                        diff = generateDiffMessage(state)
                        state
                    }
                }

                diff
            },
            subject = { _, diff -> "$AIS_STREAM.diff.${diff.mmsi}" },
            isPoisonPill = { wrapper -> wrapper.lastElement }
        )
    }

    private fun generateDiffMessage(state: State): AisDiffMessage? {
        // If the state was never updated, replace the null value with a non-null default for the comparisons
        val lastMessageTime = state.combined?.messageTime ?: Instant.MIN

        // Combine received AIS message types into the external AisMessage class
        val combined = when {
            state.position?.timestamp?.isAfter(lastMessageTime) == true ||
                state.static?.timestamp?.isAfter(lastMessageTime) == true ||
                state.longrange?.timestamp?.isAfter(lastMessageTime) == true ->
                getAisMessage(state.position, state.static, state.longrange)

            state.station?.timestamp?.isAfter(lastMessageTime) == true ->
                getAisMessage(state.station)

            else -> null
        } ?: return null

        // Only send an update if the configured rate limit interval has been exceeded
        if (Duration.between(lastMessageTime, combined.messageTime) > config.history.rateLimitInterval) {

            // If we can derive a combined message, determine which fields have changed
            val diff = getAisDiff(state.combined, combined)

            if (!diff.isEmpty()) {
                // Something has been updated, record in the state
                state.combined = combined
                addToWriteUpdates(combined.mmsi)
                return diff
            }
        }

        return null
    }

    /**
     * Thread that listens on [historicUpdates] and publishes full historical AIS messages on the queue, after a delay
     * to effectively rate-limit the data.
     */
    private fun historyPublisher() {
        LOG.info { "Starting history publisher thread" }

        var running = true
        val send = LinkedBlockingQueue<QueueWrapper<AisWrapper<out BaseAisMessage>>>()

        while (running || historicUpdates.isNotEmpty()) {
            // Only publish messages when we don't have more than the max unpublished messages
            try {
                val startTime = clock.instant()

                historicDataService.processHistory(
                    poll = {
                        val item = historicUpdates.poll()
                        if (item == null) {
                            null
                        } else {
                            if (item.lastElement) {
                                running = false
                            }
                            item.data
                        }
                    },
                    send = {
                        send.add(QueueWrapper(it))
                    }
                )

                while (send.isNotEmpty()) {
                    processHistoricUpdates(send)
                }

                val runTime = Duration.between(startTime, clock.instant())
                val remaining = config.history.processingInterval - runTime

                LOG.info { "History publisher took $runTime" }

                if (DurationUtils.isPositive(remaining)) {
                    Thread.sleep(remaining.toMillis())
                }
            } catch (e: InterruptedException) {
                break
            }
        }

        LOG.info { "Flushing history data" }

        val count = historicDataService.flush { send.add(QueueWrapper(it)) }

        while (send.isNotEmpty()) {
            processHistoricUpdates(send)
        }

        LOG.info { "Flushed $count historic messages, history publisher thread stopped" }
    }

    /**
     * Thread that listens on [fullUpdates] and publishes full (live) updates to the queue.
     */
    private fun fullUpdatePublisher() {
        LOG.info { "Starting full update publisher thread" }

        var running = true

        while (running || fullUpdates.isNotEmpty()) {
            val lastElement = processFullUpdates()
            if (lastElement) {
                // Set running to false if we processed the poison pill, which is the last element in the queue.
                // Note that we might call processFullUpdates again if the queue is not empty, so we can't simply assign
                // the return value to running, because that would overwrite it after we set running to false
                running = false
            }
        }
        LOG.info { "Stopping full update publisher thread" }
    }

    internal fun processFullUpdates() = processHistoricUpdates(fullUpdates)

    private fun processHistoricUpdates(queue: BlockingQueue<QueueWrapper<AisWrapper<out BaseAisMessage>>>): Boolean {
        return historyProducerStream.publishAsync(
            queue = queue,
            pending = pendingHistoricMessages,
            published = publishedHistoricMessages,
            subject = { _, wrapper -> "$AIS_STREAM.history.${wrapper.message.mmsi}.${wrapper.type}" },
            convert = { wrapper -> wrapper.data },
            isPoisonPill = { wrapper -> wrapper.lastElement }
        )
    }

    /**
     * Check if the update in the given [wrapper] is valid. This is done by comparing it to the previous value, if
     * possible, to weed out "implausible" updates. Historical updates are always considered valid.
     */
    private fun validUpdate(wrapper: AisPositionWrapper): Boolean {
        val old = getStateByMmsi(wrapper.message.mmsi, aisTransponderFilterService::initializeTransponderFilterForShip)?.position

        return if (old != null) {
            if (old.timestamp.isBefore(wrapper.timestamp)) {
                // If this is a regular update we can do filtering based on the previous value
                validHeading(old, wrapper) &&
                    validDistance(old, wrapper) &&
                    wrapper.message.location?.let { landJitter(it) } != true
            } else {
                // Historical updates are only filtered on distance, data may be older/newer than the current update
                validDistance(old, wrapper)
            }
        } else {
            // No previous knowledge, just accept
            true
        }
    }

    override fun createEmptyState(): State = State()
}

private fun State.latestSource(): String? =
    listOfNotNull(position, static, station, longrange).maxByOrNull { it.timestamp }?.source
