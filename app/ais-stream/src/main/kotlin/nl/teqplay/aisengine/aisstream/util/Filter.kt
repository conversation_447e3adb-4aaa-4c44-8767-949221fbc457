package nl.teqplay.aisengine.aisstream.util

import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.util.calculateAngle
import nl.teqplay.skeleton.util.calculateAngleDifference
import nl.teqplay.skeleton.util.haversineDistance
import nl.teqplay.skeleton.util.pointInPolygon
import java.time.Duration

// Constants for the heading filter
private val HEADING_MAX_INTERVAL = Duration.ofMinutes(10)
private const val HEADING_MIN_SPEED = 0.3
private const val HEADING_MAX_DELTA = 90.0

// Constants for the speed filter
private const val KNOTS_TO_METERS_PER_SECOND = 0.514444444
private const val MAX_SPEED = 40.5 * KNOTS_TO_METERS_PER_SECOND
private const val LONG_DISTANCE_THRESHOLD = 200_000
private const val MAX_SPEED_LONG_DISTANCE = 27.0 * KNOTS_TO_METERS_PER_SECOND

// Constants for the land jitter filter
private val NLRTM_RWG_AIS_JITTER_EXCL = Polygon(
    Location(51.94565477300329, 3.972747815884359), Location(51.94150629627594, 3.980402160154333),
    Location(51.95640859813138, 3.995932265448232), Location(51.96626186101782, 3.993871321134983),
    Location(51.94565477300329, 3.972747815884359)
)

/** Specifies the bounding box of the apm2 area to exclude for AIS update because of jitter  */
private val NLRTM_APM2_AIS_JITTER_EXCL = Polygon(
    Location(51.94011239736119, 3.98448472504888), Location(51.9366000649983, 3.992589998776968),
    Location(51.95471004365562, 4.01477904057211), Location(51.9580702463098, 4.007385190525168),
    Location(51.94011239736119, 3.98448472504888)
)

/**
 * Test whether the reported heading in [new] corresponds with the angle between the location in [old] and the location
 * in [new]. The new position is considered invalid when:
 * - the ship is sailing at > 0.3kts,
 * - the time between updates is < 10 minutes, and
 * - the difference between the reported heading and the angle between the locations is over 90°.
 */
fun validHeading(old: AisPositionWrapper, new: AisPositionWrapper): Boolean {
    val deltaT = Duration.between(old.timestamp, new.timestamp)
    val speed = old.message.speedOverGround ?: return true
    val oldLocation = old.message.location ?: return true
    val newLocation = new.message.location ?: return true
    val heading = new.message.heading ?: return true

    if (deltaT < HEADING_MAX_INTERVAL && speed > HEADING_MIN_SPEED) {
        val angle = calculateAngle(oldLocation, newLocation)
        if (calculateAngleDifference(heading.toDouble(), angle) > HEADING_MAX_DELTA) {
            return false
        }
    }

    return true
}

/**
 * Test whether the distance between [old] and [new] is realistic given the timestamps of the messages.
 */
fun validDistance(old: AisPositionWrapper, new: AisPositionWrapper): Boolean {
    val deltaT = Duration.between(old.timestamp, new.timestamp)
    val oldLocation = old.message.location ?: return true
    val newLocation = new.message.location ?: return true
    val distance = haversineDistance(oldLocation, newLocation)

    return when {
        deltaT.seconds == 0L && distance > 0 -> false
        deltaT.seconds == 0L -> true
        else -> {
            val speed = distance / deltaT.seconds

            if (distance > LONG_DISTANCE_THRESHOLD) {
                speed < MAX_SPEED_LONG_DISTANCE
            } else {
                speed < MAX_SPEED
            }
        }
    }
}

/**
 * Test whether the [location] is in a land area where we often encounter AIS jitter.
 */
fun landJitter(location: Location): Boolean {
    return if (location.lat < 51.936599 || location.lat > 51.966098 ||
        location.lon < 3.970571 || location.lon > 4.018774
    ) {
        false
    } else {
        pointInPolygon(NLRTM_RWG_AIS_JITTER_EXCL, location) ||
            pointInPolygon(NLRTM_APM2_AIS_JITTER_EXCL, location)
    }
}

/**
 * Test whether the provided value is a valid IMO number. If the checksum digit is correct, `true` is returned.
 */
fun isValidImo(imo: Long): Boolean {
    val imoString = imo.toString()

    if (imoString.length != 7) {
        return false
    }

    // see if check digit matches, (first*7 + second*6 + ... + sixth*2) % 10 == seventh
    var checksum: Long = 0
    for (i in 0..5) {
        checksum += (imoString.substring(i, i + 1).toInt() * (7 - i)).toLong()
    }
    return (checksum % 10) == imoString.substring(6, 7).toLong()
}
