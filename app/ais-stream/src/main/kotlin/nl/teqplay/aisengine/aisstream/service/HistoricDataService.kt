package nl.teqplay.aisengine.aisstream.service

import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties
import java.time.Clock
import java.time.Duration
import java.time.Instant

/**
 * Class to rate-limit historic (non-live) messages, with the associated state. When older messages arrive there are no
 * real guarantees as to their ordering. Since our aim is to try and have at least a certain amount of time between
 * consecutive messages of the same type for every ship, this service collects historic out-of-order date for a bit of
 * time, and after messages have been in memory for a minute or more, they are sorted and passed on, while filtering out
 * the excessive messages.
 * A complication in this approach is that by default it cannot offer back pressure: since we need to keep the data
 * around for about a minute, we will end up reading data without throttling into memory! The solution to this problem
 * is that when the caller detects that we are taking a long time sending the data (because the receiver might throttle
 * us), it can signal this to this service, and it will only consume as many messages as it has been able to send the
 * last time.
 */
class HistoricDataService(
    /** External clock, used to control time during unit tests. */
    private val clock: Clock,
    private val config: AisStreamProperties
) {
    /**
     * Based on the minimal age and the time between runs, this is the amount of data that needs to be stored
     * between each run.
     */
    val batchSize = with(config.history) {
        (bufferSize / (delay.seconds / processingInterval.seconds)).toInt()
    }

    private var kept = mutableListOf<AisWrapper<out BaseAisMessage>>()

    /**
     * Consume data by calling [poll] to get it, and send it rate-limited by calling [send], possibly at a later
     * invocation of this method. The [poll] function should not block, if no data is available it should return `null`
     * instead.
     */
    fun processHistory(
        poll: () -> AisWrapper<out BaseAisMessage>?,
        send: (AisWrapper<out BaseAisMessage>) -> Unit
    ) {
        val processing = mutableListOf<AisWrapper<out BaseAisMessage>>()
        val toKeep = mutableListOf<AisWrapper<out BaseAisMessage>>()

        // Split the kept messages into those that are old enough to be rate-limited reliably, and those that
        // need to be kept for another iteration.  This mechanism allows us to wait for a bit to collect all (or
        // at least most of) the out-of-order messages and rate-limit them.
        kept.forEach { msg ->
            if (Duration.between(msg.receptionTimestamp, clock.instant()) > config.history.delay) {
                processing.add(msg)
            } else {
                toKeep.add(msg)
            }
        }
        kept = toKeep

        limitAndSend(processing, send)

        while (kept.size < config.history.bufferSize) {
            kept.add(poll() ?: break)
        }
    }

    /**
     * Flush the kept data immediately using the given [send] function.
     */
    fun flush(send: (AisWrapper<out BaseAisMessage>) -> Unit): Int {
        val size = limitAndSend(kept, send)
        kept.clear()
        return size
    }

    /**
     * Group the messages in [msgs] by mmsi, and send one message per mmsi and per type per [historyInterval] using the
     * given [send] function.
     */
    private fun limitAndSend(
        msgs: List<AisWrapper<out BaseAisMessage>>,
        send: (AisWrapper<out BaseAisMessage>) -> Unit
    ): Int {
        var size = 0

        msgs.groupBy { it.message.mmsi }
            .mapValues { elem -> elem.value.sortedBy { it.timestamp } }
            .forEach { (_, msgs) ->
                val lastSents = mutableMapOf<String, Instant>()
                size += msgs.size

                msgs.forEach { msg ->
                    val lastSent = lastSents.getOrDefault(msg.type, Instant.MIN)
                    if (Duration.between(lastSent, msg.timestamp) > config.history.rateLimitInterval) {
                        lastSents[msg.type] = msg.timestamp
                        send(msg)
                    }
                }
            }

        return size
    }
}
