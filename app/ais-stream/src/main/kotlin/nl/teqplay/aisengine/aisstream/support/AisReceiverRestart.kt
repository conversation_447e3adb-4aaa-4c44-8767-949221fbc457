package nl.teqplay.aisengine.aisstream.support

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.service.AisReceiver
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.Scheduled

private val LOG = KotlinLogging.logger {}

@Configuration
class AisReceiverRestart(
    private val receivers: List<AisReceiver>
) {

    @Scheduled(
        initialDelayString = "\${ais-stream.restart-check-interval}",
        fixedDelayString = "\${ais-stream.restart-check-interval}"
    )
    fun restartReceiverWhenUnhealthy() {
        receivers.forEach { receiver ->
            if (!receiver.isHealthy()) {
                LOG.warn { "${receiver.source.name}: Status unhealthy" }
                receiver.restart()
            }
        }
    }
}
