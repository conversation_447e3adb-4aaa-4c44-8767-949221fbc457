package nl.teqplay.aisengine.aisstream.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.aisstream.model.State
import nl.teqplay.aisengine.datasource.StateDataSource
import nl.teqplay.aisengine.datasource.StateWrapper
import org.springframework.stereotype.Component
import java.time.Instant

@Component
class ShipStateDataSource(
    mongoDatabase: MongoDatabase
) : StateDataSource<State, ShipStateWrapper>(mongoDatabase, ShipStateWrapper::class.java) {
    override fun toStateWrapper(entry: Map.Entry<Int, State>): ShipStateWrapper {
        return ShipStateWrapper(
            _id = entry.key,
            state = entry.value,
            updatedAt = Instant.now()
        )
    }
}

data class ShipStateWrapper(
    override val _id: Int,
    override val state: State,
    override val updatedAt: Instant = Instant.now()
) : StateWrapper<State>
