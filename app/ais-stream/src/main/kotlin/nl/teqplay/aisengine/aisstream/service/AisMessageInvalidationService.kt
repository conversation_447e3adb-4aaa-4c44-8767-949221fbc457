package nl.teqplay.aisengine.aisstream.service

import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import org.springframework.stereotype.Component

/**
 * Service that invalidates messages based on their contents, like usage of an invalid MMSI.
 */
@Component
class AisMessageInvalidationService {

    // only 9-digit MMSIs are valid
    private val validMmsiRange = 100000000..999999999

    /**
     * Entry point to determine if the [wrapper] contains invalid data
     */
    fun isInvalid(wrapper: AisWrapper<out BaseAisMessage>): Boolean {
        return wrapper.message.mmsi !in validMmsiRange
    }
}
