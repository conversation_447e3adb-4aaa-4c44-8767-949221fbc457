package nl.teqplay.aisengine.aisstream.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStationWrapper
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.model.GhostShipFilterState
import nl.teqplay.skeleton.util.haversineDistance
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

private val LOG = KotlinLogging.logger {}

private const val MAX_DISTANCE_DIFF_METERS = 5000
private const val MAX_TIME_DIFF_SECONDS = 1800
private const val MIN_VALID_COUNTER = 10
private const val PURGE_THRESHOLD = MIN_VALID_COUNTER * MAX_TIME_DIFF_SECONDS

/**
 * Service that uses a state and filtering to determine whether a given AIS message belongs to a ghost ship.
 */
@Component
class GhostShipFilterService {

    /**
     * The quarantine uses a ship's MMSI as key. The values are objects consisting of the most recent update for the
     * ship, along with the count of valid updates received for the ship. Updates are valid when the time interval
     * between them doesn't exceed [MAX_TIME_DIFF_SECONDS] and if the distance between their locations doesn't exceed
     * [MAX_DISTANCE_DIFF_METERS].
     */
    private val quarantine = ConcurrentHashMap<Int, GhostShipFilterState>()

    /**
     * Function to check whether a given update belongs to a ghost ship.
     */
    fun isGhostShip(update: AisWrapper<out BaseAisMessage>): Boolean {
        val mmsi = update.message.mmsi
        val previous = quarantine.getOrPut(mmsi) { GhostShipFilterState(update, 1) }

        return when (update) {
            is AisPositionWrapper -> validateUpdate(previous, update) { timeDiff, previousUpdate ->
                // Only allow the quarantine to be updated if both time and distance diff checks pass
                val distanceDiff = calculateDistanceDiff(previousUpdate, update)
                if (distanceDiff == null) {
                    // One of the locations was invalid/null, not updating counter
                    quarantine[mmsi] = previous.copy(update = update)
                    true
                } else if (distanceDiff < MAX_DISTANCE_DIFF_METERS && timeDiff < MAX_TIME_DIFF_SECONDS) {
                    increaseValidUpdateCounter(previous, update)
                } else {
                    // If an update failed the threshold checks, reset the counter and start over
                    quarantine[mmsi] = GhostShipFilterState(update, 1)
                    true
                }
            }

            is AisStationWrapper -> validateUpdate(previous, update)

            else -> {
                // if not caught by previous statements, then the ship is always regarded as a ghost
                true
            }
        }
    }

    private inline fun <reified T : AisWrapper<out BaseAisMessage>> validateUpdate(
        previous: GhostShipFilterState,
        update: T,
        action: (Long, T) -> Boolean = { _, _ -> increaseValidUpdateCounter(previous, update) }
    ): Boolean {
        val mmsi = update.message.mmsi

        if (previous.update !is T) {
            // quarantined messages may only be of one message type
            // only position and base station updates are considered,
            // a ship may not send mixed messages of these two types
            quarantine.remove(mmsi)
            return true
        }

        // Only care about the update if it occurred after the most recent update
        val timeDiff = Duration.between(previous.update.timestamp, update.timestamp).seconds
        if (timeDiff > 0) {
            return action(timeDiff, previous.update)
        }
        return true
    }

    private fun increaseValidUpdateCounter(
        last: GhostShipFilterState,
        update: AisWrapper<out BaseAisMessage>
    ): Boolean {
        val mmsi = update.message.mmsi

        // If an update passed the threshold checks, increment the counter and replace the previous message
        val newValidUpdateCounter = last.validUpdateCounter + 1

        // Check the amount of valid updates, remove the mmsi from quarantine and return false if there are enough
        if (newValidUpdateCounter > MIN_VALID_COUNTER) {
            quarantine.remove(mmsi)
            LOG.debug { "Removing mmsi $mmsi from quarantine" }
            return false
        }

        quarantine[mmsi] = last.copy(
            update = update,
            validUpdateCounter = newValidUpdateCounter
        )
        return true
    }

    /**
     * Calculate the distance in meters between two updates. If the update doesn't contain a location, return null.
     */
    private fun calculateDistanceDiff(
        last: AisWrapper<AisPositionMessage>,
        update: AisWrapper<AisPositionMessage>
    ): Double? {
        val oldPosition = last.message.location ?: return null
        val newPosition = update.message.location ?: return null

        return haversineDistance(oldPosition, newPosition)
    }

    @Scheduled(fixedRate = 60_000)
    private fun stats() {
        LOG.debug { "Ghost ship filter quarantine size: ${quarantine.size} ships currently in quarantine" }
    }

    @Scheduled(fixedRate = 10, timeUnit = TimeUnit.MINUTES)
    private fun purge() {
        val now = Instant.now()

        val purgingMmsis = quarantine.filterValues { state ->
            Duration.between(state.update.timestamp, now).seconds > PURGE_THRESHOLD
        }.keys

        if (purgingMmsis.isNotEmpty()) {
            LOG.info { "Purging ${purgingMmsis.size} ships out of ${quarantine.size} from the ghost ship filter quarantine" }
            quarantine.keys.removeAll(purgingMmsis)
        }
    }
}
