package nl.teqplay.aisengine.aisstream

import nl.teqplay.aisengine.aisstream.service.AisReceiver
import nl.teqplay.skeleton.actuator.degraded
import org.springframework.boot.actuate.health.AbstractHealthIndicator
import org.springframework.boot.actuate.health.Health
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Instant
import java.time.temporal.ChronoUnit

@Configuration
class HealthConfiguration {

    @Bean
    fun aisHealthIndicator(receivers: List<AisReceiver>): HealthIndicator {
        return object : AbstractHealthIndicator() {
            override fun doHealthCheck(builder: Health.Builder) {
                var degraded = false
                receivers.forEach { receiver ->
                    val healthy = receiver.isHealthy()
                    val receiverStatus = if (healthy) "UP" else "DOWN"
                    builder.withDetail(receiver.source.name, receiverStatus)
                    if (!healthy) {
                        degraded = true
                    }
                }

                if (degraded) {
                    builder.degraded()
                } else {
                    builder.up()
                }
            }
        }
    }

    @Bean
    fun streamHealthIndicator(receivers: List<AisReceiver>): HealthIndicator {
        return object : AbstractHealthIndicator() {
            override fun doHealthCheck(builder: Health.Builder) {

                val fiveMinutesAgo = Instant.now().minus(5, ChronoUnit.MINUTES)

                val unhealthy = receivers.all { receiver ->
                    val messagesDiff = receiver.getLatestMessagesDiff()
                    val newestAge = receiver.getNewestMessageAge()

                    builder.withDetail(receiver.source.name, "messagesDiff: $messagesDiff, newestAge: $newestAge")

                    messagesDiff == 0L && newestAge.isBefore(fiveMinutesAgo)
                }

                // all receivers are not receiving any messages anymore, assume something is going wrong, and
                // we can only try restarting to fix the issue
                if (unhealthy) {
                    builder.down()
                } else {
                    builder.up()
                }
            }
        }
    }
}
