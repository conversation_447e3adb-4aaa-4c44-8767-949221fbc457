package dk.dma.ais.reader

import com.google.common.net.HostAndPort
import java.nio.charset.StandardCharsets

/**
 * Extension of [AisTcpReader] that authenticates using a token. The class has to be in the dk.dma.ais.reader package to
 * be able to access [AisTcpReader.addHostPort] which is private. Connects to the given [host], and if [token] is not
 * null or blank, sends `A|T|<token>` upon connecting to authenticate. This can be used to connect to the Spire raw
 * AIS stream.
 */
class TokenAisTcpReader(
    host: HostAndPort,
    private val token: String? = null
) : AisTcpReader() {
    init {
        addHostPort(host)
    }

    override fun connect() {
        super.connect()

        if (!token.isNullOrBlank()) {
            clientSocket.get().getOutputStream().write(("A|T|$token\n").toByteArray(StandardCharsets.US_ASCII))
        }
    }
}
