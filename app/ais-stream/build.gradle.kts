buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":lib:ship-state"))
    implementation(project(":api:nats-stream-ais-publish"))
    implementation("nl.teqplay.skeleton:metrics:$skeletonVersion")

    implementation("nl.teqplay.skeleton:datasource2:$skeletonVersion")
    implementation("nl.teqplay.skeleton:actuator:$skeletonVersion")
    implementation("nl.teqplay.skeleton:util-location2:$skeletonVersion")
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:$springdocVersion")

    implementation("dk.dma.ais.lib:ais-lib-utils:$aisLibVersion") {
        exclude(group = "org.slf4j", module = "slf4j-log4j12")
        exclude(group = "log4j", module = "log4j")
    }
    implementation("dk.dma.ais.lib:ais-lib-communication:$aisLibVersion")
    implementation("dk.dma.ais.lib:ais-lib-messages:$aisLibVersion")

    implementation("org.eclipse.paho:org.eclipse.paho.client.mqttv3:$eclipseMqttvVersion")

    testImplementation("nl.teqplay.skeleton:nats-test:$skeletonVersion")
    testImplementation("org.awaitility:awaitility:$awaitilityVersion")
    testImplementation("org.awaitility:awaitility-kotlin:$awaitilityVersion")
}

testing {
    suites {
        named("integrationTest", JvmTestSuite::class).configure {
            dependencies {
                implementation("org.testcontainers:mongodb:$testContainersVersion")
                implementation("io.nats:jnats:$jnatsVersion")
                implementation(project(":lib:common"))
                implementation("com.fasterxml.jackson.core:jackson-databind:$jacksonVersion")
                implementation("com.fasterxml.jackson.core:jackson-annotations:$jacksonVersion")
                implementation("com.fasterxml.jackson.module:jackson-module-kotlin:$jacksonVersion")
            }

            targets.configureEach {
                testTask.configure {
                    testLogging.showStandardStreams = true
                }
            }
        }
    }
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
