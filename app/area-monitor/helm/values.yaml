global:
  storageClass: "gp2"
  namespaceOverride: "teqplay-app"

replicaCount: ~
deployStrategy: RollingUpdate

statefulSet:
  enabled: true
  podManagementPolicy: Parallel

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/area-monitor

resources:
  requests:
    cpu: 0.3
    memory: 4Gi
  limits:
    memory: 4Gi

mongodb:
  enabled: true
  nodeSelector:
    app.teqplay.nl/nodegroup: ais-engine

  tolerations:
    - key: nodegroup
      operator: Equal
      value: ais-engine
      effect: NoSchedule
  persistence:
    size: 2Gi
  auth:
    database: area-monitor
    username: area-monitor
  resources:
    requests:
      cpu: 0.1
      memory: 2Gi
    limits:
      memory: 2Gi

terminationGracePeriodSeconds: 90

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

nodeSelector:
  app.teqplay.nl/nodegroup: ais-engine

tolerations:
  - key: nodegroup
    operator: Equal
    value: ais-engine
    effect: NoSchedule
