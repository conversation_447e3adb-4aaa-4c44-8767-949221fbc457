# area-monitor

> A service for monitoring a **large amount of areas** of various types like ports, berths, etc.

- [Envisioned horizontal scaling of areas](./docs/scalable.md)

## Features

- high performance
- API to perform updates/deletes
- "zero trust" area updates, you can only specify area identifiers, not concrete area models, those are fetched from the
  appropriate resource

## Responsibilities

**area-monitor's:**

- Offer an API to update/delete areas within a resource.
- Offer an API to inspect all areas within a resource.
- Ensure data consistency, replayability and reliability.
- Ensure continuous monitoring of the available areas.
- Ensure all areas to be fetched from the resource itself, no manual inputs/edits.
- Support a **large** amount of areas, and ensure efficiency between different instances.

**other's:**

- Adding/updating/deleting areas (i.e. keeping the area-monitor up-to-date).
    - The area-monitor cannot know when and if an area is added/updated/deleted. You as the provider/resource of the
      areas should signal the area-monitor when an add/update/delete should happen.
- Validating areas.
    - The area-monitor does not validate or adapt areas more than necessary. Editing areas should be done in the
      required resource, NOT in the area-monitor.
- Versioning of areas when making edits / validity of generated events after updates.
    - The area-monitor only stores a `since` timestamp, of the latest version, that indicates since when this specific
      representation of an area is reliably generating events. There are no guarantees regarding event consistency or
      versioning for area representations that might be added/updated prior to the `since`. One should assume all
      events, for this area, prior to the `since` may be invalid and/or require regeneration.

## Endpoints

### Updating/deleting areas within a resource

| Endpoint                     | Description                             |
|------------------------------|-----------------------------------------|
| `POST /v1/area/{resource}`   | Fetch and update areas for a `resource` |
| `DELETE /v1/area/{resource}` | Delete areas from a `resource`          |

**Example input**

```json
{
  "PORT": [
    "NLRTM",
    "NLAMS"
  ],
  "BERTH": [
    ...
  ],
  "PILOT_BOARDING_PLACE": [
    ...
  ]
}
```

**Limitations**

- Empty lists or an empty root object is not allowed, you must specify which identifiers should be updated/deleted.
- The amount of total identifiers that may be specified is limited.

### Inspecting contained areas within a resource

| Endpoint                              | Description                               |
|---------------------------------------|-------------------------------------------|
| `GET /v1/area/{resource}`             | Get all area types and their identifiers. |
| `GET /v1/area/{resource}/{type}`      | Get all identifiers for this area type.   |
| `GET /v1/area/{resource}/{type}/{id}` | Get one specific area (if it exists).     |

## Known limitations

- When updating/deleting areas, the application will temporarily need to stop consuming, to ensure that the
  area's `since` is a valid measure and ensures correct event generation for all events generated since this timestamp.
  This means that event generation will temporarily be stopped to perform the update/delete, please take into account
  that any event generated may be delayed by about 30 seconds when this action is being performed.
- When updating areas, some events may not be generated depending on the resizing. If the new inner area is smaller
  than (or equal to) the old inner area, no issues will occur. If the new inner area is larger than the old inner area,
  you will miss `AreaStartEvent` for all ships already laying inside the new inner area but not yet inside the old inner
  area when the update is performed.
- There is currently no way to be "notified" about when the request for updating/deleting areas has been completed,
  except for manually inspecting if the `since` for an updated area has been updated or a deleted area can't be found
  anymore.
- When updating areas, only areas that contain an inner area/`polygon` are considered, if it doesn't it will not be
  added, and will not be shown in the content endpoints for a given resource.
- The outer area/`outerPolygon`, if set, must always be larger (in size) than the inner area/`polygon`, so the inner
  area must be contained inside the outer area (this will not throw errors, but it may lead into flip-floppy
  events).

## Implementation

### Area event generation

There are two types of events that will be generated for a specific area (per ship):

| Event            | Trigger condition                                                                                                                                                                                |
|------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `AreaStartEvent` | `AreaStartEvent` is not triggered for this area already, and ship moves from outside the inner area into the inner area.                                                                         |
| `AreaEndEvent`   | `AreaStartEvent` for this area is triggered before (ship is already inside this area), and moves from the outer area out of the outer area (or the inner area if the outer area is not defined). |

These trigger conditions are set to ensure an `AreaEndEvent` will only be triggered if an `AreaStartEvent` was triggered
before. Only if the ship **moves into** (or out of when already inside) the polygon will it trigger an event.

This has the following limitation: if the ship is inside the inner area already, before this area is being monitored,
no `AreaStartEvent` nor an `AreaEndEvent` will be triggered. Since the ship already moved into this inner area prior to
the monitoring being enabled. This may lead into "inconsistent" behavior though when the ship would exit the inner area
and enter it again, since it will not have generated an `AreaStartEvent` before and will generate one now.

_Legend:_

- 🟢 = inner area
- 🔴 = outer area

![](./images/NLRTM_area.png)

### Updating areas and the impact on events

Updating areas will have an impact on event consistency between area updates.

Let's imagine the following scenario: a ship is moving into Rotterdam, from 1 to 3. We currently have specified an inner
area which we'd like to enlarge. When updating an area, the area-monitor will temporarily freeze message consumption to
ensure the new area will have a concrete `since` timestamp. This new area will be applied and message consumption will
continue like normal, with this new area being enabled starting from the specified `since`.

However, depending on when the update happens and depending on the difference between the old and new
area: `AreaEndEvent` may have inaccurate timestamps and `AreaStartEvent` may not be fired at all.

| Location | Description                                          | Result                                                                                                                                                                   |
|:--------:|------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|    1     | Ship is outside of the old and new area.             | No discrepancies, no events will be fired.                                                                                                                               |
|    2     | Ship is outside of the old, but inside the new area. | An `AreaStartEvent` will NOT be fired. The ship will have to actually move into the new area to trigger an `AreaStartEvent`.                                             |
|    3     | Ship is inside both the old and new area.            | No discrepancies, `AreaStartEvent` has already been fired, albeit that its timestamp will reference the time of entering the old area, and not the time of the new area. |

When moving in the other direction (assuming this area is also acting as the outer/exit area):

| Location | Description                                          | Result                                                                                                                                                                |
|:--------:|------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|    3     | Ship is inside both the old and new area.            | No discrepancies, no events will be fired.                                                                                                                            |
|    2     | Ship is outside of the old, but inside the new area. | No discrepancies, an `AreaEndEvent` has not been fired yet. And will be fired once it leaves the new area.                                                            |
|    1     | Ship is outside of the old and new area.             | No discrepancies, `AreaEndEvent` has already been fired, albeit that its timestamp will reference the time of exiting the old area, and not the time of the new area. |

You could miss out on an `AreaStartEvent` when:

- Ship is moving into the inner area and the new inner area has been enlarged, and a ship is already in the new area but
  had not yet entered the old area.

An `AreaEndEvent` may be impacted when:

- Ship is moving out of the outer area and the new outer area has been shrunken, and a ship is still inside the old area
  and hasn't left yet but is already outside the new area. The event will still be generated though, albeit with the
  timestamp being of the last known ship location inside the old area (which may be later than when the ship left the
  new outer area).

_Legend:_

- 🟢 = inner area
- 🔵 = ship's route

![](./images/NLRTM_area_zoom.png)
