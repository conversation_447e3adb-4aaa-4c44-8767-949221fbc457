# area-monitor

[<< back](../README.md)

## Envisioned horizontal scaling of areas

### Overview

Previously the area-monitor was made scalable by partitioning areas over multiple instances. We used to partition by
area instead of by ship, since the envisioned bottleneck in area-monitor's scalability was the amount of areas rising
too much. And only by splitting on area can you decrease and balance the load on the area-monitor, the number of ships
doesn't scale as much as the amount of areas could scale.

The implementation for this has been removed, but the documentation for the scalable area-monitor has been documented
below.

### Internal communication

When the area-monitor is scaled past 1 partition, it will require to internally communicate between partitions to
coordinate
different actions and perform updating and deleting areas.

When areas are requested to be updated/deleted they are placed into a queue and processed asynchronously. Once a request
is taken from the queue, the area-monitor will figure out a strategy to ensure the update/delete action can take place.
If areas need to be updated, they will first be fetched from the appropriate resource, and only if this was successfully
performed will it continue determining and executing the required strategy.

There are four actions that can be taken as part of a strategy:

| Strategy type    | Communication Type | Description                                                                                                                                               |
|------------------|--------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------|
| update (/delete) | Direct             | A request is sent to a specific partition to signal updating/deleting those specified areas.                                                              |
| scale            | Kubernetes API     | Scales the Kubernetes StatefulSet to a size of N partitions (`N >= 1`).                                                                                   |
| balance          | Delegated          | Balances areas between the delegated partition and the target partition. Balancing equally distributes the various area types between the two partitions. |
| move             | Delegated          | Moves areas from the delegated partition to the target partition.                                                                                         |

In all cases area-monitor-0 acts as the leader. When making a request to update/delete areas it will be proxied to the
leader (if not already sent to the leader). In the case of updating/deleting areas the leader can directly communicate
to a partition to signal the updating/deleting of specific areas contained within that partition. In the case of
the `balance` and `move` strategy types it will delegate the responsibilities of performing this action to another
partition. Since these actions need to be coordinated between these two partitions, to ensure that both area-monitors
will
freeze message consumption at the same time, perform the required action, and both unfreeze.

### Determining update/delete strategies

When determining the strategy, the area-monitor will ensure that currently present areas will be updated/deleted first,
before making further changes like scaling up/down, balancing or moving.

#### Scaling up

When the maximum capacity of one partition has been reached, the area-monitor will need to scale up. Depending on how
many
areas are to be added it will spin up 1 or multiple partitions to cover these additions.

The leader (area-monitor-0) will wait for the scale action to complete before sending updates to the newly added
partitions to load these areas.

After adding these areas, the partitions will require balancing. This is done to ensure efficient distribution of the
different areas across all partitions, by ensuring that all partitions have roughly the same amount of areas per area
type.

The balancing will never be perfect though, since:

- Only two partitions will be balanced at once, meaning only the areas present in those two can be equally distributed,
  not all the partitions at the same time.
- There could be an odd amount of areas of any given type.
- The balancing is only triggered if a certain threshold is met that would prompt this action.

**An example of scaling up to 4 partitions**

![](../images/scaling_up.png)

(Do note that the ordering of balance steps and the steps taken are only meant to display what could happen. Which exact
partitions are involved in balancing and in what order is determined as part of the strategy.)

#### Scaling down

When multiple areas are to be deleted from the area-monitor, the leader (area-monitor-0) will notice that a smaller
amount of partitions would suffice for the currently monitored areas minus the ones to be deleted.

Starting from the last partition, it will move all areas from that partition into the first partition. Continuing moving
areas
to the second partition if the first became full, and so on. After moving all areas out of the last partition, this
partition
can be removed by scaling down. This process will repeat itself until no partitions can be removed anymore by moving
areas
to other partitions and emptying it.

After moving all areas and scaling down, they also will require balancing for efficiency, as described in the section
about scaling up.

**An example of scaling down to 2 partitions**

![](../images/scaling_down.png)
