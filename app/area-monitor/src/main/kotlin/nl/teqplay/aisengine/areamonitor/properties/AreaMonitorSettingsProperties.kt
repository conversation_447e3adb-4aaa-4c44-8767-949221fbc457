package nl.teqplay.aisengine.areamonitor.properties

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "settings")
data class AreaMonitorSettingsProperties(
    /**
     * Details about fetching from resources
     */
    val fetch: Fetch,

    /**
     * Settings related to the admin of area-monitor
     */
    val admin: Admin,

    /**
     * Flag to enable sending state events for certain area types.
     */
    val enableStateEventsForAreaType: Set<AreaIdentifier.AreaType>,
) {
    data class Fetch(
        /**
         * When fetching areas from a resource, this determines how many areas are fetched per request
         */
        val size: Int,

        /**
         * <PERSON>ron specifying when areas are checked if they require being updated from a resource
         */
        val cron: String
    )

    data class Admin(
        /**
         * Specifies how many areas can be requested to be updated/deleted at once
         */
        val maximumInputSize: Int,

        /**
         * Prune completed actions/strategies after a certain amount of time
         */
        val pruneQueue: Duration
    )
}
