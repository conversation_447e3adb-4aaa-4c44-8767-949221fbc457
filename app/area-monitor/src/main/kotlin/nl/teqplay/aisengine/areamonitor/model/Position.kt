package nl.teqplay.aisengine.areamonitor.model

import nl.teqplay.skeleton.model.Location
import org.bson.codecs.pojo.annotations.BsonId
import java.time.Instant

/**
 * Lightweight ship position data for persistence
 *
 * @param mmsi The ship's MMSI (Maritime Mobile Service Identity) - used as the document ID
 * @param location The ship's latest known location
 * @param messageTime When this position was recorded
 */
data class Position(
    @BsonId
    val mmsi: Int,
    val location: Location,
    val messageTime: Instant
)
