package nl.teqplay.aisengine.areamonitor.model.config

import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import nl.teqplay.aisengine.areamonitor.model.config.ConfigQueueAction.Type
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import java.time.Instant
import java.util.UUID

/**
 * A queued action to make updates to the config.
 *
 * @param type either [Type.UPDATE] or [Type.DELETE]
 * @param resource from which resource the [areas] come from
 * @param areas the areas to fetch/update/delete
 */
data class ConfigQueueAction(
    val type: Type,
    val resource: AreaResourceIdentifier.Resource,
    val areas: Map<AreaIdentifier.AreaType, Set<String>>,

    val tries: Int = 0,
    val completed: Boolean = false,

    val time: Instant = Instant.now(),
    val _id: String = UUID.randomUUID().toString()
) {

    enum class Type {
        UPDATE,
        DELETE
    }
}
