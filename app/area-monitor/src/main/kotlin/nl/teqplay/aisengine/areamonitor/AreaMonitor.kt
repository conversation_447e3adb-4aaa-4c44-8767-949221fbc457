package nl.teqplay.aisengine.areamonitor

import nl.teqplay.skeleton.common.logging.EnableIncomingRequestLogging
import nl.teqplay.skeleton.common.logging.EnableOutgoingRequestLogging
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity

@SpringBootApplication(exclude = [MongoAutoConfiguration::class])
@ConfigurationPropertiesScan
@EnableScheduling
@EnableAsync
@EnableIncomingRequestLogging
@EnableOutgoingRequestLogging
@EnableGlobalMethodSecurity(securedEnabled = true)
class AreaMonitor : SpringBootServletInitializer()

fun main(args: Array<String>) {
    runApplication<AreaMonitor>(*args)
}
