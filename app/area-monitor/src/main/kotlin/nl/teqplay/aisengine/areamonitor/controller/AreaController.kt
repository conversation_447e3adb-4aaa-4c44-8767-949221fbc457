package nl.teqplay.aisengine.areamonitor.controller

import nl.teqplay.aisengine.areamonitor.service.config.AreaConfigUpdateService
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * Endpoints used to request area representations as well as acquiring all available
 * area identifiers within a given resource
 */
@RestController
@RequestMapping("/v1/area")
class AreaController(
    private val configUpdateService: AreaConfigUpdateService
) {
    @PostMapping("/scheduleRefreshAll")
    fun refreshAllAreas() {
        configUpdateService.refreshAllConfig()
    }
}
