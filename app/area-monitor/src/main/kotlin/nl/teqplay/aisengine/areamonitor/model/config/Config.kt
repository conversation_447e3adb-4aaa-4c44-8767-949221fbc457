package nl.teqplay.aisengine.areamonitor.model.config

import nl.teqplay.aisengine.areamonitor.model.Area
import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import java.time.Instant

/**
 * Configuration for a specific area
 *
 * @param area the area content itself
 * @param resource which resource contains this [area]
 * @param fetched when this [area] has last been fetched from its [resource]
 * @param since since when this [area] has been actively monitored
 */
data class Config(
    val area: Area,
    val resource: AreaResourceIdentifier.Resource,
    val fetched: Instant,
    val since: Instant,

    val _id: String = AreaResourceIdentifier(resource, area).toKey()
)
