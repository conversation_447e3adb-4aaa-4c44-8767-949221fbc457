package nl.teqplay.aisengine.areamonitor.util

import nl.teqplay.aisengine.areamonitor.model.Area
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.BERTH
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.END_OF_SEA_PASSAGE
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.NAUTICAL_MILE
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.TERMINAL_MOORING_AREA
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.poma.api.v1.ApiAreaMargin
import nl.teqplay.poma.api.v1.ApiInnerArea
import nl.teqplay.poma.api.v1.ApiModel
import nl.teqplay.poma.api.v1.ApiOuterArea
import nl.teqplay.poma.api.v1.ApiUnlocode
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.poma.api.v1.Terminal
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.util.blowUpPolygon
import nl.teqplay.poma.api.v1.Location as PomaLocation

private const val DISTANCE_OUTSIDE_BERTH_IN_METERS = 50
private const val METERS_TO_DECIMAL_DEGREES = 94500.0

fun <T : ApiModel> convertToArea(
    areaType: AreaIdentifier.AreaType,
    model: T
): Collection<Area> {
    val id = model._id ?: return emptyList()

    val area = (model as? ApiInnerArea)?.area
        ?.ifEmpty { null }
        ?.map { Location(it.latitude, it.longitude) }
        ?: return emptyList()

    val outerArea = (model as? ApiOuterArea)?.outerArea
        ?.ifEmpty { null }
        ?.map { Location(it.latitude, it.longitude) }

    val margin = (model as? ApiAreaMargin)?.margin?.takeIf { it != 0.0 }

    val polygon = Polygon(area)
    val outerPolygon = when {
        // Make sure we add outer polygon for berths to counter false exits
        areaType == BERTH && polygon.locations.size >= 4 -> {
            blowUpPolygon(polygon, DISTANCE_OUTSIDE_BERTH_IN_METERS / METERS_TO_DECIMAL_DEGREES)
        }
        outerArea != null -> Polygon(outerArea)
        margin != null -> blowUpPolygon(polygon, margin / METERS_TO_DECIMAL_DEGREES)
        else -> null
    }

    val pomaBerth = model as? Berth

    // if type=BERTH but not a berth, or if a berth but type!=BERTH
    if ((areaType == BERTH && pomaBerth == null) || (pomaBerth != null && areaType != BERTH)) {
        // this shouldn't happen and is invalid data
        return emptyList()
    }

    val berth = pomaBerth?.run {
        BerthIdentifier(
            direction = calculateAreaDirection(area),
            terminalName = terminalName?.trim()?.ifBlank { null }
        )
    }

    val unlocode = pomaBerth?.run {
        mainPort ?: ports.firstOrNull()
    } ?: (model as? ApiUnlocode)?.unlocode

    val result = Area(
        id = id,
        type = areaType,
        polygon = polygon,
        outerPolygon = outerPolygon,
        name = model.name,
        unlocode = unlocode,
        berth = berth
    )

    return when (model) {
        // If we have a Port, also return area objects for the "nautical miles" areas contained in there
        is Port -> {
            listOfNotNull(
                result,
                toNmArea(id, model.name, model.nm12Area, unlocode, 12),
                toNmArea(id, model.name, model.nm60Area, unlocode, 60),
                toNmArea(id, model.name, model.nm80Area, unlocode, 80),
                toNmArea(id, model.name, model.nm120Area, unlocode, 120),
                toEndOfSeaPassageArea(model)
            )
        }

        // If we have a Terminal, also return a terminal mooring area
        is Terminal -> {
            listOfNotNull(
                result,
                toTerminalMooringArea(id, model.name, model.mooringArea)
            )
        }

        else -> {
            listOf(result)
        }
    }
}

/**
 * Create an [Area] model for a "nautical miles" area in Poma. These are extra areas that are defined in the port
 * object as a list of locations without an identifier, so we have to create an identifier ourselves.
 */
private fun toNmArea(
    id: String,
    name: String,
    locations: List<PomaLocation>,
    unlocode: String?,
    distance: Int
): Area? {
    if (locations.isEmpty()) {
        return null
    }

    return Area(
        id = "$id.${distance}nm",
        type = NAUTICAL_MILE,
        polygon = locations.toPolygon(),
        outerPolygon = null,
        name = "$name $distance nm Area",
        unlocode = unlocode,
        berth = null
    )
}

private fun toEndOfSeaPassageArea(port: Port): Area? {
    val endOfSeaPassage = port.eosArea

    if (endOfSeaPassage.isNullOrEmpty()) {
        return null
    }

    return Area(
        id = "${port._id}.eosp",
        type = END_OF_SEA_PASSAGE,
        polygon = endOfSeaPassage.toPolygon(),
        outerPolygon = null,
        name = "${port.name} end of sea passage",
        unlocode = port.unlocode,
        berth = null
    )
}

/**
 * Create an [Area] model for a "terminal mooring" area in Poma. These are extra areas that are defined in the
 * terminal object as a list of locations without an identifier, so we have to create an identifier ourselves.
 */
private fun toTerminalMooringArea(
    id: String,
    name: String,
    locations: List<PomaLocation>,
): Area? {
    if (locations.isEmpty()) {
        return null
    }

    return Area(
        id = "$id.mooringarea",
        type = TERMINAL_MOORING_AREA,
        polygon = locations.toPolygon(),
        outerPolygon = null,
        name = name,
        unlocode = null,
        berth = null
    )
}

private fun List<PomaLocation>.toPolygon(): Polygon {
    val area = this.map { location -> location.toLocation() }
    return Polygon(area)
}

private fun PomaLocation.toLocation(): Location {
    return Location(lat = this.latitude, lon = this.longitude)
}
