package nl.teqplay.aisengine.areamonitor.datasource.config

import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import nl.teqplay.aisengine.areamonitor.model.config.ConfigQueueAction
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier

interface ConfigQueueActionDataSource {
    fun getFirstItem(): ConfigQueueAction?

    fun scheduleUpdate(
        resource: AreaResourceIdentifier.Resource,
        areas: Map<AreaIdentifier.AreaType, Set<String>>
    )

    fun scheduleDelete(
        resource: AreaResourceIdentifier.Resource,
        areas: Map<AreaIdentifier.AreaType, Set<String>>
    )

    fun incrementTry(id: String)
    fun complete(id: String)
}
