package nl.teqplay.aisengine.areamonitor.service.config

import nl.teqplay.aisengine.areamonitor.config.external.PomaRestTemplate
import nl.teqplay.aisengine.areamonitor.model.Area
import nl.teqplay.aisengine.areamonitor.model.config.Request
import nl.teqplay.aisengine.areamonitor.util.convertToArea
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.ANCHOR
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.APPROACH_POINT
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.BASIN
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.BERTH
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.CUSTOM
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.LOCK
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.PILOT_BOARDING_PLACE
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.PORT
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.TERMINAL
import nl.teqplay.poma.api.v1.Anchorage
import nl.teqplay.poma.api.v1.ApiModel
import nl.teqplay.poma.api.v1.ApproachArea
import nl.teqplay.poma.api.v1.Basin
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.poma.api.v1.CustomArea
import nl.teqplay.poma.api.v1.Lock
import nl.teqplay.poma.api.v1.PilotBoardingPlace
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.poma.api.v1.Terminal
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForObject

@Service
class PomaAreaResourceService(
    @PomaRestTemplate private val restTemplate: RestTemplate
) : AreaResourceService {
    override val supportedAreaTypes: Set<AreaIdentifier.AreaType>
        get() = setOf(PORT, ANCHOR, PILOT_BOARDING_PLACE, TERMINAL, BERTH, LOCK, BASIN, APPROACH_POINT, CUSTOM)

    override fun getRequestByAreaType(areaType: AreaIdentifier.AreaType): Request? {
        return when (areaType) {
            PORT -> getRequest<Port>("/v1/port", areaType)
            ANCHOR -> getRequest<Anchorage>("/v1/anchorage", areaType)
            PILOT_BOARDING_PLACE -> getRequest<PilotBoardingPlace>("/v1/pilotboardingplace", areaType)
            TERMINAL -> getRequest<Terminal>("/v1/terminal", areaType)
            BERTH -> getRequest<Berth>("/v1/berth", areaType)
            LOCK -> getRequest<Lock>("/v1/lock", areaType)
            BASIN -> getRequest<Basin>("/v1/basin", areaType)
            APPROACH_POINT -> getRequest<ApproachArea>("/v1/approacharea", areaType)
            CUSTOM -> getRequest<CustomArea>("/v1/customarea", areaType)
            else -> null
        }
    }

    private inline fun <reified T : ApiModel> getRequest(
        url: String,
        areaType: AreaIdentifier.AreaType
    ): Request = Request(
        getAll = getAllRequest<T>(url, areaType)
    )

    private inline fun <reified T : ApiModel> getAllPomaItems(
        url: String
    ): Array<T> = restTemplate.getForObject("$url?validated=true")

    private inline fun <reified T : ApiModel> getAllRequest(
        url: String,
        areaType: AreaIdentifier.AreaType
    ): () -> List<Area> {
        return {
            getAllPomaItems<T>(url)
                .flatMap { model -> convertToArea(areaType, model) }
        }
    }
}
