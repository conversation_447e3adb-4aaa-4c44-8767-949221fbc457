package nl.teqplay.aisengine.areamonitor.support

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.areamonitor.service.ConsumerService
import nl.teqplay.aisengine.areamonitor.service.StateService
import org.springframework.context.event.ContextClosedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.util.concurrent.TimeUnit

/**
 * A handler used to properly shut down this instance.
 */
@Component
class ShutdownHandler(
    private val consumerService: ConsumerService,
    private val stateService: StateService
) {

    private val log = KotlinLogging.logger { }

    @EventListener(ContextClosedEvent::class)
    fun shutdown(event: ContextClosedEvent) {
        log.info { "Shutting down" }

        val start = Instant.now()

        // await unfreezing if frozen
        if (consumerService.isFrozen()) {
            log.info { "Consumer is frozen, awaiting unfreeze..." }
            try {
                while (consumerService.isFrozen() && Duration.between(start, Instant.now()) < Duration.ofMinutes(1)) {
                    TimeUnit.SECONDS.sleep(5)
                }
            } catch (e: Throwable) {
                log.warn(e) { "Error unfreezing consumer" }
            }
        }

        // then stop consuming
        try {
            consumerService.shutdown()
        } catch (e: Throwable) {
            log.warn(e) { "Error shutting down consumer" }
        }

        // then ensure the state is persisted
        try {
            stateService.shutdown()
        } catch (e: Throwable) {
            log.warn(e) { "Error shutting down state service" }
        }

        log.info { "Shutdown complete" }
    }
}
