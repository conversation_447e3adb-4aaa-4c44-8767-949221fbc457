package nl.teqplay.aisengine.areamonitor.datasource.position

import nl.teqplay.aisengine.areamonitor.model.Position
import nl.teqplay.aisengine.revents.ReventsProfile
import org.springframework.stereotype.Component

/**
 * No-op implementation of PositionDataSource for Revents profile
 */
@Component
@ReventsProfile
class PositionDataSourceReventsImpl : PositionDataSource {
    override fun save(positions: List<Position>) {
        // No-op for Revents
    }

    override fun list(): List<Position> = emptyList()
}
