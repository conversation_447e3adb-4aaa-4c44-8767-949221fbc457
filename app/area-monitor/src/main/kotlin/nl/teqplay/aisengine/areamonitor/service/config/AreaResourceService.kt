package nl.teqplay.aisengine.areamonitor.service.config

import nl.teqplay.aisengine.areamonitor.model.Area
import nl.teqplay.aisengine.areamonitor.model.config.Request
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier

interface AreaResourceService {
    val supportedAreaTypes: Set<AreaIdentifier.AreaType>

    /**
     * Gets a lambda to fetch areas specific to a given [areaType] (if available).
     *
     * Upon invoking the lambda with a list of identifiers, a request will be made and the
     * objects will be converted into area-monitor compatible [Area] objects.
     */
    fun getRequestByAreaType(areaType: AreaIdentifier.AreaType): Request?

    fun getAll(): List<Area> {
        return supportedAreaTypes
            .mapNotNull { supportedAreaType -> getRequestByAreaType(supportedAreaType) }
            .flatMap { request -> request.getAll() }
    }
}
