package nl.teqplay.aisengine.areamonitor.datasource.config

import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.areamonitor.service.config.PomaAreaResourceService
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.revents.ReventsProfile
import org.springframework.stereotype.Component
import java.time.Instant

@Component
@ReventsProfile
class ConfigDataSourceReventsImpl(
    private val pomaAreaResourceService: PomaAreaResourceService
) : ConfigDataSource {

    override fun getAll(): List<Config> {
        val now = Instant.now()
        val areas = pomaAreaResourceService.getAll()
        return areas.map { area ->
            Config(
                area = area,
                resource = AreaResourceIdentifier.Resource.POMA,
                fetched = now,
                since = now
            )
        }
    }

    override fun getById(id: String): Config? = null

    override fun getMaxFetchedTimeForAreaType(
        resource: AreaResourceIdentifier.Resource,
        areaType: AreaIdentifier.AreaType
    ): Instant? = null

    override fun insert(config: List<Config>) {
    }

    override fun update(config: List<Config>) {
    }

    override fun delete(ids: Set<String>) {
    }

    override fun getContentByResource(
        resource: AreaResourceIdentifier.Resource,
        type: AreaIdentifier.AreaType?
    ): List<ConfigDataSource.Content> = emptyList()
}
