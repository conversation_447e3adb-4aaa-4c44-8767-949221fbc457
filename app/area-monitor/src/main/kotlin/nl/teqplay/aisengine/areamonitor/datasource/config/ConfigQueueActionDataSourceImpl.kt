package nl.teqplay.aisengine.areamonitor.datasource.config

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import nl.teqplay.aisengine.areamonitor.model.config.ConfigQueueAction
import nl.teqplay.aisengine.areamonitor.properties.AreaMonitorSettingsProperties
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.revents.NotReventsProfile
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ascending
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.inc
import nl.teqplay.skeleton.datasource.kmongo.lt
import nl.teqplay.skeleton.datasource.kmongo.set
import nl.teqplay.skeleton.datasource.kmongo.setTo
import org.springframework.stereotype.Component
import java.time.Instant

/**
 * A data source containing [ConfigQueueAction]. It's a queued action to:
 * - [ConfigQueueAction.Type.UPDATE] (or add) areas
 * - [ConfigQueueAction.Type.DELETE] areas
 */
@Component
@NotReventsProfile
class ConfigQueueActionDataSourceImpl(
    private val areaMonitorSettingsProperties: AreaMonitorSettingsProperties,
    mongoDatabase: MongoDatabase
) : ConfigQueueActionDataSource {
    private val collection = mongoDatabase.getCollection<ConfigQueueAction>("configQueueAction").also {
        it.ensureIndex(ConfigQueueAction::completed, ConfigQueueAction::time)
    }

    /**
     * Gets the first item in the queue for this specific instance of the area-monitor
     */
    override fun getFirstItem(): ConfigQueueAction? = collection
        .find(ConfigQueueAction::completed eq false)
        .sort(ascending(ConfigQueueAction::time))
        .limit(1)
        .firstOrNull()

    /**
     * Schedules the updating of the given [areas] on a [resource], by appending an item into the queue
     */
    override fun scheduleUpdate(
        resource: AreaResourceIdentifier.Resource,
        areas: Map<AreaIdentifier.AreaType, Set<String>>
    ) {
        collection.insertOne(
            ConfigQueueAction(
                type = ConfigQueueAction.Type.UPDATE,
                resource = resource,
                areas = areas,
            )
        )
    }

    /**
     * Schedules the deleting of the given [areas] on a [resource], by appending an item into the queue
     */
    override fun scheduleDelete(
        resource: AreaResourceIdentifier.Resource,
        areas: Map<AreaIdentifier.AreaType, Set<String>>
    ) {
        collection.insertOne(
            ConfigQueueAction(
                type = ConfigQueueAction.Type.DELETE,
                resource = resource,
                areas = areas,
            )
        )
    }

    /**
     * Increment the [ConfigQueueAction.tries] counter
     */
    override fun incrementTry(id: String) {
        collection.updateOne(ConfigQueueAction::_id eq id, inc(ConfigQueueAction::tries, 1))
    }

    /**
     * Complete the [ConfigQueueAction] by its [id]
     */
    override fun complete(id: String) {
        collection.updateOne(ConfigQueueAction::_id eq id, set(ConfigQueueAction::completed setTo true))
        deleteOld()
    }

    /**
     * Deletes all queued items if they have become too old
     */
    private fun deleteOld() {
        val duration = areaMonitorSettingsProperties.admin.pruneQueue
        val threshold = Instant.now() - duration
        collection.deleteMany(
            and(
                ConfigQueueAction::completed eq true,
                ConfigQueueAction::time lt threshold
            )
        )
    }
}
