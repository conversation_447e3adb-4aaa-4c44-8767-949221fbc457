package nl.teqplay.aisengine.areamonitor.datasource.position

import com.mongodb.client.model.ReplaceOneModel
import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.areamonitor.model.Position
import nl.teqplay.aisengine.revents.NotReventsProfile
import nl.teqplay.skeleton.datasource.kmongo.eq
import org.springframework.stereotype.Component

/**
 * MongoDB implementation of PositionDataSource for persisting ship positions
 */
@Component
@NotReventsProfile
class PositionDataSourceImpl(
    mongoDatabase: MongoDatabase
) : PositionDataSource {
    private val collection = mongoDatabase.getCollection<Position>("shipPositions")

    /**
     * Save ship positions using bulk upsert operations
     */
    override fun save(positions: List<Position>) {
        if (positions.isNotEmpty()) {
            collection.bulkWrite(
                positions.map { position ->
                    ReplaceOneModel(
                        Position::mmsi eq position.mmsi,
                        position,
                        ReplaceOptions().upsert(true)
                    )
                }
            )
        }
    }

    /**
     * Get all ship positions
     */
    override fun list(): List<Position> {
        return collection.find().toList()
    }
}
