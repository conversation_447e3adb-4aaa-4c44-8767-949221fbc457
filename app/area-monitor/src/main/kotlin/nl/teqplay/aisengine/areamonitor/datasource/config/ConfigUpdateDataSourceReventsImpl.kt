package nl.teqplay.aisengine.areamonitor.datasource.config

import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.revents.ReventsProfile
import org.springframework.stereotype.Component

@Component
@ReventsProfile
class ConfigUpdateDataSourceReventsImpl : ConfigUpdateDataSource {
    override fun get(ids: Set<String>): List<Config> = emptyList()

    override fun save(config: List<Config>) {
    }

    override fun delete(ids: Set<String>) {
    }
}
