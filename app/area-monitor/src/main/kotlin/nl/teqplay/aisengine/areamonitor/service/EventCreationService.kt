package nl.teqplay.aisengine.areamonitor.service

import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.StateAreaInsideEvent
import nl.teqplay.aisengine.event.model.StateAreaOutsideEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import org.springframework.stereotype.Component
import java.util.UUID

/**
 * Service to simply create [AreaStartEvent] and [AreaEndEvent] based on the area [Config] and [AisDiffMessage]
 */
@Component
class EventCreationService {

    fun createStartEvent(
        config: Config,
        diff: AisDiffMessage
    ): AreaStartEvent {
        val ship = diff.toShipIdentifier()
        val area = config.toAreaIdentifier()
        return AreaStartEvent(
            _id = UUID.randomUUID().toString(),
            ship = ship,
            area = area,
            berth = config.area.berth,
            heading = diff.heading.latest(),
            draught = diff.draught.latest(),
            speedOverGround = diff.speedOverGround.latest(),
            location = diff.location.latest(),
            actualTime = diff.messageTime
        )
    }

    fun createEndEvent(
        config: Config,
        diff: AisDiffMessage,
        startEventId: String?
    ): AreaEndEvent {
        val ship = diff.toShipIdentifier()
        val area = config.toAreaIdentifier()
        return AreaEndEvent(
            _id = UUID.randomUUID().toString(),
            startEventId = startEventId,
            ship = ship,
            area = area,
            berth = config.area.berth,
            heading = diff.heading.latest(),
            draught = diff.draught.latest(),
            speedOverGround = diff.speedOverGround.latest(),
            location = diff.location.latest(),
            actualTime = diff.messageTime
        )
    }

    fun createStateAreaInsideEvent(
        config: Config,
        diff: AisDiffMessage
    ): StateAreaInsideEvent {
        val ship = diff.toShipIdentifier()
        val area = config.toAreaIdentifier()
        return StateAreaInsideEvent(
            _id = UUID.randomUUID().toString(),
            ship = ship,
            area = area,
            berth = config.area.berth,
            heading = diff.heading.latest(),
            draught = diff.draught.latest(),
            speedOverGround = diff.speedOverGround.latest(),
            location = diff.location.latest(),
            actualTime = diff.messageTime
        )
    }

    fun createStateAreaOutsideEvent(
        config: Config,
        diff: AisDiffMessage
    ): StateAreaOutsideEvent {
        val ship = diff.toShipIdentifier()
        val area = config.toAreaIdentifier()
        return StateAreaOutsideEvent(
            _id = UUID.randomUUID().toString(),
            ship = ship,
            area = area,
            berth = config.area.berth,
            heading = diff.heading.latest(),
            draught = diff.draught.latest(),
            speedOverGround = diff.speedOverGround.latest(),
            location = diff.location.latest(),
            actualTime = diff.messageTime
        )
    }

    private fun AisDiffMessage.toShipIdentifier() = AisShipIdentifier(mmsi, imo.latest())

    private fun Config.toAreaIdentifier() = AreaIdentifier(
        id = area.id,
        type = area.type,
        name = area.name,
        unlocode = area.unlocode
    )
}
