package nl.teqplay.aisengine.areamonitor.datasource.config

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.revents.NotReventsProfile
import nl.teqplay.skeleton.datasource.kmongo.`in`
import org.springframework.stereotype.Component

/**
 * A data source containing [Config]. Normally this data source has no data. It's only used to store fetched areas.
 * These fetched areas will be removed once these areas area loaded.
 */
@Component
@NotReventsProfile
class ConfigUpdateDataSourceImpl(
    mongoDatabase: MongoDatabase
) : ConfigUpdateDataSource {
    private val collection = mongoDatabase.getCollection<Config>("configUpdate")

    /**
     * Get all configs that require updating by a given set of [ids]
     */
    override fun get(ids: Set<String>) =
        collection.find(Config::_id `in` ids).toList()

    /**
     * Save a list of updatable [config]
     */
    override fun save(config: List<Config>) {
        if (config.isEmpty()) return
        val ids = config.map { it._id }
        collection.deleteMany(Config::_id `in` ids)
        collection.insertMany(config)
    }

    /**
     * Delete the representations used for updating configs by a given set of [ids]
     */
    override fun delete(ids: Set<String>) {
        collection.deleteMany(Config::_id `in` ids)
    }
}
