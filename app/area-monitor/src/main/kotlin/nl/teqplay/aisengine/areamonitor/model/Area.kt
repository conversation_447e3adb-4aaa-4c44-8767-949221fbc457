package nl.teqplay.aisengine.areamonitor.model

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.skeleton.model.Polygon

data class Area(
    val id: String,
    val type: AreaIdentifier.AreaType,
    val polygon: Polygon,
    val outerPolygon: Polygon?,

    val name: String?,
    val unlocode: String?,
    val berth: BerthIdentifier?
)
