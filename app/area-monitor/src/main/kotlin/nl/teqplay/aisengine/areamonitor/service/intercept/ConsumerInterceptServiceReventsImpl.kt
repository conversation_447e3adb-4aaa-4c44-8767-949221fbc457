package nl.teqplay.aisengine.areamonitor.service.intercept

import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.service.ConsumerPublishService
import nl.teqplay.aisengine.areamonitor.service.EventGenerationService
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.revents.ReventsProfile
import org.springframework.stereotype.Component

@Component
@ReventsProfile
class ConsumerInterceptServiceReventsImpl(
    private val eventGenerationService: EventGenerationService,
    private val consumerPublishService: ConsumerPublishService
) : ConsumerInterceptService {

    private val seenShips = mutableSetOf<Int>()

    /**
     * Intercepts a [diff] and ensures [AreaStartEvent] are sent if the ship
     * is seen for the first time, and it's inside an area.
     */
    override fun intercept(diff: AisDiffMessage) {
        // only continue if ship has not been seen before
        if (!seenShips.add(diff.mmsi)) {
            return
        }

        // check if a ship is already inside an area
        val location = diff.location.latest()
        val events = eventGenerationService.processCurrentLocation(diff, location)

        // only save the state, we purposely don't want to send start events,
        // assuming those have been generated outside our process
        events.forEach { (config, event) ->
            consumerPublishService.save(diff, config, event)
        }
    }
}
