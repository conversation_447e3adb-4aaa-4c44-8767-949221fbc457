package nl.teqplay.aisengine.areamonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.areamonitor.service.config.AreaConfigService
import nl.teqplay.skeleton.model.Location
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.system.measureTimeMillis

/**
 * Service that periodically validates area state and handles missed end events.
 * 
 * Every 6 hours, this service:
 * 1. Goes through all ships with active start events in areas
 * 2. Checks if each ship is still in its respective area using latest position
 * 3. For ships that have left areas, sends missed end events
 * 4. Cleans up invalid state entries
 */
@Component
class AreaStateValidationService(
    private val stateService: StateService,
    private val areaSearchService: AreaSearchService,
    private val configService: AreaConfigService,
    private val eventCreationService: EventCreationService,
    private val consumerPublishService: ConsumerPublishService
) {
    
    private val log = KotlinLogging.logger { }
    
    /**
     * Scheduled validation task that runs every 6 hours.
     * Initial delay of 30 minutes to allow system to fully initialize.
     */
    @Scheduled(fixedRate = 6 * 60 * 60 * 1000L) // 6 hours in milliseconds
    fun validateAreaState() {
        log.info { "Starting area state validation..." }
        
        val timeTaken = measureTimeMillis {
            try {
                val validationResults = performValidation()
                log.info { 
                    "Area state validation completed. " +
                    "Checked ${validationResults.totalChecked} ships, " +
                    "found ${validationResults.missedEndEvents} ships outside areas, " +
                    "sent ${validationResults.eventsPublished} missed end events"
                }
            } catch (e: Exception) {
                log.error(e) { "Error during area state validation" }
            }
        }
        
        log.info { "Area state validation completed in ${timeTaken}ms" }
    }
    
    /**
     * Performs the actual validation logic
     */
    private fun performValidation(): ValidationResults {
        var totalChecked = 0
        var missedEndEvents = 0
        var eventsPublished = 0
        
        // Get all active start events from state service
        val activeStartEvents = stateService.getAllActiveStartEvents()
        
        for ((configId, mmsi, startEvent) in activeStartEvents) {
            totalChecked++
            
            // Get the area configuration
            val config = configService.getConfig(configId)
            if (config == null) {
                log.warn { "Config not found for configId: $configId" }
                continue
            }
            
            // Get the latest position for this ship
            val latestPosition = stateService.getLatestShipPosition(mmsi)
            if (latestPosition == null) {
                log.debug { "No latest position found for MMSI: $mmsi in area: ${config.area.name}" }
                continue
            }
            
            // Check if the ship is still inside the area
            val isStillInside = areaSearchService.isInsideArea(
                location = latestPosition.location,
                config = config,
                includeOuter = false
            )
            
            if (!isStillInside) {
                log.info { 
                    "Ship MMSI: $mmsi has left area: ${config.area.name} (${config.area.id}), " +
                    "sending missed end event"
                }

                // Create synthetic end message based on latest location
                val endMessage = startEvent.diff.createEndMessage(latestPosition.location)

                // Create and publish the missed end event
                val endEvent = eventCreationService.createEndEvent(
                    config = config,
                    diff = endMessage,
                    startEventId = startEvent.startEventId
                )

                cleanUpState(config, startEvent.diff)
                
                // Publish the event and update state
                consumerPublishService.save(endMessage, config, endEvent)
                consumerPublishService.publish(endMessage, listOf(config to endEvent))
                
                missedEndEvents++
                eventsPublished++
            }
        }
        
        return ValidationResults(totalChecked, missedEndEvents, eventsPublished)
    }
    
    /**
     * Cleans up invalid state for a config/mmsi combination
     */
    private fun cleanUpState(areaConfig: Config, diff: AisDiffMessage) {
        // This would need to be implemented in StateService
        log.warn { "Cleaning up invalid state for configId: ${areaConfig._id}, MMSI: ${diff.mmsi}" }
        stateService.deleteStartEvent(config = areaConfig, diff = diff)
    }

    private fun AisDiffMessage.createEndMessage(location: Location): AisDiffMessage {
        return this.copy(
            messageTime = Instant.now(),
            oldMessageTime = this.messageTime,
            location = AisDiffField(this.location.latest(), AisDiffField.Change(location))
        )
    }

    /**
     * Data class to hold validation results
     */
    private data class ValidationResults(
        val totalChecked: Int,
        val missedEndEvents: Int,
        val eventsPublished: Int
    )
}
