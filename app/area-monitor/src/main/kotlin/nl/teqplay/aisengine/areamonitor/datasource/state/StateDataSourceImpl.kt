package nl.teqplay.aisengine.areamonitor.datasource.state

import com.mongodb.client.model.ReplaceOneModel
import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.areamonitor.model.State
import nl.teqplay.aisengine.revents.NotReventsProfile
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.`in`
import org.springframework.stereotype.Component

/**
 * A data source containing [State]. One [State] represents one area configuration, which includes sent area start events.
 */
@Component
@NotReventsProfile
class StateDataSourceImpl(
    mongoDatabase: MongoDatabase
) : StateDataSource {
    private val collection = mongoDatabase.getCollection<State>("state")

    /**
     * Save all state for the given [list]
     */
    override fun save(
        list: List<State>
    ) {
        if (list.isNotEmpty()) {
            collection.bulkWrite(
                list.map {
                    ReplaceOneModel(State::_id eq it._id, it, ReplaceOptions().upsert(true))
                }
            )
        }
    }

    /**
     * Lists all state for the given [ids]
     */
    override fun list(
        ids: Set<String>
    ): List<State> = collection.find(State::_id `in` ids).toList()

    /**
     * Deletes state by the given [ids]
     */
    override fun delete(ids: Set<String>) {
        collection.deleteMany(State::_id `in` ids)
    }
}
