package nl.teqplay.aisengine.areamonitor.datasource.config

import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import nl.teqplay.aisengine.areamonitor.model.config.ConfigQueueAction
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.revents.ReventsProfile
import org.springframework.stereotype.Component

@Component
@ReventsProfile
class ConfigQueueActionDataSourceReventsImpl : ConfigQueueActionDataSource {

    override fun getFirstItem(): ConfigQueueAction? = null

    override fun scheduleUpdate(
        resource: AreaResourceIdentifier.Resource,
        areas: Map<AreaIdentifier.AreaType, Set<String>>
    ) {
    }

    override fun scheduleDelete(
        resource: AreaResourceIdentifier.Resource,
        areas: Map<AreaIdentifier.AreaType, Set<String>>
    ) {
    }

    override fun incrementTry(id: String) {
    }

    override fun complete(id: String) {
    }
}
