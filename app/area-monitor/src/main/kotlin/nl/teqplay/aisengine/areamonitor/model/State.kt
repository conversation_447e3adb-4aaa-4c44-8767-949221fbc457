package nl.teqplay.aisengine.areamonitor.model

import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.model.config.Config
import java.time.Instant

/**
 * State for a specific [Config], where the [_id] matches the [Config._id]
 *
 * The [events] identify start events and related event data.
 */
data class State(
    val _id: String,
    val events: Map<Int, StartEvent>,
    val outside: Set<Int> = emptySet()
) {

    /**
     * A start event that has been triggered for this specific [_id]
     *
     * @param diff latest message
     * @param startEventId related event id that's sent with the start event
     */
    data class StartEvent(
        val diff: AisDiffMessage,
        val startEventId: String,
        val lastStateEventSentTime: Instant? = null
    )
}
