package nl.teqplay.aisengine.areamonitor.datasource.config

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.areamonitor.datasource.config.ConfigDataSource.Content
import nl.teqplay.aisengine.areamonitor.model.Area
import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.revents.NotReventsProfile
import nl.teqplay.skeleton.datasource.kmongo.aggregate
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.descending
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.group
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.match
import nl.teqplay.skeleton.datasource.kmongo.push
import org.springframework.stereotype.Component
import java.time.Instant

/**
 * A data source used for area [Config].
 */
@Component
@NotReventsProfile
class ConfigDataSourceImpl(
    mongoDatabase: MongoDatabase
) : ConfigDataSource {
    private val collection = mongoDatabase.getCollection<Config>("config").also {
        it.ensureIndex(Config::resource, Config::area / Area::type, Config::area / Area::id, Config::fetched)
    }

    /**
     * Gets all areas
     */
    override fun getAll(): List<Config> =
        collection.find().toList()

    override fun getById(id: String): Config? {
        return collection.findOneById(id)
    }

    /**
     * Returns the maximal [Config.fetched] time within this specific [resource] and [areaType]
     */
    override fun getMaxFetchedTimeForAreaType(
        resource: AreaResourceIdentifier.Resource,
        areaType: AreaIdentifier.AreaType
    ): Instant? = collection.find(and(Config::resource eq resource, Config::area / Area::type eq areaType))
        .sort(descending(Config::fetched))
        .limit(1)
        .firstOrNull()
        ?.fetched

    override fun insert(config: List<Config>) {
        collection.insertMany(config)
    }

    /**
     * Updates all [config]
     */
    override fun update(config: List<Config>) {
        if (config.isEmpty()) return
        val ids = config.map { it._id }
        collection.deleteMany(Config::_id `in` ids)
        collection.insertMany(config)
    }

    /**
     * Deletes all areas by the given [ids]
     */
    override fun delete(ids: Set<String>) {
        collection.deleteMany(Config::_id `in` ids)
    }

    /**
     * Gets all contained area identifiers for a specific [resource] and an optional [type]
     */
    override fun getContentByResource(
        resource: AreaResourceIdentifier.Resource,
        type: AreaIdentifier.AreaType?
    ): List<Content> = collection
        .aggregate<Content>(
            match(
                and(
                    Config::resource eq resource,
                    type?.let { Config::area / Area::type eq it }
                )
            ),
            group(
                Config::area / Area::type,
                Content::ids.push(Config::area / Area::id)
            )
        )
        .toList()
}
