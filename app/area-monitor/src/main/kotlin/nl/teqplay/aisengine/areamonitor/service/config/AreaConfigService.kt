package nl.teqplay.aisengine.areamonitor.service.config

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.areamonitor.datasource.config.ConfigDataSource
import nl.teqplay.aisengine.areamonitor.model.config.Config
import org.springframework.stereotype.Service
import kotlin.system.measureTimeMillis

/**
 * A service managing the required [Config] for multiple areas.
 */
@Service
class AreaConfigService(
    private val configDataSource: ConfigDataSource
) {
    private val log = KotlinLogging.logger { }
    private val configMap = mutableMapOf<String, Config>()

    /**
     * Loads all areas on startup
     */
    init {
        log.info { "Loading areas..." }
        val timeTaken = measureTimeMillis {
            configDataSource.getAll()
                .associateByTo(configMap) { it._id }
        }
        log.info { "Loaded ${configMap.size} areas in ${timeTaken}ms" }
    }

    fun getConfigIds(): Set<String> = configMap.keys
    fun getConfigs(): List<Config> = configMap.values.toList()
    fun getConfig(id: String): Config? = configMap[id]
    fun getConfigs(ids: Set<String>): List<Config> = ids.mapNotNull { id -> getConfig(id) }

    /**
     * Loads area [config] into memory
     */
    fun loadLocally(config: List<Config>) {
        config.forEach { configMap[it._id] = it }
    }

    /**
     * Remove area config by the given [ids] from memory
     */
    fun removeLocally(ids: Set<String>) {
        ids.forEach { configMap.remove(it) }
    }
}
