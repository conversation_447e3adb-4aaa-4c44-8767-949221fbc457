package nl.teqplay.aisengine.areamonitor.service.config

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.areamonitor.datasource.config.ConfigDataSource
import nl.teqplay.aisengine.areamonitor.model.Area
import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.areamonitor.service.AreaSearchService
import nl.teqplay.aisengine.areamonitor.service.EventCreationService
import nl.teqplay.aisengine.areamonitor.service.StateService
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.nats.stream.EventStreamService
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * Service used to do any updating of every area [Config].
 */
@Service
class AreaConfigUpdateService(
    private val configDataSource: ConfigDataSource,
    private val pomaAreaResourceService: PomaAreaResourceService,
    private val areaConfigService: AreaConfigService,
    private val areaSearchService: AreaSearchService,
    private val stateService: StateService,
    private val eventCreationService: EventCreationService,
    private val eventStreamService: EventStreamService
) {
    private val log = KotlinLogging.logger { }

    @Scheduled(cron = "\${settings.fetch.cron}")
    fun refreshAllConfig() {
        val now = Instant.now()
        refreshAllConfig(now)
    }

    fun refreshAllConfig(now: Instant) {
        log.info { "Refreshing config using Poma" }
        val allPomaAreas = pomaAreaResourceService.getAll()
        val convertedConfig = convertAreasToConfig(
            areas = allPomaAreas,
            resource = AreaResourceIdentifier.Resource.POMA,
            now = now
        )
        replaceConfig(convertedConfig)
        log.info { "Done refreshing all areas using Poma" }
    }

    /**
     * Insert the new [configs] in the database.
     *
     * This will add the config in the following:
     * 1. The database
     * 2. In memory config map
     *
     * @param configs The newly added configs that we want to start tracking
     */
    fun insert(configs: List<Config>) {
        configDataSource.insert(configs)
        areaConfigService.loadLocally(configs)
    }

    /**
     * Update multiple area [configs]. After updating check if we moved outside our new outer polygons.
     * If this is the case then we need to trigger an end event, as we will only be able to end the ongoing area state
     *  once it enters the new polygon and then leaves on the normal way, which could potentially take a long time or might never happen.
     *
     * This will update the config in the following:
     * 1. The database
     * 2. In memory config map
     *
     * @param configs The [Config] we want to update
     */
    fun update(configs: List<Config>) {
        configDataSource.update(configs)
        areaConfigService.loadLocally(configs)

        log.info { "Finished updating ${configs.size} areas, checking now if we need to create any exit events" }
        val configMap = configs.associateBy { it._id }
        val currentAreaStates = stateService.getStateList(configMap.keys)

        for (areaState in currentAreaStates) {
            val areaConfig = configMap.getValue(areaState._id)

            val createdExitEvents = areaState.events.mapNotNull { (_, startEventState) ->
                val latestShipLocation = startEventState.diff.location.latest()
                val isShipStillInUpdatedArea = areaSearchService.isInsideArea(
                    location = latestShipLocation,
                    config = areaConfig,
                    includeOuter = true // Check outer are as we only want to check for end events
                )

                // Create an area exit event if the outer area polygon was moved away from the current ship location
                if (!isShipStillInUpdatedArea) {
                    // Delete the current state for the ship we are creating an exit event for
                    stateService.deleteStartEvent(config = areaConfig, diff = startEventState.diff)

                    eventCreationService.createEndEvent(
                        config = areaConfig,
                        diff = startEventState.diff,
                        startEventId = startEventState.startEventId
                    )
                } else {
                    // Ship is still inside area, we don't have to do anything
                    null
                }
            }

            // Send all generated exit events
            if (createdExitEvents.isNotEmpty()) {
                log.info { "Publishing ${createdExitEvents.size} end events for UPDATED area (id = ${areaConfig._id})" }
                createdExitEvents.forEach { eventStreamService.publish(it) }
            } else {
                log.debug { "All ${areaState.events.size} ships are still inside the updated area, no exit events were needed" }
            }
        }
    }

    /**
     * Delete area config and also check if we need to create any exit events for any ships still in those areas.
     *
     * This will delete the config from the following:
     * 1. The database
     * 2. In memory config map
     *
     * @param configs The [Config] we want to delete from the AreaMonitor
     */
    fun delete(configs: Map<String, Config>) {
        // Checking if we need to create any exit events before deleting the areas to avoid having start events that never finished
        val currentAreaStates = stateService.getStateList(configs.keys)

        if (currentAreaStates.isNotEmpty()) {
            log.info { "Found ${currentAreaStates.size} states for areas out of the ${configs.size} deleted areas. Checking if we need to create any exit events" }
            for (areaState in currentAreaStates) {
                val areaConfig = configs.getValue(areaState._id)

                val createdExitEvents = areaState.events.mapNotNull { (_, startEventState) ->
                    // Delete the current state for the ship we are creating an exit event for
                    stateService.deleteStartEvent(config = areaConfig, diff = startEventState.diff)

                    eventCreationService.createEndEvent(
                        config = areaConfig,
                        diff = startEventState.diff,
                        startEventId = startEventState.startEventId
                    )
                }

                // Send all generated exit events
                log.info { "Publishing ${createdExitEvents.size} end events for DELETED area (id = ${areaConfig._id})" }
                createdExitEvents.forEach { eventStreamService.publish(it) }
            }
        } else {
            log.info { "No ship states were found in any of our deleted areas" }
        }

        configDataSource.delete(configs.keys)
        areaConfigService.removeLocally(configs.keys)
    }

    private fun replaceConfig(newConfig: List<Config>) {
        val groupedCurrentConfigs = configDataSource.getAll()
            .groupBy { config -> config.area.type }
        val groupedNewConfigs = newConfig.groupBy { config -> config.area.type }

        AreaIdentifier.AreaType.entries.forEach { areaType ->
            val typeCurrentConfigs = groupedCurrentConfigs[areaType]
            val typeNewConfigs = groupedNewConfigs[areaType]

            replaceConfigOfAreaType(
                areaType = areaType,
                newConfigs = typeNewConfigs ?: emptyList(),
                currentConfigs = typeCurrentConfigs ?: emptyList()
            )
        }
    }

    private fun replaceConfigOfAreaType(areaType: AreaIdentifier.AreaType, newConfigs: List<Config>, currentConfigs: List<Config>) {
        if (newConfigs.isEmpty() && currentConfigs.isEmpty()) {
            log.info { "[$areaType] There is nothing to replace as both the new and current config don't have any areas" }
            return
        }

        if (newConfigs.isEmpty()) {
            log.info { "[$areaType] There is no new config, meaning all ${currentConfigs.size} areas will be deleted" }
            // We can delete all our current configs as apparently our area sources don't have any of our area type anymore
            val configsToDelete = currentConfigs.associateBy { it._id }
            return delete(configs = configsToDelete)
        }

        if (currentConfigs.isEmpty()) {
            log.info { "[$areaType] There is no existing config, meaning all ${newConfigs.size} areas are all new" }
            return insert(configs = newConfigs)
        }

        // At this point we can possibly have newly added, updated and deleted areas.
        // This means we have to go over all areas to see if there are changes.
        val newConfigById = newConfigs.associateBy { config -> config._id }
        val currentConfigById = currentConfigs.associateBy { config -> config._id }

        val added = newConfigById - currentConfigById.keys
        val removed = currentConfigById - newConfigById.keys
        val sameIds = currentConfigById.keys intersect newConfigById.keys
        val (unchanged, changed) = sameIds.partition { id ->
            val current = currentConfigById[id]
            val new = newConfigById[id]

            // Check only the areas as that only changes how the areas are monitored
            current?.area == new?.area
        }
        val updated = changed.mapNotNull { id -> newConfigById[id] }

        log.info { "[$areaType] Updating configs (added = ${added.size}, updated = ${updated.size}, deleted = ${removed.size}, unchanged = ${unchanged.size})" }
        if (added.isNotEmpty()) { insert(configs = added.values.toList()) }
        if (updated.isNotEmpty()) { update(configs = updated) }
        if (removed.isNotEmpty()) { delete(configs = removed) }
    }

    private fun convertAreasToConfig(
        areas: List<Area>,
        resource: AreaResourceIdentifier.Resource,
        now: Instant
    ): List<Config> {
        return areas.map { area ->
            convertAreaToConfig(area, resource, now)
        }
    }

    private fun convertAreaToConfig(
        area: Area,
        resource: AreaResourceIdentifier.Resource,
        now: Instant
    ): Config {
        return Config(
            area = area,
            resource = resource,
            fetched = now,
            since = now
        )
    }
}
