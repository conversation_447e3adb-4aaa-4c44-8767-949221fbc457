package nl.teqplay.aisengine.areamonitor.properties.external

import nl.teqplay.skeleton.common.config.KeycloakS2SClientProperties
import org.springframework.boot.context.properties.ConfigurationProperties

/**
 * Properties used to connect to POMA.
 */
@ConfigurationProperties(prefix = "poma")
data class PomaProperties(
    override val url: String,
    override val domain: String?,
    override val realm: String?,
    override val clientId: String,
    override val clientSecret: String
) : KeycloakS2SClientProperties(url, domain, realm, clientId, clientSecret)
