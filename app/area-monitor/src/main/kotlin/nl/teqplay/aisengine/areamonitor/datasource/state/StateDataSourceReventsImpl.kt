package nl.teqplay.aisengine.areamonitor.datasource.state

import nl.teqplay.aisengine.areamonitor.model.State
import nl.teqplay.aisengine.revents.ReventsProfile
import org.springframework.stereotype.Component

@Component
@ReventsProfile
class StateDataSourceReventsImpl : StateDataSource {
    override fun save(list: List<State>) {
    }

    override fun list(ids: Set<String>): List<State> = emptyList()

    override fun delete(ids: Set<String>) {
    }
}
