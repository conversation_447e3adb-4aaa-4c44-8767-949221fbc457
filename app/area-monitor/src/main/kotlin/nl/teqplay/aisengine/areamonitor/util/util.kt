package nl.teqplay.aisengine.areamonitor.util

import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.util.calculateAngle
import nl.teqplay.skeleton.util.haversineDistance

/**
 * Helper method to calculate the direction of an [area], being the absolute
 * attitude of the longest side.
 *
 * @return the direction in decimal degrees (0 - 359.9999) relative to
 * absolute North
 */
fun calculateAreaDirection(area: List<Location>): Double? {
    if (area.size < 2) return null
    val (loc1, loc2) = area.zipWithNext().maxBy { (prev, next) -> haversineDistance(prev, next) }
    return calculateAngle(loc1, loc2)
}
