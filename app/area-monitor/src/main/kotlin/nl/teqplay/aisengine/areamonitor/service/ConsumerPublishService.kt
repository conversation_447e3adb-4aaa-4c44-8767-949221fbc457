package nl.teqplay.aisengine.areamonitor.service

import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.areamonitor.properties.AreaMonitorSettingsProperties
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.StateEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.StateAreaInsideEvent
import nl.teqplay.aisengine.event.model.StateAreaOutsideEvent
import nl.teqplay.aisengine.nats.stream.EventStreamService
import org.springframework.stereotype.Component

/**
 * The service that publishes 0-N [AreaStartEvent] or [AreaEndEvent].
 */
@Component
class ConsumerPublishService(
    private val stateService: StateService,
    private val eventStreamService: EventStreamService,
    private val properties: AreaMonitorSettingsProperties,
) {

    /**
     * Publish the [events] for the [diff] and ensures the state is updated.
     */
    fun publish(
        diff: AisDiffMessage,
        events: List<Pair<Config, AreaEvent>>
    ) {
        for ((config, event) in events) {
            // publish message, and save in state only if successfully published
            if (event !is StateEvent || event.area.type in properties.enableStateEventsForAreaType) {
                eventStreamService.publish(event)
            }
            save(diff, config, event)
        }
    }

    /**
     * Save the [event] for an area.
     */
    fun save(
        diff: AisDiffMessage,
        config: Config,
        event: AreaEvent
    ) {
        when (event) {
            is AreaStartEvent -> stateService.saveStartEvent(config, diff, event._id)
            is AreaEndEvent -> stateService.deleteStartEvent(config, diff)
            is StateAreaInsideEvent -> stateService.saveLastStateEventSentTime(config, diff, event.actualTime)
            is StateAreaOutsideEvent -> stateService.deleteOutsideEvent(config, diff)
        }
    }
}
