package nl.teqplay.aisengine.areamonitor.config.external

import nl.teqplay.aisengine.areamonitor.properties.external.PomaProperties
import nl.teqplay.skeleton.auth.credentials.keycloak.s2s.client.KeycloakS2SClientWrapper
import nl.teqplay.skeleton.common.logging.OutgoingRequestLogger
import org.springframework.beans.factory.ObjectProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate
import java.lang.annotation.Inherited

@Configuration
class PomaConfiguration {

    @Bean(POMA_REST_TEMPLATE)
    fun pomaRestTemplate(
        restTemplateBuilder: RestTemplateBuilder,
        outgoingRequestLoggerProvider: ObjectProvider<OutgoingRequestLogger>,
        pomaProperties: PomaProperties
    ): RestTemplate = KeycloakS2SClientWrapper.create(
        restTemplateBuilder,
        outgoingRequestLoggerProvider,
        pomaProperties
    )
}

const val POMA_REST_TEMPLATE = "PomaRestTemplate"

/** Allow the template to be used as an annotation */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Inherited
@Qualifier(POMA_REST_TEMPLATE)
annotation class PomaRestTemplate
