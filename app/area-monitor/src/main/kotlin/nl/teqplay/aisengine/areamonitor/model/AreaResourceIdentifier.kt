package nl.teqplay.aisengine.areamonitor.model

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier

/**
 * An identifier for an area from a specific [resource]
 *
 * @param resource where the area is stored and can be retrieved from
 * @param type area type
 * @param id identifier of the area, unique in its [type] and [resource]
 */
data class AreaResourceIdentifier(
    val resource: Resource,
    val type: AreaIdentifier.AreaType,
    val id: String
) {
    constructor(resource: Resource, area: Area) : this(resource, area.type, area.id)

    fun toKey(): String = "${resource}_${type}_$id"

    enum class Resource {
        POMA
    }
}
