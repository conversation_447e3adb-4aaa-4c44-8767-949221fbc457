package nl.teqplay.aisengine.areamonitor.service

import com.github.davidmoten.rtree.RTree
import com.github.davidmoten.rtree.geometry.Geometries
import com.github.davidmoten.rtree.geometry.Rectangle
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.areamonitor.service.config.AreaConfigService
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.util.pointInPolygon
import org.springframework.stereotype.Component
import java.util.concurrent.atomic.AtomicReference
import kotlin.system.measureTimeMillis

/**
 * A simple area search service, lending its implementation from the area searching lib
 */
@Component
class AreaSearchService(
    private val configService: AreaConfigService
) {
    private val log = KotlinLogging.logger { }

    private var tree = AtomicReference(RTree.star().create<String, Rectangle>())

    /**
     * Initializes the tree to be able to search for areas
     */
    init {
        log.info { "Initializing search tree..." }
        val timeTaken = measureTimeMillis {
            update(configService.getConfigs())
        }
        log.info { "Initialized search tree in ${timeTaken}ms" }
    }

    fun update(configs: Collection<Config>) {
        val configIds = configs.map { it._id }.toSet()
        tree.set(
            configs.fold(tree.get().delete(configIds)) { tree, config ->
                tree.add(config)
            }
        )
    }

    fun delete(configIds: Set<String>) {
        tree.set(tree.get().delete(configIds))
    }

    private fun RTree<String, Rectangle>.add(config: Config): RTree<String, Rectangle> {
        val polygon = config.area.outerPolygon ?: config.area.polygon
        val (lat1, lat2, lon1, lon2) = listOf(
            polygon.locations.minOf { it.lat }, polygon.locations.maxOf { it.lat },
            polygon.locations.minOf { it.lon }, polygon.locations.maxOf { it.lon }
        )
        return add(config._id, Geometries.rectangleGeographic(lon1, lat1, lon2, lat2))
    }

    private fun RTree<String, Rectangle>.delete(configIds: Set<String>): RTree<String, Rectangle> {
        var mutableTree = this
        mutableTree.entries().filter { it.value() in configIds }.forEach { entry ->
            mutableTree = mutableTree.delete(entry)
        }
        return mutableTree
    }

    /**
     * Given a [location] get all areas where this [location] is inside.
     *
     * Use [includeOuter] if you want to include the outerPolygon in the search.
     */
    fun getInsideAreas(
        location: Location,
        includeOuter: Boolean
    ): MutableSet<Config> {
        val areas = mutableSetOf<Config>()

        tree.get()
            .search(Geometries.pointGeographic(location.lon, location.lat))
            .forEach {
                val config = configService.getConfig(it.value())
                if (config != null) {
                    if (isInsideArea(location, config, includeOuter)) {
                        areas.add(config)
                    }
                }
            }

        return areas
    }

    /**
     * Given a [location] and a [config] return if this [location] is inside.
     *
     * Use [includeOuter] if you want to include the outerPolygon in the search.
     */
    fun isInsideArea(
        location: Location,
        config: Config,
        includeOuter: Boolean
    ): Boolean {
        val polygon = if (includeOuter) config.area.outerPolygon ?: config.area.polygon else config.area.polygon
        return pointInPolygon(polygon, location)
    }

    /**
     * Given a [from] and [to] location get all areas where the [from] location is outside
     * the area and the [to] is inside the area
     *
     * Use [includeOuter] if you want to include the [AreaSpec.outerPolygon] in the search.
     *
     * Examples:
     * - moving into the inner area:
     *      getMovedIntoAreas(from, to, false)
     * - moving out of the outer area:
     *      getMovedIntoAreas(to, from, true)
     *
     * IMPORTANT: when checking if a move is leaving the area, you need to flip the [from] and [to] location.
     */
    fun getMovedIntoAreas(
        from: Location,
        to: Location,
        includeOuter: Boolean
    ): MutableSet<Config> {
        val areas = mutableSetOf<Config>()

        val minLat = minOf(from.lat, to.lat)
        val maxLat = maxOf(from.lat, to.lat)
        val minLon = minOf(from.lon, to.lon)
        val maxLon = maxOf(from.lon, to.lon)

        tree.get()
            .search(Geometries.rectangleGeographic(minLon, minLat, maxLon, maxLat))
            .forEach {
                val config = configService.getConfig(it.value())
                if (config != null) {
                    val polygon =
                        if (includeOuter) config.area.outerPolygon ?: config.area.polygon else config.area.polygon
                    // `from => to` should be a move into the polygon
                    if (!pointInPolygon(polygon, from) && pointInPolygon(polygon, to)) {
                        areas.add(config)
                    }
                }
            }

        return areas
    }
}
