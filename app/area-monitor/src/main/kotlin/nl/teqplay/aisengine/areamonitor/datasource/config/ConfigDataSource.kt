package nl.teqplay.aisengine.areamonitor.datasource.config

import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import org.bson.codecs.pojo.annotations.BsonId
import java.time.Instant

interface ConfigDataSource {
    fun getAll(): List<Config>
    fun getById(id: String): Config?

    fun getMaxFetchedTimeForAreaType(
        resource: AreaResourceIdentifier.Resource,
        areaType: AreaIdentifier.AreaType
    ): Instant?

    fun insert(config: List<Config>)
    fun update(config: List<Config>)
    fun delete(ids: Set<String>)

    /**
     * Gets all contained area identifiers for a specific [resource] and an optional [type]
     */
    fun getContentByResource(
        resource: AreaResourceIdentifier.Resource,
        type: AreaIdentifier.AreaType?
    ): List<Content>

    data class Content(
        @BsonId
        val type: AreaIdentifier.AreaType,
        val ids: List<String>
    )
}
