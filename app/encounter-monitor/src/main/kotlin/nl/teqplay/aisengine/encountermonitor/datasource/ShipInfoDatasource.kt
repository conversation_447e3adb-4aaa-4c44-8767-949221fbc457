package nl.teqplay.aisengine.encountermonitor.datasource

import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipType
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.encountermonitor.service.ShipInfoUpdateService
import nl.teqplay.csi.model.ship.info.component.ShipRole
import org.springframework.stereotype.Component

/**
 * Data source providing static information about vessels needed to support the detection of encounters. For proper
 * detection, the service needs to have the following information:
 * - the role of vessels,
 * - the type of vessels,
 * - whether a vessel is a sea vessel, and
 * - the transponder position/dimensions.
 * The first is downloaded regularly from CSI. The others are stored based on the incoming ais-diff data.
 */
@Component
class ShipInfoDatasource(
    private val roleDatasource: RoleDatasource
) {
    private val shipInfo = mutableMapOf<Int, CompactShipInfo>()

    /**
     * Download all the roles from CSI at startup.
     *
     * Scheduled updating is performed by [ShipInfoUpdateService.updateRoles]
     */
    @PostConstruct
    fun updateRoles() {
        roleDatasource.updateRoles()
    }

    /**
     * Get the [ShipRole] for the vessel with the given [mmsi]. Will return `ShipRole.NONE` if the vessel is not known.
     */
    fun getRoleForShip(mmsi: Int): ShipRole = roleDatasource.getRoleForShip(mmsi) ?: ShipRole.NONE

    /**
     * Update the ship info for the vessel with the given [mmsi].
     * If the vessel is not known, it will be added.
     * If the vessel is known, the values will be updated.
     */
    fun update(
        mmsi: Int,
        isSeaVessel: Boolean? = null,
        transponderPosition: TransponderPosition? = null,
        shipType: ShipType? = null,
    ) {
        val info = shipInfo.getOrPut(mmsi) {
            CompactShipInfo(
                shipType = shipType ?: ShipType.UNDEFINED,
                isSeaVessel = isSeaVessel ?: false,
                transponderPosition = transponderPosition
            )
        }

        isSeaVessel?.let { info.isSeaVessel = it }
        transponderPosition?.let { info.transponderPosition = it }
        shipType?.let { info.shipType = it }
    }

    fun getIsSeaVessel(mmsi: Int) = shipInfo[mmsi]?.isSeaVessel ?: false
    fun getTransponderPosition(mmsi: Int) = shipInfo[mmsi]?.transponderPosition
    fun getShipType(mmsi: Int) = shipInfo[mmsi]?.shipType ?: ShipType.UNDEFINED
}

/**
 * Compact version of the ship info needed to support the detection of encounters.
 *
 * Values are mutable for performance reasons.
 */
data class CompactShipInfo(
    var shipType: ShipType,
    var isSeaVessel: Boolean,
    var transponderPosition: TransponderPosition?
)
