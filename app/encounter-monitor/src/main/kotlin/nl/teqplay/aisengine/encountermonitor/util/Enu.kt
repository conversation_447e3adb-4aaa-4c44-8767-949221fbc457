package nl.teqplay.aisengine.encountermonitor.util

import nl.teqplay.skeleton.model.Location
import kotlin.math.cos
import kotlin.math.sin

/**
 * Class representing coordinates in a local frame, using east, north, up convention. The east/north plane is always
 * taken to be tangential to the ellipsoid.
 */
data class ENU(
    val e: Double,
    val n: Double,
    val u: Double
)

/**
 * Convert [ECEF] coordinates to the [ENU] reference frame defined by the rotation matrix [m]. The rotation matrix can
 * be obtained from [enuMatrix] and reused for multiple conversions.
 */
fun ECEF.toENU(m: List<List<Double>>) = ENU(
    m[0][0] * x + m[0][1] * y + m[0][2] * z,
    m[1][0] * x + m[1][1] * y + m[1][2] * z,
    m[2][0] * x + m[2][1] * y + m[2][2] * z
)

/**
 * Construct a rotation matrix to convert [ECEF] coordinates to an [ENU] frame centered around the given [location].
 */
fun enuMatrix(location: Location): List<List<Double>> {
    val lat = Math.toRadians(location.lat)
    val lon = Math.toRadians(location.lon)

    val sinLat = sin(lat)
    val cosLat = cos(lat)
    val sinLon = sin(lon)
    val cosLon = cos(lon)

    return listOf(
        listOf(-sinLon, cosLon, 0.0),
        listOf(-cosLon * sinLat, -sinLon * sinLat, cosLat),
        listOf(cosLon * cosLat, sinLon * cosLat, sinLat)
    )
}

/**
 * Given [ENU] coordinates in the frame centered around the given [location], convert them to [ECEF] coordinates.
 */
fun ENU.toECEF(location: Location): ECEF {
    val lat = Math.toRadians(location.lat)
    val lon = Math.toRadians(location.lon)

    val sinLat = sin(lat)
    val cosLat = cos(lat)
    val sinLon = sin(lon)
    val cosLon = cos(lon)

    return ECEF(
        -sinLon * e + -cosLon * sinLat * n + cosLon * cosLat * u,
        cosLon * e + -sinLon * sinLat * n + sinLon * cosLat * u,
        0 * e + cosLat * n + sinLat * u
    )
}

/**
 * Rotate the given [ENU] coordinates by [theta] degrees in the east/north plane.
 */
fun ENU.rotate(theta: Double): ENU {
    val thetaRadians = Math.toRadians(-theta)
    val sinTheta = sin(thetaRadians)
    val cosTheta = cos(thetaRadians)
    return ENU(
        cosTheta * e - sinTheta * n,
        sinTheta * e + cosTheta * n,
        u
    )
}

/**
 * Check if the lines defined by the [ENU] coordinate pairs a/b and c/d intersect.
 */
fun intersects(a: ENU, b: ENU, c: ENU, d: ENU): Boolean {
    // The calculation below defines the lines a/b and c/d using their first Bezier parameters, t and u.
    val denom = (a.e - b.e) * (c.n - d.n) -
        (a.n - b.n) * (c.e - d.e)

    // If the denominator is zero, the lines are parralel and don't intersect
    if (denom == 0.0) return false

    // Parameters t and u define at which point along each line the intersection lies. If either t or u are outside of
    // the range of [0, 1), there is no intersection.
    val t = ((a.e - c.e) * (c.n - d.n) - (a.n - c.n) * (c.e - d.e)) / denom
    val u = ((a.e - c.e) * (a.n - b.n) - (a.n - c.n) * (a.e - b.e)) / denom

    return t >= 0.0 && t < 1.0 && u >= 0.0 && u < 1.0
}
