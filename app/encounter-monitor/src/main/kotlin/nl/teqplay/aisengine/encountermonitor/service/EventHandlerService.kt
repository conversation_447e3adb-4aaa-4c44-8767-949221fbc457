package nl.teqplay.aisengine.encountermonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.encountermonitor.datasource.ShipInfoDatasource
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.EventStreamService.MessageContext
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsAisStreamOptions
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.stereotype.Component

private val LOG = KotlinLogging.logger {}

@Component
class EventHandlerService(
    private val encounterService: EncounterService,
    private val eventStreamService: EventStreamService,
    private val diffConsumerStream: NatsConsumerStream<AisDiffMessage>,
    private val shipInfoDatasource: ShipInfoDatasource
) {

    @PostConstruct
    fun startup() {
        LOG.info { "Starting stream consumer" }
        eventStreamService.consume(
            stream = diffConsumerStream,
            revents = ReventsAisStreamOptions(
                includeServiceVessels = true
            ),
            handler = ::process
        )
    }

    private fun process(diff: AisDiffMessage, message: MessageContext) {
        val mmsi = diff.mmsi
        val isSeaVessel = diff.imo.latest()?.let { it > 999_999 } ?: false
        val transponderPosition = diff.transponderPosition.latest()
        val shipType = diff.shipType.latest()

        shipInfoDatasource.update(mmsi, isSeaVessel, transponderPosition, shipType)

        val from = diff.location.old
        val to = diff.location.changed?.new

        // FIXME: we should also send just an update of the speed over ground
        if (to != null) {
            encounterService.processMovement(
                messageTime = diff.messageTime,
                ship = AisShipIdentifier(diff.mmsi, diff.imo.latest()),
                from = from,
                to = to,
                speed = diff.speedOverGround.latest(),
                courseOverGround = diff.courseOverGround.latest(),
                heading = diff.heading.latest()?.toFloat()
            )
        }
        message.ack()
    }
}
