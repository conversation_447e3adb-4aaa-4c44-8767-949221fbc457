package nl.teqplay.aisengine.encountermonitor.model

import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipType
import nl.teqplay.aisengine.encountermonitor.util.ECEF
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.skeleton.model.Location
import java.time.Instant

data class ShipState(
    val ship: AisShipIdentifier,
    val location: Location,
    val ecefLocation: ECEF,
    val speed: Float?,
    val courseOverGround: Float?,
    val heading: Float?,
    val messageTime: Instant,
    /** Exponential weighted moving average of the speed, using the time constant defined in ShipStateService. */
    val averageSpeed: Double,

    val role: ShipRole,
    val isSeaVessel: Boolean,
    val relevantSeaShip: Boolean,
    val relevantInlandShip: Boolean,
    val authority: Boolean,
    val type: ShipType
) {
    fun hasRole(role: ShipRole) = this.role == role

    inline val hasSpeed
        get() = speed != null && speed > 0.5f

    inline val hasLowOrNoSpeed
        get() = speed != null && speed <= 2.0f

    inline val hasNoSpeed
        get() = speed != null && speed <= 1.0f

    inline val slow
        get() = speed != null && speed <= 3.0f
}
