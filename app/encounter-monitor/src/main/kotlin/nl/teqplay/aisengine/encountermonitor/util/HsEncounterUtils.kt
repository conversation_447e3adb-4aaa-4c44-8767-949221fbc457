package nl.teqplay.aisengine.encountermonitor.util

import nl.teqplay.aisengine.encountermonitor.model.EncounterState
import nl.teqplay.aisengine.encountermonitor.model.ShipState
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.util.calculatePositiveAnglesDifference
import java.lang.Integer.max
import java.time.Duration
import java.time.Instant
import java.util.LinkedList

/**
 * Data storage class for high-speed encounters. Short traces of all ships in the vicinity of the service vessel are
 * stored in this data class, and used to determine which ship was closest to the service vessel when it has been
 * detected that services have been given.
 */
data class HsEncounter(
    /** The MMSI of the service vessel for this encounter. */
    val serviceMmsi: Int,
    /** Short traces of all vessels in the vicinity of the service vessel. */
    val vesselTraces: MutableMap<AisShipIdentifier, ShortTrace>,
    /** Encounter state for when an actual encounter has been detected. */
    var detectedEncounter: EncounterState? = null
)

/**
 * Data point for a vessel in a high-speed encounter.
 */
data class TracePoint(
    val location: Location,
    val ecefLocation: ECEF,
    val course: Double?,
    val speed: Double,
    val time: Instant
) {
    constructor(state: ShipState) : this(
        location = state.location,
        ecefLocation = state.ecefLocation,
        course = state.courseOverGround?.toDouble() ?: state.heading?.toDouble(),
        speed = state.speed?.toDouble() ?: 0.0,
        time = state.messageTime
    )
}

/**
 * A trace that is automatically limited to at most 15 minutes of data. Based on the characteristics of the trace, the
 * [servicesGiven] function can be used to detect if services were given.
 */
class ShortTrace(
    private val length: Duration = Duration.ofMinutes(15)
) {
    private val points = LinkedList<TracePoint>()

    /**
     * Add the given point to the trace, deleting old points to keep the trace length limited.
     */
    fun add(point: TracePoint) {
        if (points.isEmpty() || points.last.time != point.time) {
            if (points.isNotEmpty() && Duration.between(points.first.time, point.time) > length) {
                points.removeFirst()
            }
            points.add(point)
        }
    }

    /**
     * Detect based on the characteristics of the trace if services were given. This function looks for a sharp turn
     * followed by acceleration, and returns the time of the turn.
     */
    fun servicesGiven(): Instant? {
        val pts = points.toList()

        // We're going to traverse backwards through the trace, examining a window of points at a time. The start of the
        // window is at i, the end at j
        var i = pts.size - 1
        var j = pts.size - 1

        // We will do this until the start of the window is before the start of the trace
        while (i >= 0) {
            // Find a position i in the list that is 4 minutes before j. In the last iteration, i will be negative, this
            // will stop the while loop.
            while (i >= 0 && Duration.between(pts[i].time, pts[j].time) < Duration.ofMinutes(4)) {
                i--
            }

            // In the window, we're going to look for a sharp turn and for acceleration
            var sharpTurn: Instant? = null
            var accel: Instant? = null

            // Iterate over the points in the time window
            for (k in Integer.max(i, 0) until j) {
                val course1 = pts[k].course
                val course2 = pts[k + 1].course

                if (sharpTurn == null &&
                    course1 != null &&
                    course2 != null &&
                    calculatePositiveAnglesDifference(course1, course2) > 40
                ) {
                    // Record the time of the first sharp turn in the window
                    sharpTurn = pts[k].time
                }
                if (accel == null && pts[k + 1].speed - pts[k].speed > 10.0) {
                    // And record the time of the first acceleration in the window
                    accel = pts[k].time
                }
            }

            // And as a fallback, we calculate the turn angle from begin to end of the segment
            val a = pts[max(i, 0)].course
            val b = pts[j].course
            val turnInSegment = if (a != null && b != null) calculatePositiveAnglesDifference(a, b) else null

            if (sharpTurn != null && accel != null && !accel.isBefore(sharpTurn)) {
                // If the ship turned sharply and accelerated after that, return the time of the turn
                return sharpTurn
            } else if (turnInSegment != null && turnInSegment > 40 && accel != null) {
                // As fallback, if the ship turned gradually in the segment, return the time of the acceleration
                return accel
            }
            j--
        }

        return null
    }

    /**
     * Return the trace point (if any) closest to the given time [t].
     */
    fun atTime(t: Instant): TracePoint? {
        if (points.isEmpty()) return null

        val iter = points.iterator()
        var prev = iter.next()
        while (iter.hasNext()) {
            val cur = iter.next()

            if (prev.time == t || cur.time == t || prev.time.isBefore(t) && t.isBefore(cur.time)) {
                return if (Duration.between(prev.time, t) < Duration.between(t, cur.time)) prev
                else cur
            }

            prev = cur
        }
        return prev
    }
}
