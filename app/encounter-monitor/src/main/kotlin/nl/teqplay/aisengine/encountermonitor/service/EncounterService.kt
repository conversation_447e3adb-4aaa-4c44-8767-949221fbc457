package nl.teqplay.aisengine.encountermonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import nl.teqplay.aisengine.encountermonitor.distanceInMeters
import nl.teqplay.aisengine.encountermonitor.longrangeDistanceInMeters
import nl.teqplay.aisengine.encountermonitor.longrangePilotVessels
import nl.teqplay.aisengine.encountermonitor.model.EncounterState
import nl.teqplay.aisengine.encountermonitor.model.EncounterState.EncounterSubtype
import nl.teqplay.aisengine.encountermonitor.model.EncounterState.Order
import nl.teqplay.aisengine.encountermonitor.model.ShipState
import nl.teqplay.aisengine.encountermonitor.properties.EncounterMonitorProperties
import nl.teqplay.aisengine.encountermonitor.service.EncounterService.Companion.MaxDeltaT.fastMoving
import nl.teqplay.aisengine.encountermonitor.service.EncounterService.Companion.MaxDeltaT.slowMoving
import nl.teqplay.aisengine.encountermonitor.service.poma.PomaService
import nl.teqplay.aisengine.encountermonitor.util.ECEF
import nl.teqplay.aisengine.encountermonitor.util.HsEncounter
import nl.teqplay.aisengine.encountermonitor.util.ShortTrace
import nl.teqplay.aisengine.encountermonitor.util.TracePoint
import nl.teqplay.aisengine.encountermonitor.util.encounterProbability
import nl.teqplay.aisengine.encountermonitor.util.toECEF
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.interfaces.EncounterMetadata
import nl.teqplay.aisengine.event.model.EncounterEndEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.event.model.encountermetadata.BoatmanEncounterMetadata
import nl.teqplay.aisengine.event.model.encountermetadata.PilotEncounterMetadata
import nl.teqplay.aisengine.event.model.encountermetadata.STSEncounterMetadata
import nl.teqplay.aisengine.event.model.encountermetadata.TugEncounterMetadata
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.util.pointInPolygon
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import kotlin.math.abs
import kotlin.math.ceil

private val LOG = KotlinLogging.logger {}

/**
 * Main service that detects encounters based on ship movements, and processes them into events that are sent to the
 * bus.
 */
@Component
class EncounterService(
    private val detectionService: DetectionService,
    private val encounterStateService: EncounterStateService,
    private val eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry,
    private val shipStateService: ShipStateService,
    private val pomaService: PomaService,
    encounterMonitorProperties: EncounterMonitorProperties
) {
    companion object {

        /**
         * Bucket radius to use when searching for long-range encounters. For the normal encounters, searching in the
         * bucket of the current vessel position and all adjacent buckets will cover all encounters in the configured
         * encounter distance (based on the bucket size). To avoid also having to have buckets sized for the long-range
         * encounter distance, we search in a bigger neighborhood.
         */
        private val longrangeBucketRadius = ceil(longrangeDistanceInMeters / distanceInMeters).toInt()

        /**
         * Pilot zone of NLRTM.
         */
        private val PILOT_AREA_RTM = Polygon(
            listOf(
                Location(52.02253891247376, 3.975550434156976),
                Location(52.11672727186411, 3.99727639149144),
                Location(52.11236369484518, 3.796620598493203),
                Location(52.09434294350063, 3.766826267411203),
                Location(52.05919706339825, 3.724571548074724),
                Location(52.03884468591866, 3.706133720384544),
                Location(52.00007221380937, 3.673270019952799),
                Location(51.9671668121736, 3.654418256828649),
                Location(51.94789443036685, 3.651097957431984),
                Location(51.9532279603909, 3.690399293282891),
                Location(51.95773480709759, 3.912097016494467),
                Location(52.02253891247376, 3.975550434156976)
            )
        )

        private object Timeout {
            val SHORTEST: Duration = Duration.ofMinutes(1)
            val SHORT: Duration = Duration.ofSeconds(90)
            val NORMAL: Duration = Duration.ofMinutes(3)
            val LONG: Duration = Duration.ofMinutes(6)
        }

        /**
         * If the timestamp of the state of two ships is above this threshold, no encounter detection is being
         * performed. The [slowMoving] value is used when one of the two ships is not moving, the [fastMoving] threshold
         * is used when both ships are moving.
         */
        private object MaxDeltaT {
            val slowMoving: Duration = Duration.ofMinutes(60)
            val fastMoving: Duration = Duration.ofMinutes(2)
        }
    }

    /**
     * Buckets for efficient geospatial access to encounter states for ships based on ECEF position. Each bucket is a
     * cube equal to the detection distance, and using a cube and its 28 neighbouring cubes all encounters with a
     * radius of the cube size can be detected.
     */
    private val buckets = mutableMapOf<BucketIndex, MutableSet<Int>>()
    private val shipMap = ConcurrentHashMap<Int, ShipState>()

    /**
     * This map keeps state for high-speed encounters, and is indexed on the mmsi of the service vessel.
     */
    private val hsEncounters = ConcurrentHashMap<Int, HsEncounter>()

    private val registry = MetricRegistry.of<EncounterService>(meterRegistry)

    private val counterTotalMsgIn = registry.createGauge(Metric.MESSAGE_COUNT_INPUT, AtomicLong())
    private val counterTotalMsgOut = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong())

    private val ongoingEncounters = registry.createGauge(
        "ongoing.encounters.encounterservice",
        AtomicLong(encounterStateService.ongoingCount().toLong()),
        Tag.of("type", "normal")
    )
    private val ongoingHsEncounters = registry.createGauge(
        "ongoing.encounters.encounterservice",
        AtomicLong(),
        Tag.of("type", "highspeed")
    )

    /**
     * Latest message timestamp received. Used to expire [shipMap] entries that are too old to generate encounters.
     */
    private var latestTimestamp = Instant.EPOCH

    /**
     * Do initial processing of a [ship] that has moved [from] one location [to] another location.
     */
    fun processMovement(
        messageTime: Instant,
        ship: AisShipIdentifier,
        from: Location,
        to: Location,
        speed: Float?,
        courseOverGround: Float?,
        heading: Float?
    ) {
        // Calculate the old and new coordinates, and the old and new bucket indices
        val fromECEF = from.toECEF()
        val toECEF = to.toECEF()
        val fromIdx = toBucketIndex(fromECEF)
        val toIdx = toBucketIndex(toECEF)

        val oldState = shipMap[ship.mmsi]
        val state = shipStateService.getState(oldState, ship, messageTime, to, toECEF, speed, courseOverGround, heading)

        updateBuckets(fromIdx, toIdx, oldState, ship)

        shipMap[state.ship.mmsi] = state

        // Finally, trigger an encounter detection for this ship
        detectEncounters(toIdx, state)

        latestTimestamp = maxOf(messageTime, latestTimestamp)

        counterTotalMsgIn.getAndIncrement()
    }

    private fun updateBuckets(
        fromIdx: BucketIndex,
        toIdx: BucketIndex,
        oldState: ShipState?,
        ship: AisShipIdentifier
    ) {
        // ship moved between buckets
        if (oldState == null || fromIdx != toIdx) {
            // Get the bucket in which the ship is currently kept
            val currentBucket = buckets[fromIdx]

            if (currentBucket != null) {
                // Remove the state, if it moved between buckets
                currentBucket.remove(ship.mmsi)

                // Clean up bucket if it got emptied
                if (currentBucket.isEmpty()) {
                    buckets.remove(fromIdx)
                }
            }

            buckets.getOrPut(toIdx, ::mutableSetOf).add(ship.mmsi)
        }
    }

    private fun detectEncounters(
        idx: BucketIndex,
        ship: ShipState
    ) {
        // Detect any new encounters
        val currentEncounters = encounterStateService.get(ship.ship.mmsi)
        val newEncounterMap = mutableMapOf<Int, EncounterState>()

        val (radius, encounterDistanceInMeters) = when (ship.ship.mmsi) {
            in longrangePilotVessels -> longrangeBucketRadius to longrangeDistanceInMeters
            else -> 1 to distanceInMeters
        }

        forOtherShips(idx, radius) { otherShipMmsi ->
            // Skip self-encounters
            if (otherShipMmsi == ship.ship.mmsi) return@forOtherShips

            // Skip encounters when not initiated by a long-range pilot
            if (otherShipMmsi in longrangePilotVessels) return@forOtherShips

            val otherShip = shipMap[otherShipMmsi] ?: return@forOtherShips

            val d = ship.ecefLocation.distanceTo(otherShip.ecefLocation)
            if (d <= encounterDistanceInMeters) {
                // If it's within encounter distance and not already in the list of current encounters, create a new
                // encounter and store it in the encounter map
                if (otherShip.ship.mmsi !in currentEncounters.keys) {
                    // Create a new state object for the encounter
                    val encounter = EncounterState(
                        ship1 = ship.ship,
                        ship2 = otherShip.ship,
                        ecefLocation1 = ship.ecefLocation,
                        ecefLocation2 = otherShip.ecefLocation,
                        speed1 = ship.speed?.toDouble() ?: 0.0,
                        speed2 = otherShip.speed?.toDouble() ?: 0.0
                    )
                    newEncounterMap[otherShip.ship.mmsi] = encounter
                }
            }
        }

        newEncounterMap.forEach { (otherShipMmsi, encounter) -> updateEncounter(otherShipMmsi, encounter, ship) }
        currentEncounters.forEach { (otherShipMmsi, encounter) -> updateEncounter(otherShipMmsi, encounter, ship) }

        // If there are no encounters, but there's an ongoing HS encounter for this service vessel, update the hs
        // encounter with the position of the service vessel only
        if (newEncounterMap.isEmpty() && currentEncounters.isEmpty() && hsEncounters.containsKey(ship.ship.mmsi)) {
            processHsEncounter(null, ship)
        }
    }

    private fun updateEncounter(
        otherShipMmsi: Int,
        encounter: EncounterState,
        ship: ShipState
    ) {
        // To be able to process the encounters fully, we need the latest ShipState of the other ship. The Encounter
        // object only has the mmsi, so look up the full ShipState
        val otherShip = shipMap[otherShipMmsi]

        if (encounter.ship1 == ship.ship) {
            encounter.ecefLocation1 = ship.ecefLocation
            encounter.speed1 = ship.speed?.toDouble() ?: 0.0
        } else {
            encounter.ecefLocation2 = ship.ecefLocation
            encounter.speed2 = ship.speed?.toDouble() ?: 0.0
        }

        // Do the actual processing of the encounter, which potentially fires an encounter event
        // If other ship doesn't exist currently, we'll have to wait to receive the ship later
        if (otherShip != null) {
            processEncounter(ship, otherShip, encounter)
        }
    }

    /**
     * Convert a [pos]ition in ECEF to a bucket index, based on the [distanceInMeters] within encounters are to be
     * detected.
     */
    private fun toBucketIndex(pos: ECEF) = BucketIndex(
        ceil(pos.x / distanceInMeters).toInt(),
        ceil(pos.y / distanceInMeters).toInt(),
        ceil(pos.z / distanceInMeters).toInt()
    )

    /**
     * Based on the bucket [idx] and the surrounding buckets in the given [radius], loop over all the ships in those buckets.
     */
    private inline fun forOtherShips(
        idx: BucketIndex,
        radius: Int,
        apply: (Int) -> Unit
    ) {
        idx.neighborhood(radius) { nIdx ->
            buckets[nIdx]?.forEach { apply(it) }
        }
    }

    /**
     * Detection rules for encounters and their type and optional subtype. These rules (functions) are processed in
     * order, when the first function returns a match the processing is stopped.
     */
    private val detectionRules = listOfNotNull(
        DetectionRule(detectionService::isMovingTugEncounter, EncounterType.TUG),
        DetectionRule(detectionService::isSingaporePilotEncounter, EncounterType.PILOT, EncounterSubtype.PILOT_SGSIN),
        DetectionRule({ meets ->
            detectionService.isPilotEncounter(meets)
                ?.takeIf { pointInPolygon(PILOT_AREA_RTM, meets.first.location) }
        }, EncounterType.PILOT, EncounterSubtype.PILOT_ZONE),
        DetectionRule(detectionService::isPilotEncounter, EncounterType.PILOT),
        DetectionRule(detectionService::isLongrangePilotEncounter, EncounterType.PILOT, EncounterSubtype.PILOT_LONGRANGE),
        DetectionRule(detectionService::isWaterBoatEncounter, EncounterType.WATER),
        DetectionRule(detectionService::isBunkerEncounter, EncounterType.BUNKER),
        DetectionRule(detectionService::isAuthoritiesEncounter, EncounterType.AUTHORITY),
        DetectionRule(detectionService::isBoatmanEncounter, EncounterType.BOATMAN),
        DetectionRule(detectionService.detectorForRole(ShipRole.SWOG), EncounterType.SWOG),
        DetectionRule(detectionService.detectorForRole(ShipRole.FENDER), EncounterType.FENDER),
        DetectionRule(detectionService.detectorForRole(ShipRole.TENDER), EncounterType.TENDER),
        DetectionRule(detectionService.boardBoardDetectorForRole(ShipRole.WASTE, true), EncounterType.WASTE),
        DetectionRule(detectionService.boardBoardDetectorForRole(ShipRole.CRANE, false), EncounterType.CRANE),
        DetectionRule(detectionService::isLubesEncounter, EncounterType.LUBES),
        DetectionRule(detectionService::isSupplyBargeEncounter, EncounterType.BARGE_SUPPLY),
        DetectionRule(detectionService.boardBoardDetectorForRole(ShipRole.CARGOBARGE, false), EncounterType.BARGE_CARGO),
        DetectionRule(detectionService.boardBoardDetectorForRole(ShipRole.TANKERBARGE, false), EncounterType.BARGE_TANKER),
        DetectionRule(detectionService.boardBoardDetectorForRole(ShipRole.PUSHBARGE, false), EncounterType.BARGE_PUSH),
        DetectionRule(detectionService::isBargeWaterEncounter, EncounterType.BARGE_WATER)
            .takeUnless { encounterMonitorProperties.disableBargeBunkerAndBargeWater },
        DetectionRule(detectionService::isBargeBunkerEncounter, EncounterType.BARGE_BUNKER)
            .takeUnless { encounterMonitorProperties.disableBargeBunkerAndBargeWater },
        DetectionRule(detectionService::isTugWaitingDepartureEncounter, EncounterType.TUG_WAITING_DEPARTURE),
        DetectionRule(detectionService::isShipToShipEncounter, EncounterType.SHIP_TO_SHIP)
    )

    data class DetectionResult(
        val direction: Order?,
        val type: EncounterType?,
        val subType: EncounterSubtype?
    )

    /**
     * Given two ships [ship] and [otherShip], execute the [detectionRules] and determine if the encounter has started,
     * ended, or is still ongoing.
     */
    private fun processEncounter(ship: ShipState, otherShip: ShipState, encounter: EncounterState) {
        val detectionResult = lazy {
            // Execute the detection rules as a sequence, and stop processing if the first rule matches
            val meets: ShipMeetsShip = ship to otherShip
            val match = detectionRules.asSequence()
                .map { rule -> rule.detector(meets) to rule }
                .find { it.first != null }

            val detectionResult = DetectionResult(
                direction = match?.first,
                type = match?.second?.type,
                subType = match?.second?.subType
            )

            // apply the direction to the encounter, if it hasn't been started yet, as we didn't have it before
            if (detectionResult.direction != null && encounter.start == null) encounter.order = detectionResult.direction

            return@lazy detectionResult
        }

        // Based on the type of encounter, do the actual processing. Currently, we only distinguish between normal and
        // high-speed encounters.
        val existingOrNewType = encounter.type ?: detectionResult.value.type
        val existingOrNewSubType = encounter.subType ?: detectionResult.value.subType

        val (mainVessel, service) = getMainAndServiceVessel(encounter, ship, otherShip)

        when {
            existingOrNewType == EncounterType.PILOT && existingOrNewSubType == EncounterSubtype.PILOT_SGSIN -> {
                encounter.type = existingOrNewType
                encounter.subType = existingOrNewSubType
                processHsEncounter(mainVessel, service)
            }
            else -> {
                processNormalEncounter(mainVessel, service, encounter, detectionResult)
            }
        }
    }

    /**
     * Process a normal encounter between a vessel and a service vessel:
     * - If the distance is below the start distance for the encounter type, the start time is recorded
     * - If the encounter is established (has been going on for long enough) fire a start event
     * - If the exit distance is exceeded, reset the encounter state and possibly fire an end event
     */
    private fun processNormalEncounter(
        mainVessel: ShipState,
        service: ShipState,
        encounter: EncounterState,
        detectionResult: Lazy<DetectionResult>
    ) {
        // Use the newest message as "current" time to do reasoning about duration of the encounter etc
        val messageTime = maxOf(mainVessel.messageTime, service.messageTime)

        // already fired a start event, check when to end the encounter
        if (encounter.startEventId != null) {
            // we already started an event, check when we should send an end event
            if (detectionService.checkExitCriteria(encounter, mainVessel, service)) {
                val end = encounter.end
                if (end == null) {
                    // Start the timer for ending the encounter
                    encounter.end(messageTime)
                } else if (encounter.finished(messageTime)) {
                    // Store the type because it gets reset when firing an end event
                    val type = encounter.type

                    // When the encounter is the ship-to-ship type, we fire an event for both parties.
                    val fireForOther = encounter.type == EncounterType.SHIP_TO_SHIP

                    // The exit criteria have held long enough for us to be sure that the encounter has ended, fire the
                    // end event
                    encounter.fireEndEvent(
                        mainVessel.location,
                        messageTime,
                        detectionService.checkValidity(encounter, mainVessel, service),
                        fireForOther
                    )
                    ongoingEncounters.getAndDecrement()
                    // Trigger a TUG start event to bypass the timeout between waiting departure and actual tug event
                    if (type == EncounterType.TUG_WAITING_DEPARTURE) {
                        startTugEvent(mainVessel, service, encounter, detectionResult, end)
                    }
                }
            } else {
                // Reset end timer, the exit criteria no longer held
                encounter.end = null
            }
        } else if (encounter.start == null) {
            encounter.startEvent(mainVessel, service, detectionResult, messageTime)
        } else if (detectionService.checkExitCriteria(encounter, mainVessel, service)) {
            encounter.reset()
        } else if (encounter.established(messageTime)) {
            // No start event has been fired, but enough time has elapsed by now to make this a valid encounter.
            // For TUG encounters, we do one final check to see if the vessels are now moving in a similar
            // direction, and then we fire the event.
            if (detectionResult.value.type != EncounterType.TUG ||
                abs((service.courseOverGround ?: 0.0f) - (mainVessel.courseOverGround ?: 0.0f)) < 90
            ) {
                // When the encounter is a ship-to-ship type we fire the event for both parties.
                val fireForOther = encounter.type == EncounterType.SHIP_TO_SHIP

                encounter.fireStartEvent(mainVessel.location, fireForOther)
                ongoingEncounters.getAndIncrement()
            }
        } else if (encounter.type != null && !detectionService.checkStartDistance(encounter.type!!, encounter.subType, mainVessel, service)) {
            // Reset encounter if the start distance is not maintained long enough
            encounter.reset()
        }
    }

    /**
     * Process high-speed encounters, currently only for pilots in Singapore.
     */
    private fun processHsEncounter(
        mainVessel: ShipState?,
        service: ShipState
    ) {
        val hsEncounter = hsEncounters.computeIfAbsent(service.ship.mmsi) { mmsi ->
            HsEncounter(mmsi, ConcurrentHashMap())
        }

        val present = mainVessel != null && hsEncounter.vesselTraces.keys.contains(mainVessel.ship)

        if (mainVessel != null && hsEncounter.detectedEncounter?.start != null) {
            // An actual encounter was detected. After 5 minutes of hold-off time, we fire the end event, and we remove
            // the encounter from the map. This prevents detecting multiple encounters.
            val detectedEncounter = requireNotNull(hsEncounter.detectedEncounter)
            if (Duration.between(detectedEncounter.start, service.messageTime) > Duration.ofMinutes(5)) {
                hsEncounters.remove(service.ship.mmsi)
                detectedEncounter.fireEndEvent(
                    mainVessel.location,
                    maxOf(mainVessel.messageTime, service.messageTime),
                    detectionService.checkValidity(detectedEncounter, mainVessel, service)
                )
                ongoingHsEncounters.getAndDecrement()
            }
        } else if (present && mainVessel != null && detectionService.checkExitDistance(EncounterType.PILOT, EncounterSubtype.PILOT_SGSIN, mainVessel, service)) {
            // The main vessel for this call has moved too far away, remove the trace from the HS encounter state
            hsEncounter.vesselTraces.remove(mainVessel.ship)

            if (hsEncounter.vesselTraces.size <= 1) {
                // There's only the service vessel left, so remove the entire encounter
                hsEncounters.remove(service.ship.mmsi)
            }
        } else {
            // This is either a new potential encounter, or an update for an ongoing potential one. We create a
            // ShortTrace for the main vessel and for the service vessel. Then, we add the points to the traces.
            // Duplicates are removed by the ShortTrace class, we don't need to worry about that.
            if (mainVessel != null) {
                hsEncounter.vesselTraces
                    .computeIfAbsent(mainVessel.ship) { _ -> ShortTrace() }
                    .add(TracePoint(mainVessel))
            }

            val serviceTrace = hsEncounter.vesselTraces.computeIfAbsent(service.ship) { _ -> ShortTrace() }
            serviceTrace.add(TracePoint(service))

            // Detect if services were given based on trace characteristics (sharp turn followed by acceleration)
            val serviceTime = serviceTrace.servicesGiven()
            // And extract the location at that time
            val serviceLocation = serviceTime?.let { serviceTrace.atTime(it) }

            if (serviceTime != null && serviceLocation != null) {
                // If the service vessel indeed gave services, find the main vessel that was closest at that time
                // Start by filtering out the service vessel trace
                val closest = hsEncounter.vesselTraces.filter { it.key != service.ship }
                    // Determine the location of the main vessel at the service time
                    .mapValues { it.value.atTime(serviceTime) }
                    // Remove any null values, for vessels for which no value at the given time could be determined
                    .mapNotNull { (shipIdentifier, tracePoint) ->
                        tracePoint?.let {
                            val distance = serviceLocation.ecefLocation.distanceTo(tracePoint.ecefLocation)
                            Triple(shipIdentifier, tracePoint, distance)
                        }
                    }
                    // And find the main vessel that had the lowest distance to the service vessel
                    .minByOrNull { (_, _, distance) -> distance }
                    // And ensure the closest vessel is not outside the exit distance
                    ?.takeUnless { (_, _, distance) -> detectionService.checkExitDistance(EncounterType.PILOT, EncounterSubtype.PILOT_SGSIN, mainVessel = null, service, distance) }

                if (closest != null) {
                    // If we have the closest main vessel, start an encounter. Note that the encounter that is passed in
                    // can be a different one, so we have to create a new encounter here

                    // Store the encounter in the HS encounter state for later reference, and fire a start event
                    hsEncounter.detectedEncounter = EncounterState(
                        ship1 = closest.first,
                        ship2 = service.ship,
                        speed1 = closest.second.speed,
                        speed2 = service.speed?.toDouble() ?: 0.0,
                        ecefLocation1 = closest.second.ecefLocation,
                        ecefLocation2 = service.ecefLocation,
                        start = serviceTime,
                        type = EncounterType.PILOT,
                        subType = EncounterSubtype.PILOT_SGSIN,
                        probability = encounterProbability(closest.second.time, service.messageTime)
                    ).also {
                        it.fireStartEvent(serviceLocation.location)
                        ongoingHsEncounters.getAndIncrement()
                    }
                }
            }
        }
    }

    /**
     * Returns a pair of the main vessel to the service vessel, based on the [encounter].
     */
    private fun getMainAndServiceVessel(
        encounter: EncounterState,
        ship: ShipState,
        otherShip: ShipState
    ) = if (ship.ship.mmsi == encounter.mainVessel.mmsi) ship to otherShip else otherShip to ship

    /**
     * Expire [shipMap] entries that are older than the [MaxDeltaT.slowMoving] threshold, compared to the newest data
     * that we have received, except if they are part of an ongoing encounter. New encounters can never be detected
     * anymore for such entries, so we can safely discard them to save memory.
     *
     * Scheduling happens by [EncounterExpireService.expireShipState]
     */
    fun expireShipState() {
        val expired = shipMap.values
            .filter { Duration.between(it.messageTime, latestTimestamp) > slowMoving }
            .filter { encounterStateService.get(it.ship.mmsi).isEmpty() }
            .onEach { shipState ->
                shipMap.remove(shipState.ship.mmsi)
                val bucketIdx = toBucketIndex(shipState.ecefLocation)
                val bucket = buckets[bucketIdx]

                if (bucket != null) {
                    bucket.remove(shipState.ship.mmsi)
                    if (bucket.isEmpty()) {
                        buckets.remove(bucketIdx)
                    }
                }
            }.size
        LOG.info { "Expired $expired old ship states" }
    }

    /**
     * Convenience function to store the relevant state when starting an encounter.
     */
    private fun EncounterState.start(
        startTime: Instant,
        order: Order,
        type: EncounterType,
        subType: EncounterSubtype?,
        probability: Double
    ) {
        startEventId = null
        start = startTime
        this.order = order
        this.type = type
        this.subType = subType
        this.probability = probability
        encounterStateService.persist(this)
    }

    /**
     * Convenience function to store the relevant state when ending an encounter.
     */
    private fun EncounterState.end(endTime: Instant) {
        end = endTime
        encounterStateService.persist(this)
    }

    /**
     * Support function that checks if the encounter has been ongoing for long enough for it to be established.
     */
    private fun EncounterState.established(messageTime: Instant) =
        checkEndurance(start ?: messageTime, messageTime)

    /**
     * Support function that checks if the encounter has been ended for long enough for it to be finished.
     */
    private fun EncounterState.finished(messageTime: Instant) =
        checkEndurance(end ?: messageTime, messageTime)

    /**
     * Support function to check if the specified [startTime] and [endTime] are far enough for the given encounter type
     * for it to be established or finished.
     */
    private fun EncounterState.checkEndurance(startTime: Instant, endTime: Instant): Boolean {
        val endurance = Duration.between(startTime, endTime)

        return when (type) {
            EncounterType.PILOT -> endurance > Timeout.SHORTEST

            EncounterType.TENDER -> endurance > Timeout.SHORT

            EncounterType.TUG,
            EncounterType.BOATMAN,
            EncounterType.BUNKER,
            EncounterType.AUTHORITY,
            EncounterType.WASTE,
            EncounterType.SWOG,
            EncounterType.WATER,
            EncounterType.FENDER,
            EncounterType.CRANE,
            EncounterType.BARGE_WATER,
            EncounterType.BARGE_BUNKER,
            EncounterType.TUG_WAITING_DEPARTURE -> endurance > Timeout.NORMAL

            EncounterType.SHIP_TO_SHIP,
            EncounterType.BARGE_SUPPLY,
            EncounterType.LUBES,
            EncounterType.BARGE_CARGO,
            EncounterType.BARGE_TANKER,
            EncounterType.BARGE_PUSH -> endurance > Timeout.LONG

            else -> endurance > Timeout.NORMAL
        }
    }

    /**
     * Generate an event id, store it in the encounter, and fire the start event.
     *
     * @param fireForOther whether we fire the event for the other ship as well.
     */
    private fun EncounterState.fireStartEvent(location: Location, fireForOther: Boolean = false) {
        val t = type
        if (t == null) {
            LOG.warn { "Cannot fire start event for unknown encounter type" }
        } else {
            startMetadata = getStartMetadata(location)
            val event = EncounterStartEvent(
                _id = UUID.randomUUID().toString(),
                ship = mainVessel,
                otherShip = serviceVessel,
                encounterType = t,
                location = location,
                actualTime = start ?: Instant.now(),
                probability = probability ?: 0.0,
                metadata = startMetadata,
                mainMoving = mainVesselSpeed > 0.5,
                serviceMoving = serviceVesselSpeed > 0.5
            )
            startEventId = event._id
            LOG.debug { "START: $type encounter between ${mainVessel.mmsi} and ${serviceVessel.mmsi}" }
            eventStreamService.publish(event)
            encounterStateService.persist(this)
            counterTotalMsgOut.getAndIncrement()

            // If enabled, also fire an event for the other ship.
            if (fireForOther) { fireOtherShipStartEvent(event) }
        }
    }

    /**
     * Fire an event for the other ship by copying the event but changing the ship-specific fields.
     */
    private fun EncounterState.fireOtherShipStartEvent(event: EncounterStartEvent) {
        val otherEvent = event.copy(
            _id = UUID.randomUUID().toString(),
            ship = event.otherShip,
            otherShip = event.ship
        )

        otherStartEventId = otherEvent._id
        eventStreamService.publish(otherEvent)
        counterTotalMsgOut.getAndIncrement()
    }

    /**
     * Fire an end event, and clear the encounter state.
     *
     * @param fireForOther whether we fire the event for the other ship as well.
     */
    private fun EncounterState.fireEndEvent(
        location: Location,
        messageTime: Instant,
        wasValid: Boolean,
        fireForOther: Boolean = false
    ) {
        val t = type
        if (t == null) {
            LOG.warn { "Cannot fire end event for unknown encounter type" }
        } else {
            val event = EncounterEndEvent(
                _id = UUID.randomUUID().toString(),
                startEventId = startEventId,
                ship = mainVessel,
                otherShip = serviceVessel,
                encounterType = t,
                location = location,
                actualTime = end ?: messageTime,
                probability = probability ?: 0.0,
                valid = wasValid,
                metadata = getEndMetadata(location)
            )
            LOG.debug { "END: $type encounter between ${mainVessel.mmsi} and ${serviceVessel.mmsi}" }
            eventStreamService.publish(event)
            counterTotalMsgOut.getAndIncrement()

            if (fireForOther) { fireOtherShipEndEvent(event) }
        }
        reset()
    }

    private fun EncounterState.fireOtherShipEndEvent(event: EncounterEndEvent) {
        val otherEvent = event.copy(
            _id = UUID.randomUUID().toString(),
            startEventId = otherStartEventId,
            ship = event.otherShip,
            otherShip = event.ship
        )

        eventStreamService.publish(otherEvent)
        counterTotalMsgOut.getAndIncrement()
    }

    /**
     * Clear the encounter state.
     */
    private fun EncounterState.reset() {
        startEventId = null
        start = null
        end = null
        type = null
        subType = null
        encounterStateService.remove(this)
    }

    private fun EncounterState.getStartMetadata(location: Location): EncounterMetadata? =
        when (type) {
            EncounterType.TUG ->
                TugEncounterMetadata(
                    hasSimultaneousTugEncounter = hasSimultaneousTugEncounter()
                )
            EncounterType.BOATMAN ->
                BoatmanEncounterMetadata(
                    hasSimultaneousTugEncounter = hasSimultaneousTugEncounter(),
                    nrOfBoatmen = currentBoatmenEncounters().size + 1,
                    arrival = (shipMap[mainVessel.mmsi]?.averageSpeed ?: 0.0) > 1.0f
                )
            EncounterType.PILOT -> {
                val ship = shipMap[mainVessel.mmsi]

                val heading = when {
                    ship?.heading != null -> ship.heading
                    ship?.speed != null && ship.speed > 1.0f -> ship.courseOverGround
                    else -> null
                }

                PilotEncounterMetadata(heading = heading)
            }
            EncounterType.SHIP_TO_SHIP -> STSEncounterMetadata(pomaService.getAreaId(location))
            else -> null
        }

    private fun EncounterState.getEndMetadata(location: Location): EncounterMetadata? =
        when (type) {
            EncounterType.TUG ->
                TugEncounterMetadata(
                    hasSimultaneousTugEncounter = hasSimultaneousTugEncounter()
                )
            EncounterType.BOATMAN ->
                BoatmanEncounterMetadata(
                    hasSimultaneousTugEncounter = hasSimultaneousTugEncounter(),
                    nrOfBoatmen = currentBoatmenEncounters().size,
                    arrival = (startMetadata as? BoatmanEncounterMetadata)?.arrival ?: false
                )
            EncounterType.PILOT ->
                PilotEncounterMetadata(
                    heading = shipMap[mainVessel.mmsi]?.heading
                )
            EncounterType.SHIP_TO_SHIP -> STSEncounterMetadata(pomaService.getAreaId(location))
            else -> null
        }

    private fun EncounterState.hasSimultaneousTugEncounter(): Boolean =
        encounterStateService.get(mainVessel.mmsi).values
            .find {
                it.serviceVessel.mmsi != serviceVessel.mmsi &&
                    it.type == EncounterType.TUG &&
                    it.startEventId != null
            } != null

    private fun EncounterState.currentBoatmenEncounters() =
        encounterStateService.get(mainVessel.mmsi).values
            .filter {
                it.type == EncounterType.BOATMAN &&
                    it.startEventId != null
            }

    /**
     * Data class for the key of the bucket map, which has the [x], [y] and [z] coordinates of the buckets.
     */
    private data class BucketIndex(
        val x: Int,
        val y: Int,
        val z: Int
    ) {
        /**
         * Get the neighboring buckets of this one as a sequence.
         */
        inline fun neighborhood(n: Int, apply: (BucketIndex) -> Unit) {
            for (i in -n..n)
                for (j in -n..n)
                    for (k in -n..n)
                        apply(BucketIndex(x + i, y + j, z + k))
        }
    }

    /**
     * Storage class for encounter detection rules and their associated type and optional subtype.
     */
    private data class DetectionRule(
        val detector: (ShipMeetsShip) -> Order?,
        val type: EncounterType,
        val subType: EncounterSubtype? = null
    )

    /**
     * Initiates a vessel encounter event if specific conditions are met:
     * - Messages from both vessels are within the time threshold
     * - Vessels are within the required start distance
     * - Valid encounter direction and type are detected
     */
    private fun EncounterState.startEvent(mainVessel: ShipState, service: ShipState, detectionResult: Lazy<DetectionResult>, messageTime: Instant) {
        val threshold = when {
            mainVessel.hasNoSpeed || service.hasNoSpeed -> slowMoving
            else -> fastMoving
        }

        if (Duration.between(mainVessel.messageTime, service.messageTime).abs() > threshold) {
            return
        }

        // This is an encounter that has just started: a rule has matched, and we are within the appropriate
        // start distance. We record the start time, and wait until the duration of this type of encounter has
        // elapsed to fire the actual event.
        val encounterType = detectionResult.value.type
        val encounterSubType = detectionResult.value.subType

        if (encounterType != null && detectionService.checkStartDistance(encounterType, encounterSubType, mainVessel, service)) {
            this.start(
                startTime = messageTime,
                order = order,
                type = encounterType,
                subType = encounterSubType,
                probability = encounterProbability(mainVessel.messageTime, service.messageTime)
            )
        }
    }

    /**
     * Creates a new encounter state for a [EncounterType.TUG] event and starts it.
     */
    private fun startTugEvent(mainVessel: ShipState, service: ShipState, encounter: EncounterState, detectionResult: Lazy<DetectionResult>, messageTime: Instant) {
        val newEncounter = EncounterState(
            ship1 = encounter.ship1,
            ship2 = encounter.ship2,
            ecefLocation1 = encounter.ecefLocation1,
            ecefLocation2 = encounter.ecefLocation2,
            speed1 = encounter.speed1,
            speed2 = encounter.speed2,
            order = encounter.order,
            type = EncounterType.TUG
        )

        newEncounter.startEvent(mainVessel, service, detectionResult, messageTime)
    }
}

typealias ShipMeetsShip = Pair<ShipState, ShipState>
