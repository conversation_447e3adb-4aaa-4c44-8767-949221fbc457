package nl.teqplay.aisengine.encountermonitor.service

import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipType
import nl.teqplay.aisengine.encountermonitor.datasource.ShipInfoDatasource
import nl.teqplay.aisengine.encountermonitor.longrangePilotVessels
import nl.teqplay.aisengine.encountermonitor.model.EncounterState
import nl.teqplay.aisengine.encountermonitor.model.EncounterState.EncounterSubtype
import nl.teqplay.aisengine.encountermonitor.model.EncounterState.Order.NORMAL
import nl.teqplay.aisengine.encountermonitor.model.EncounterState.Order.REVERSE
import nl.teqplay.aisengine.encountermonitor.model.ShipState
import nl.teqplay.aisengine.encountermonitor.service.poma.PomaService
import nl.teqplay.aisengine.encountermonitor.util.canCatchUp
import nl.teqplay.aisengine.encountermonitor.util.distanceBetweenShips
import nl.teqplay.aisengine.encountermonitor.util.isAlongside
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.csi.model.ship.info.component.ShipRole.AUTHORITIES
import nl.teqplay.csi.model.ship.info.component.ShipRole.BOATMAN
import nl.teqplay.csi.model.ship.info.component.ShipRole.BUNKER
import nl.teqplay.csi.model.ship.info.component.ShipRole.CARGOBARGE
import nl.teqplay.csi.model.ship.info.component.ShipRole.COASTGUARD
import nl.teqplay.csi.model.ship.info.component.ShipRole.CUSTOMS
import nl.teqplay.csi.model.ship.info.component.ShipRole.LUBES
import nl.teqplay.csi.model.ship.info.component.ShipRole.PILOT
import nl.teqplay.csi.model.ship.info.component.ShipRole.PORTAUTHORITIES
import nl.teqplay.csi.model.ship.info.component.ShipRole.PORTPOLICE
import nl.teqplay.csi.model.ship.info.component.ShipRole.PUSHBARGE
import nl.teqplay.csi.model.ship.info.component.ShipRole.SUPPLYBARGE
import nl.teqplay.csi.model.ship.info.component.ShipRole.TANKERBARGE
import nl.teqplay.csi.model.ship.info.component.ShipRole.TUG
import nl.teqplay.csi.model.ship.info.component.ShipRole.WASTE
import nl.teqplay.csi.model.ship.info.component.ShipRole.WATER
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.util.pointInPolygon
import org.springframework.stereotype.Component
import java.time.Duration
import kotlin.math.abs

/**
 * Service containing the actual detection logic for encounters.
 *
 * This class contains a "mini-DSL" for succintly encoding the criteria for an encounter in a function returning the
 * [EncounterState.Order] for that encounter if it's a match, and `null` otherwise. The function is set up using `meets`,
 * one of [withRole] or [byPredicate], and finished with a [matching] block containing the actual logic.
 *
 * A simple example is [isWaterBoatEncounter], containing the following code block:
 * ```
 * meets withRole ShipRole.WATER matching {
 *     mainVessel.relevantSeaShip &&
 *     !mainVessel.hasSpeed &&
 *     mainVessel.getRole() !in serviceRoles
 * }
 * ```
 * `ship1 to ship2` (as `meets`) sets up the initial data structure. `withRole ShipRole.WATER` orders the two ships such that the
 * water boat is stored as `service`, and the other ship as `mainVessel`. If neither of the boats is a water boat,
 * `null` is returned, and the `matching` block is not executed. Otherwise, `matching` executes the given code block to
 * determine if the other criteria of the encounter hold. In this block, `mainVessel` refers to the main vessel in the
 * encounter, and `service` to the service vessel. Note that it is not necessary to check if `service` has the
 * `ShipRole.WATER` role, this check is already performed by the `withRole` operator.
 */
@Component
class DetectionService(
    private val shipInfoDatasource: ShipInfoDatasource,
    private val pomaService: PomaService,
) {
    companion object {
        private val SGSIN = Polygon(
            listOf(
                Location(1.419129, 103.277013),
                Location(0.9447814006874024, 103.4197998046875),
                Location(1.215271416559006, 104.20806884765625),
                Location(1.3175565929906294, 104.15605545043947),
                Location(1.3180714409118992, 104.09374237060548),
                Location(1.5461379995639477, 103.86749267578125),
                Location(1.419129, 103.277013)
            )
        )

        private val SGSIN_HARBOUR_SEA_PASSAGE = Polygon(
            listOf(
                Location(1.425612680447366, 103.3359275269673),
                Location(1.028696310456058, 103.400092576814),
                Location(1.332181217673046, 104.5311337085246),
                Location(1.697193353737318, 103.898885006569),
                Location(1.425612680447366, 103.3359275269673)
            )
        )

        private val SGSIN_PILOT_STATION_EAST = Polygon(
            listOf(
                Location(1.2578013441209204, 103.84256422519684),
                Location(1.257908606627928, 103.84203851222992),
                Location(1.2572167633803177, 103.84043991565704),
                Location(1.2564498361551697, 103.8406652212143),
                Location(1.2575492772121346, 103.84275197982788),
                Location(1.2578013441209204, 103.84256422519684)
            )
        )

        private val SGSIN_PILOT_STATION_WEST = Polygon(
            listOf(
                Location(1.2904089414305158, 103.76067101955412),
                Location(1.2919213232171995, 103.7615293264389),
                Location(1.292371819745637, 103.76072466373444),
                Location(1.290955973244459, 103.75982880592346),
                Location(1.2904089414305158, 103.76067101955412)
            )
        )

        private val NLRTM_KTMKADE8_EXCLUDE = Polygon(
            listOf(
                Location(51.89707453399229, 4.355042639388074),
                Location(51.8963990215147, 4.355361820653812),
                Location(51.89715432226551, 4.359989178468455),
                Location(51.89786068512632, 4.359706680804953),
                Location(51.89707453399229, 4.355042639388074)
            )
        )

        private val NLRTM_ETT_JETTY_EXCLUDE = Polygon(
            listOf(
                Location(51.92676161835748, 4.197422213940667),
                Location(51.92813080668963, 4.19921290983841),
                Location(51.93209687311184, 4.191181700313614),
                Location(51.93076067106389, 4.189373168171848),
                Location(51.92676161835748, 4.197422213940667)
            )
        )

        private val BEANR_512BC_EXCLUDE = Polygon(
            listOf(
                Location(51.28767661509368, 4.339177798963041),
                Location(51.2882012019371, 4.34076559405193),
                Location(51.28962046913031, 4.339567297765631),
                Location(51.28936285448569, 4.338735930876061),
                Location(51.29039333473448, 4.337837145246899),
                Location(51.29015912821404, 4.337028239425353),
                Location(51.28767661509368, 4.339177798963041)
            )
        )

        /**
         * Threshold distances for starting an encounter of various types in meters.
         */
        private object StartDistance {
            const val ALONGSIDE = 40.0
            const val MOVING_FAST = 155.0
            const val MOVING_SLOW = 75.0
            const val PILOT_ZONE = 400.0
            const val PILOT_SGSIN = 400.0
            const val PILOT_LONGRANGE = 800.0
        }

        /**
         * Threshold distances for ending an encounter of various types in meters.
         */
        private object EndDistance {
            const val ALONGSIDE = 130.0
            const val MOVING = 250.0
            const val MOVING_TUG = 400.0
            const val BUNKER = 400.0
            const val PILOT_ZONE = 500.0
            const val PILOT_SGSIN = 4100.0
            const val PILOT_LONGRANGE = 1000.0
        }

        /**
         * Set of roles for excluding some encounters between two service vessels.
         */
        private val serviceRoles = setOf(
            WATER,
            BUNKER,
            BOATMAN,
            PILOT,
            WASTE
        )

        /**
         * Set of roles for authorities
         */
        val authorityRoles = setOf(
            AUTHORITIES,
            PORTAUTHORITIES,
            CUSTOMS,
            PORTPOLICE,
            COASTGUARD
        )

        val nonSeaShipRoles = setOf(TUG, WATER)

        val relevantInlandShipRoles = setOf(
            CARGOBARGE,
            PUSHBARGE,
            SUPPLYBARGE,
            TANKERBARGE
        )

        /**
         * Set of encounters for which we use [StartDistance.ALONGSIDE] and [EndDistance.ALONGSIDE].
         */
        private val alongsideEncounterTypes = setOf(
            EncounterType.SWOG,
            EncounterType.WATER,
            EncounterType.WASTE,
            EncounterType.BUNKER,
            EncounterType.FENDER,
            EncounterType.TENDER,
            EncounterType.BARGE_TANKER,
            EncounterType.BARGE_CARGO,
            EncounterType.BARGE_PUSH,
            EncounterType.BARGE_SUPPLY,
            EncounterType.LUBES,
            EncounterType.BARGE_WATER,
            EncounterType.BARGE_BUNKER
        )

        /**
         * Anti-jitter timeout for bunker/lubes encounter exit criterium.
         */
        private val alongsideExitTimeout = Duration.ofMinutes(5)
    }

    fun isMovingTugEncounter(meets: ShipMeetsShip) =
        meets withRole TUG matching {
            mainVessel.relevantSeaShip &&
                service.hasSpeed &&
                mainVessel.hasSpeed &&
                abs((mainVessel.speed ?: 0.0f) - (service.speed ?: 0.0f)) <= 1.5
        }

    fun isSingaporePilotEncounter(meets: ShipMeetsShip) =
        meets withRole PILOT matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.hasRole(PILOT) &&
                mainVessel.hasLowOrNoSpeed &&
                pointInPolygon(SGSIN_HARBOUR_SEA_PASSAGE, service.location) &&
                !pointInPolygon(SGSIN_PILOT_STATION_EAST, mainVessel.location) &&
                !pointInPolygon(SGSIN_PILOT_STATION_WEST, mainVessel.location)
        }

    fun isPilotEncounter(meets: ShipMeetsShip) =
        meets byPredicate {
            it.hasRole(PILOT) && it.ship.mmsi !in longrangePilotVessels
        } matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.hasRole(PILOT) &&
                service.hasSpeed
        }

    fun isLongrangePilotEncounter(meets: ShipMeetsShip) =
        meets byPredicate {
            it.hasRole(PILOT) && it.ship.mmsi in longrangePilotVessels
        } matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.hasRole(PILOT) &&
                mainVessel.hasSpeed
        }

    fun isWaterBoatEncounter(meets: ShipMeetsShip) =
        meets withRole WATER matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.hasSpeed &&
                mainVessel.role !in serviceRoles
        }

    fun isBoatmanEncounter(meets: ShipMeetsShip) =
        meets withRole BOATMAN matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.hasRole(BOATMAN) &&
                service.hasNoSpeed &&
                mainVessel.hasNoSpeed
        }

    fun isBunkerEncounter(meets: ShipMeetsShip) =
        meets withRole BUNKER matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.hasRole(BUNKER) &&
                !mainVessel.hasSpeed &&
                !service.hasSpeed &&
                bunkeringAllowed(service.location) &&
                (pointInPolygon(SGSIN, service.location) || isAlongside(mainVessel, service))
        }

    fun isAuthoritiesEncounter(meets: ShipMeetsShip) =
        meets byPredicate { it.authority } matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.authority &&
                !service.hasSpeed
        }

    fun isLubesEncounter(meets: ShipMeetsShip) =
        meets withRole LUBES matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.hasRole(LUBES) &&
                !mainVessel.hasSpeed &&
                !service.hasSpeed &&
                bunkeringAllowed(service.location) &&
                (pointInPolygon(SGSIN, service.location) || isAlongside(mainVessel, service))
        }

    fun isBargeWaterEncounter(meets: ShipMeetsShip) =
        meets withRole WATER matching {
            !mainVessel.hasSpeed && mainVessel.relevantInlandShip
        }

    fun isBargeBunkerEncounter(meets: ShipMeetsShip) =
        meets withRole BUNKER matching {
            !mainVessel.relevantSeaShip &&
                !mainVessel.hasSpeed &&
                mainVessel.relevantInlandShip
        }

    fun isTugWaitingDepartureEncounter(meets: ShipMeetsShip) =
        meets withRole TUG matching {
            mainVessel.relevantSeaShip &&
                service.hasNoSpeed &&
                mainVessel.hasNoSpeed
        }

    fun isSupplyBargeEncounter(meets: ShipMeetsShip) =
        meets withRole SUPPLYBARGE matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.hasSpeed &&
                mainVessel.role !in serviceRoles &&
                !mainVessel.authority
        }

    fun isShipToShipEncounter(meets: ShipMeetsShip) =
        meets sameCargoType meets.first.type matching {
            (mainVessel.isSeaVessel || service.isSeaVessel) &&
                (service.hasLowOrNoSpeed && mainVessel.hasLowOrNoSpeed) &&
                (!service.hasRole(BUNKER) && !mainVessel.hasRole(BUNKER)) &&
                areBothAlongside(meets.first, meets.second) &&
                inSTSArea(mainVessel.location)
        }

    fun isEncounterOfRole(meets: ShipMeetsShip, role: ShipRole) =
        meets withRole role matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.hasRole(role) &&
                !mainVessel.hasSpeed
        }

    fun detectorForRole(role: ShipRole) =
        { meets: ShipMeetsShip -> isEncounterOfRole(meets, role) }

    fun isBoardBoardEncounterOfRole(meets: ShipMeetsShip, role: ShipRole, alongside: Boolean) =
        meets withRole role matching {
            mainVessel.relevantSeaShip &&
                !mainVessel.hasSpeed &&
                mainVessel.role !in serviceRoles &&
                !mainVessel.authority &&
                (!alongside || isAlongside(mainVessel, service))
        }

    fun boardBoardDetectorForRole(role: ShipRole, alongside: Boolean) =
        { meets: ShipMeetsShip -> isBoardBoardEncounterOfRole(meets, role, alongside) }

    fun checkStartDistance(type: EncounterType, subType: EncounterSubtype?, mainVessel: ShipState, service: ShipState): Boolean {
        val distance = distanceBetweenShips(
            mainVessel,
            shipInfoDatasource.getTransponderPosition(mainVessel.ship.mmsi),
            service,
            shipInfoDatasource.getTransponderPosition(service.ship.mmsi)
        )

        val detectionDistance = when (type) {
            in alongsideEncounterTypes -> StartDistance.ALONGSIDE
            EncounterType.PILOT -> when (subType) {
                EncounterSubtype.PILOT_ZONE -> StartDistance.PILOT_ZONE
                EncounterSubtype.PILOT_SGSIN -> StartDistance.PILOT_SGSIN
                EncounterSubtype.PILOT_LONGRANGE -> StartDistance.PILOT_LONGRANGE
                else ->
                    if (mainVessel.slow) StartDistance.MOVING_SLOW
                    else StartDistance.MOVING_FAST
            }
            else ->
                if (mainVessel.slow) StartDistance.MOVING_SLOW
                else StartDistance.MOVING_FAST
        }

        return distance < detectionDistance
    }

    /**
     * Check if the distance between [mainVessel] and [service] is bigger than the exit distance defined for the given
     * encounter [type] and [subType]. Returns true if the encounter should be ended.
     */
    fun checkExitDistance(type: EncounterType, subType: EncounterSubtype?, mainVessel: ShipState, service: ShipState): Boolean {
        val distance = distanceBetweenShips(
            ship1 = mainVessel,
            dimensions1 = shipInfoDatasource.getTransponderPosition(mainVessel.ship.mmsi),
            ship2 = service,
            dimensions2 = shipInfoDatasource.getTransponderPosition(service.ship.mmsi),
            // No extrapolation is done when checking the exit range
            extrapolate = false
        )

        return checkExitDistance(type, subType, mainVessel, service, distance)
    }

    fun checkExitDistance(
        type: EncounterType,
        subType: EncounterSubtype?,
        mainVessel: ShipState?,
        service: ShipState,
        distance: Double,
    ): Boolean {
        val detectionDistance = when (type) {
            in alongsideEncounterTypes -> EndDistance.ALONGSIDE
            EncounterType.PILOT -> when (subType) {
                EncounterSubtype.PILOT_ZONE -> EndDistance.PILOT_ZONE
                EncounterSubtype.PILOT_SGSIN -> EndDistance.PILOT_SGSIN
                EncounterSubtype.PILOT_LONGRANGE -> EndDistance.PILOT_LONGRANGE
                else -> EndDistance.MOVING
            }

            EncounterType.TUG,
            EncounterType.TUG_WAITING_DEPARTURE -> {
                if (mainVessel?.hasSpeed != true && !service.hasSpeed) EndDistance.ALONGSIDE
                else EndDistance.MOVING_TUG
            }

            EncounterType.BUNKER,
            EncounterType.LUBES -> EndDistance.BUNKER

            else -> EndDistance.MOVING
        }

        val exceeded = distance > detectionDistance

        return exceeded && !canCatchUp(mainVessel, service, distance)
    }

    /**
     * Checks if the given encounter should be ended. This check is done based on [checkExitDistance], with an
     * additional check for bunker and lubes encounters to see if the service vessel is still alongside the main vessel.
     * A timeout of 5 minutes is used to avoid encounter flip-flopping due to GPS jitter, also see
     * [EncounterState.alongsideEndTime].
     */
    fun checkExitCriteria(encounter: EncounterState, mainVessel: ShipState, service: ShipState): Boolean {
        val type = encounter.type

        if (type == null || checkExitDistance(type, encounter.subType, mainVessel, service)) {
            return true
        }

        if (encounter.type == EncounterType.BUNKER || encounter.type == EncounterType.LUBES) {
            val mainSpeed = mainVessel.speed ?: 0.0f
            val serviceSpeed = service.speed ?: 0.0f

            if (abs(mainSpeed - serviceSpeed) > 3.0) {
                // If the main and service vessel are moving at dissimilar speeds, end the encounter immediately
                return true
            } else {
                // Check if the service vessel is still alongside (except in SGSIN)
                val isAlongside = pointInPolygon(SGSIN, mainVessel.location) ||
                    isAlongside(service, mainVessel, shipInfoDatasource.getTransponderPosition(mainVessel.ship.mmsi), EndDistance.BUNKER)

                if (!isAlongside) {
                    // End the encounter if the service vessel is no longer alongside for more than 5 minutes
                    val timestamp = maxOf(mainVessel.messageTime, service.messageTime)
                    val endTime = encounter.alongsideEndTime
                    if (endTime == null) {
                        encounter.alongsideEndTime = timestamp
                    } else if (Duration.between(endTime, timestamp) > alongsideExitTimeout) {
                        return true
                    }
                } else {
                    encounter.alongsideEndTime = null
                }
            }
        } else if (encounter.type == EncounterType.TUG_WAITING_DEPARTURE) {
            return mainVessel.hasSpeed && service.hasSpeed
        } else if (encounter.type == EncounterType.SHIP_TO_SHIP) {
            return !mainVessel.hasLowOrNoSpeed || !service.hasLowOrNoSpeed
        }

        return false
    }

    fun checkValidity(encounter: EncounterState, mainVessel: ShipState, service: ShipState) = when (encounter.type) {
        EncounterType.TUG_WAITING_DEPARTURE -> {
            // The waiting-for-departure encounter was a valid one if it is ended by both vessels starting to move.
            // If one of the vessels moves away this was not a valid encounter
            mainVessel.hasSpeed && service.hasSpeed
        }
        else -> true
    }

    /**
     * Infix function to order a pair of ships based on a role. The ship matching the role will be stored as the service
     * vessel in [OrderedShips]. If neither of the ships matches, `null` is returned.
     */
    private infix fun ShipMeetsShip.withRole(role: ShipRole) = byPredicate { it.hasRole(role) }

    /**
     * Infix to check if the ships have the same cargo type. If they do, the ships are stored as the main and service
     * vessel in [OrderedShips]. If they don't, `null` is returned.
     */
    private infix fun ShipMeetsShip.sameCargoType(type: ShipType): OrderedShips? {
        // Define cargo types explicitly
        val validCargoTypes = setOf(ShipType.CARGO, ShipType.TANKER)

        // Check if the type is valid and both ships have the same type
        return if (type in validCargoTypes && first.type == second.type) {
            OrderedShips(first, second, NORMAL)
        } else {
            null
        }
    }

    /**
     * Infix function to order a pair of ships based on a predicate. The ship matching the predicate will be stored as
     * the service vessel in [OrderedShips]. If neither of the ships matches, `null` is returned.
     */
    private inline infix fun ShipMeetsShip.byPredicate(predicate: (ShipState) -> Boolean): OrderedShips? {
        val order = when {
            predicate(first) -> REVERSE
            predicate(second) -> NORMAL
            else -> return null
        }
        val (other, service) = if (order == REVERSE) second to first else first to second
        return OrderedShips(other, service, order)
    }

    /**
     * Execute the [predicate] against the [OrderedShips] to determine if the main and service vessel are a match for
     * this type of encounter. Returns the [OrderedShips.order] if it's a match, `null` otherwise.
     */
    private inline infix fun OrderedShips?.matching(predicate: Encounter.() -> Boolean): EncounterState.Order? =
        if (this != null && Encounter(mainVessel, service).predicate()) {
            order
        } else {
            null
        }

    /**
     * Intermediate data class for use in the mini-DSL for describing encounters.
     */
    private data class OrderedShips(
        val mainVessel: ShipState,
        val service: ShipState,
        val order: EncounterState.Order
    )

    /**
     * Intermediate data class for use in the mini-DSL for describing encounters.
     */
    private data class Encounter(
        val mainVessel: ShipState,
        val service: ShipState
    )

    /**
     * Check if the given [vessel1] and [vessel2] are alongside each other. The [maxDistance] defines the maximum
     * distance between the two vessels to be considered alongside.
     */
    private fun areBothAlongside(vessel1: ShipState, vessel2: ShipState): Boolean {
        val maxDistance = 75.0

        val vessel1Alongside = isAlongside(
            service = vessel1,
            seaVessel = vessel2,
            seaVesselDimensions = shipInfoDatasource.getTransponderPosition(vessel1.ship.mmsi),
            maxDistance = maxDistance
        )

        val vessel2Alongside = isAlongside(
            service = vessel2,
            seaVessel = vessel1,
            seaVesselDimensions = shipInfoDatasource.getTransponderPosition(vessel2.ship.mmsi),
            maxDistance = maxDistance
        )

        return vessel1Alongside || vessel2Alongside
    }

    private fun isAlongside(seaVessel: ShipState, serviceVessel: ShipState) = isAlongside(
        service = serviceVessel,
        seaVessel = seaVessel,
        seaVesselDimensions = shipInfoDatasource.getTransponderPosition(seaVessel.ship.mmsi),
        maxDistance = 50.0
    )

    private fun bunkeringAllowed(location: Location) =
        !(
            pointInPolygon(NLRTM_KTMKADE8_EXCLUDE, location) ||
                pointInPolygon(NLRTM_ETT_JETTY_EXCLUDE, location) ||
                pointInPolygon(BEANR_512BC_EXCLUDE, location)
            )

    /**
     * Check if the given [location] is in the Ship-to-Ship area.
     */
    private fun inSTSArea(location: Location): Boolean {
        return pomaService.getShipToShipAreas().any {
            pointInPolygon(it.polygon, location)
        }
    }
}
