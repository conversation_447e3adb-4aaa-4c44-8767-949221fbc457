package nl.teqplay.aisengine.encountermonitor.service

import nl.teqplay.aisengine.encountermonitor.datasource.ShipInfoDatasource
import nl.teqplay.aisengine.revents.NotReventsProfile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit

/**
 * Service that schedules updating of ship info.
 */
@NotReventsProfile
@Component
class ShipInfoUpdateService(
    private val shipInfoDatasource: ShipInfoDatasource,
) {

    @Scheduled(fixedRate = 4, initialDelay = 4, timeUnit = TimeUnit.HOURS)
    fun updateRoles() {
        shipInfoDatasource.updateRoles()
    }
}
