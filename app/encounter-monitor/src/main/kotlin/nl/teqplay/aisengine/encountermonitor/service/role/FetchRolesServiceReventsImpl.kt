package nl.teqplay.aisengine.encountermonitor.service.role

import nl.teqplay.aisengine.revents.ReventsProfile
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.skeleton.csi.client.CsiShipClient
import org.springframework.stereotype.Component

@Component
@ReventsProfile
class FetchRolesServiceReventsImpl(
    private val csiShipClient: CsiShipClient,
) : FetchRolesService {

    override fun fetchRoles(): Map<Int, ShipRole> {
        val list = csiShipClient.listShipRegisterInfoCache()
        val shipsByImoMap = list.associateBy { it.identifiers.imo }
        val mappings = csiShipClient.listShipRegisterMapping()

        val newRoles = mutableMapOf<Int, ShipRole>()

        // add role mapping for all ships
        list.forEach {
            val role = it.types.role
            if (it.identifiers.mmsi != null && role != null && role != ShipRole.NONE) {
                val mmsi = requireNotNull(it.identifiers.mmsi).toInt()
                newRoles[mmsi] = role
            }
        }

        // apply historical IMO/MMSI mappings
        mappings
            .flatMap {
                it.mapping.map { mapping ->
                    ImoMmsiMapping(
                        imo = it.imo,
                        mmsi = mapping.mmsi,
                        from = mapping.from ?: Long.MIN_VALUE,
                        to = mapping.to ?: Long.MAX_VALUE
                    )
                }
            }
            // sort based on their occurrence, this is not perfect though
            // we only support one MMSI/role combination, if a MMSI is used by two ships at some point
            // then this will only work for the ship that had the MMSI last
            .sortedWith(compareBy(ImoMmsiMapping::from, ImoMmsiMapping::to))
            .forEach { mapping ->
                val mmsi = mapping.mmsi.toInt()
                val role = shipsByImoMap[mapping.imo]?.types?.role
                if (role != null && role != ShipRole.NONE) {
                    newRoles[mmsi] = role
                }
            }

        return newRoles
    }

    data class ImoMmsiMapping(
        val imo: String,
        val mmsi: String,
        val from: Long,
        val to: Long,
    )
}
