package nl.teqplay.aisengine.encountermonitor.service

import nl.teqplay.aisengine.revents.NotReventsProfile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit

/**
 * Service that schedules expiring of encounter state.
 */
@NotReventsProfile
@Component
class EncounterExpireService(
    private val encounterService: EncounterService,
) {

    @Scheduled(fixedRate = 10, initialDelay = 10, timeUnit = TimeUnit.MINUTES)
    private fun expireShipState() {
        encounterService.expireShipState()
    }
}
