package nl.teqplay.aisengine.encountermonitor.util

import java.time.Duration
import java.time.Instant

private val probabilityMap = mapOf(
    Duration.ofMinutes(15) to 1.0,
    Duration.ofMinutes(30) to 0.9,
    Duration.ofHours(1) to 0.8,
    Duration.ofHours(4) to 0.6,
    Duration.ofHours(8) to 0.4,
    Duration.ofHours(12) to 0.2,
)

/**
 * Calculate the probability of an encounter based on the relative update time of the two ships.
 */
fun encounterProbability(time1: Instant, time2: Instant): Double {
    val duration = Duration.between(time1, time2).abs()

    return probabilityMap.asSequence()
        .filter { duration < it.key }
        .minByOrNull { it.key }
        ?.value ?: 0.1
}
