package nl.teqplay.aisengine.encountermonitor.service.role

import nl.teqplay.aisengine.revents.NotReventsProfile
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.skeleton.csi.client.CsiShipClient
import org.springframework.stereotype.Component

@Component
@NotReventsProfile
class FetchRolesServiceImpl(
    private val csiShipClient: CsiShipClient,
) : FetchRolesService {

    override fun fetchRoles(): Map<Int, ShipRole> {
        return csiShipClient.listShipRegisterInfoCache()
            .filter { it.identifiers.mmsi != null && it.types.role != null && it.types.role != ShipRole.NONE }
            .associate { it.identifiers.mmsi!!.toInt() to it.types.role!! }
    }
}
