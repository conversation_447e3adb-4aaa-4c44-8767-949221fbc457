package nl.teqplay.aisengine.encountermonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.encountermonitor.model.EncounterState
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import kotlin.system.measureTimeMillis

private val LOG = KotlinLogging.logger {}

interface EncounterStateService {
    fun get(mmsi: Int): Map<Int, EncounterState>
    fun persist(encounter: EncounterState)
    fun remove(encounter: EncounterState)
    fun ongoingCount(): Int
}

/**
 * Service that tracks the state of ongoing encounters and persists them to the NATS KV store. The service supports the
 * following operations:
 * - retrieving/storing all long range encounters
 * - storing/updating an individual encounter
 * - removing an individual encounter
 * - accessing all encounters a specific mmsi is taking part in (as either the "first" or "second" ship)
 * Of note is that, to support the last operation, the service stores two references for each encounter: one based on
 * the first ship in the encounter, and one based on the second. This ensures that the encounter can be found using each
 * of the two.
 */
@Component
class EncounterStateServiceImpl(
    private val kvBucket: NatsKeyValueBucket<EncounterState>
) : EncounterStateService {
    companion object {
        private const val KV_BUCKET = "encounter-monitor"
    }

    /**
     * Double hashmap for finding encounter states based on MMSI. Note that, for every encounter, two references are
     * stored in this hashmap: one starting with the first MMSI as key, and another one starting with the second MMSI
     * as key.
     */
    private val ongoing = ConcurrentHashMap<Int, MutableMap<Int, EncounterState>>()

    init {
        LOG.info { "Reading stored encounter states..." }
        var encounters = 0
        val time = measureTimeMillis {
            kvBucket.entries { (_, value) ->
                if (value != null) {
                    store(value)
                    encounters++
                }
            }
        }
        LOG.info { "Done reading $encounters stored encounter states in $time ms" }
    }

    /**
     * Get all the encounters in which the ship with the given [mmsi] is taking part.
     */
    override fun get(mmsi: Int): Map<Int, EncounterState> {
        return ongoing[mmsi]?.toMap() ?: emptyMap()
    }

    /**
     * Persist the encounter to the KV store.
     */
    override fun persist(encounter: EncounterState) {
        store(encounter)
        kvBucket.put(encounter.key(), encounter)
    }

    /**
     * Remove the given encounter. The encounter is removed from the NATS KV store and from the internal data
     * structures.
     */
    override fun remove(encounter: EncounterState) {
        ongoing[encounter.mmsi1]?.remove(encounter.mmsi2)
        ongoing[encounter.mmsi2]?.remove(encounter.mmsi1)
        kvBucket.delete(encounter.key())
    }

    /**
     * Get the number of ongoing encounters.
     */
    override fun ongoingCount(): Int {
        return ongoing.values.sumOf { it.filter { it.value.startEventId != null }.size } / 2
    }

    @Scheduled(fixedRate = 15, timeUnit = TimeUnit.SECONDS)
    fun stats() {
        val size = ongoing.values.sumOf { it.size } / 2
        val started = ongoingCount()
        LOG.info { "$size encounters, $started started, ${size - started} not started" }
    }

    /**
     * Support function that stores the given encounter in the encounter lists for both ships in the encounter.
     */
    private fun store(encounter: EncounterState) {
        ongoing.getOrPut(encounter.mmsi1, ::mutableMapOf)[encounter.mmsi2] = encounter
        ongoing.getOrPut(encounter.mmsi2, ::mutableMapOf)[encounter.mmsi1] = encounter
    }
}

val EncounterState.mmsi1: Int get() = ship1.mmsi
val EncounterState.mmsi2: Int get() = ship2.mmsi
