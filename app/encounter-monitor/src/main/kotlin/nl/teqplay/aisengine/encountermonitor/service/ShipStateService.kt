package nl.teqplay.aisengine.encountermonitor.service

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.encountermonitor.datasource.ShipInfoDatasource
import nl.teqplay.aisengine.encountermonitor.model.ShipState
import nl.teqplay.aisengine.encountermonitor.util.ECEF
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.skeleton.model.Location
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import kotlin.math.exp

/**
 * Service to construct [ShipState]
 */
@Component
class ShipStateService(
    private val shipInfoDatasource: ShipInfoDatasource
) {
    companion object {
        /**
         * Time constant in minutes for the moving average of the speed. This will (roughly!) result in speeds of 30
         * minutes ago contributing 39% to the average, and speeds of 60 minutes ago contributing 14%.
         */
        private const val speedAverageConstant = 30.0
    }

    fun getState(
        oldState: ShipState?,
        ship: AisShipIdentifier,
        messageTime: Instant,
        to: Location,
        toECEF: ECEF,
        speed: Float?,
        courseOverGround: Float?,
        heading: Float?
    ): ShipState {
        val role = shipInfoDatasource.getRoleForShip(ship.mmsi)
        val isSeaVessel = shipInfoDatasource.getIsSeaVessel(ship.mmsi)
        val shipType = shipInfoDatasource.getShipType(ship.mmsi)
        val authority = role in DetectionService.authorityRoles

        val averageSpeed = if (oldState != null && speed != null) {
            // Determine the time between the updates
            val deltaT = Duration.between(oldState.messageTime, messageTime).toSeconds() / 60.0

            // Compute smoothing factor alpha based on the time constant
            val alpha = 1 - exp(-1 * deltaT / speedAverageConstant)

            alpha * speed + (1 - alpha) * oldState.averageSpeed
        } else 0.0

        return ShipState(
            ship = ship,
            location = to,
            ecefLocation = toECEF,
            speed = speed ?: oldState?.speed,
            courseOverGround = courseOverGround ?: oldState?.courseOverGround,
            heading = heading, // don't overwrite if null, heading is allowed to be null
            messageTime = messageTime,

            role = role,
            isSeaVessel = isSeaVessel,
            relevantSeaShip = isSeaVessel &&
                role !in DetectionService.nonSeaShipRoles &&
                shipInfoDatasource.getShipType(ship.mmsi) != AisMessage.ShipType.FISHING && !authority,
            relevantInlandShip = !isSeaVessel &&
                role in DetectionService.relevantInlandShipRoles,
            authority = authority,
            averageSpeed = averageSpeed,
            type = shipType
        )
    }
}
