package nl.teqplay.aisengine.encountermonitor.service.poma

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.poma.client.PomaInfrastructureClient
import nl.teqplay.skeleton.util.pointInPolygon
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import nl.teqplay.poma.api.v1.ShipToShipArea as PomaShipToShipArea

private val LOG = KotlinLogging.logger { }

/**
 * Service for POMA related functionality.
 */
@Service
class PomaService(private val pomaInfrastructureClient: PomaInfrastructureClient) {

    private var stsAreas = listOf<ShipToShipArea>()

    /**
     * Load all areas on startup and refresh every day at 08:00
     */
    init {
        refreshShipToShipAreas()
    }

    @Scheduled(cron = "0 0 8 * * *")
    private fun refreshShipToShipAreas() {
        try {
            stsAreas = pomaInfrastructureClient.getShipToShipArea().toList().map { fromPomaArea(it) }
        } catch (e: Exception) {
            LOG.error(e) { "Error refreshing ship to ship areas" }
        }
    }

    fun getShipToShipAreas() = stsAreas

    fun getAreaId(location: Location) = stsAreas.firstOrNull { pointInPolygon(it.polygon, location) }?._id

    private fun fromPomaArea(area: PomaShipToShipArea) = ShipToShipArea(
        _id = area._id,
        polygon = Polygon(
            locations = area.area.map {
                Location(it.latitude, it.longitude)
            }
        )
    )
}

/**
 * ShipToShipArea object using platform models instead of POMA models.
 */
data class ShipToShipArea(
    val _id: String?,
    val polygon: Polygon
)
