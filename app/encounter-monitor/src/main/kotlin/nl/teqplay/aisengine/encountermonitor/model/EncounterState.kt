package nl.teqplay.aisengine.encountermonitor.model

import nl.teqplay.aisengine.encountermonitor.util.ECEF
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterMetadata
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import java.time.Instant

/**
 * Data class tracking the state of an ongoing encounter between two ships. The algorithm uses a data structure that
 * allows access to this class via first ship 1, then ship 2, and also first ship 2, then ship 1. Both paths reference
 * the same instance of this class. To allow easy updating of the state, various fields in this class are declared as
 * var.
 */
data class EncounterState(
    val ship1: AisShipIdentifier,
    val ship2: AisShipIdentifier,
    var ecefLocation1: ECEF,
    var ecefLocation2: ECEF,
    var speed1: Double,
    var speed2: Double,
    /**
     * The time at which the encounter has (possibly) started. After the endurance time for the specified encounter type
     * has elapsed, the start event will be fired. In that event, this time is taken as the start time.
     */
    var start: Instant? = null,
    /**
     * The time at which the encounter has (possibly) ended. After the endurance time for the specified encounter type
     * has elapsed, the end event will be fired. In that event, this time is taken as the end time.
     */
    var end: Instant? = null,
    /**
     * Flag to indicate whether the order is normal (ship1 is the main vessel, ship2 is the service vessel) or reversed.
     */
    var order: Order = Order.NORMAL,
    /**
     * The type of the encounter, null if no actual encounter has been detected yet.
     */
    var type: EncounterEvent.EncounterType? = null,
    /**
     * The subtype of the encounter, used to differentiate between some types of pilot encounters, to take into account
     * the differing entry/exit distances for those.
     */
    var subType: EncounterSubtype? = null,
    /**
     * If a start event has been generated, the id will be stored here. This is used to refer to the start event when
     * the end event is generated.
     */
    var startEventId: String? = null,
    /**
     * Start event id for the event that has been fired for [ship2]. This is used in Ship-to-ship Encounters.
     */
    var otherStartEventId: String? = null,
    /**
     * Timestamp for alongside end detection hysteresis. Only if a bunker/lubes vessel is no longer alongside for an
     * extended time do we trigger the encounter end, to prevent GPS-jitter induced flip-flopping.
     */
    var alongsideEndTime: Instant? = null,
    /**
     * The probability of the encounter, based on the relative update age of the two vessels at the start of the
     * encounter.
     */
    var probability: Double? = null,
    /**
     * The metadata of the start event, which is needed to construct the metadata for the end event. This is because it
     * is nice if certain things that are deduced at the start have the same value at the end ;)
     */
    var startMetadata: EncounterMetadata? = null,
) {
    /**
     * Create a string key uniquely identifying anencounter, based on the MMSIs of the two given ships.
     */
    companion object {
        fun key(ship1: AisShipIdentifier, ship2: AisShipIdentifier) =
            if (ship1.mmsi < ship2.mmsi) "${ship1.mmsi}-${ship2.mmsi}"
            else "${ship2.mmsi}-${ship1.mmsi}"
    }
    /**
     * Create a string key uniquely identifying this encounter, based on the MMSIs of the two ships in it.
     */
    fun key() = key(ship1, ship2)

    /**
     * Get the service vessel taking part in this encounter, based on [order].
     */
    val serviceVessel: AisShipIdentifier
        get() = if (order == Order.REVERSE) ship1 else ship2

    /**
     * Get the main vessel taking part in this encounter, based on [order].
     */
    val mainVessel: AisShipIdentifier
        get() = if (order == Order.REVERSE) ship2 else ship1

    /**
     * Get the speed of the service vessel taking part in this encounter, based on [order].
     */
    val serviceVesselSpeed: Double
        get() = if (order == Order.REVERSE) speed1 else speed2

    /**
     * Get the speed of the main vessel taking part in this encounter, based on [order].
     */
    val mainVesselSpeed: Double
        get() = if (order == Order.REVERSE) speed2 else speed1

    /**
     * Enum to store the order of the encounter, see [order].
     */
    enum class Order { NORMAL, REVERSE }

    /**
     * Enum to store the subtype of the encounter, which is only used for internal logic. See [subType].
     */
    enum class EncounterSubtype {
        PILOT_ZONE, PILOT_SGSIN, PILOT_LONGRANGE
    }
}
