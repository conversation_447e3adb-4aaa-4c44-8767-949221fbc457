package nl.teqplay.aisengine.encountermonitor.util

import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.skeleton.model.Location
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

/**
 * Storage class for ECEF coordinates, in meters. These are cartesian coordinates, with the origin at Earth center,
 * with Earth fixed (hence, ECEF). This system is used since it allows very fast and precise distance measurements, as
 * opposed to latitude/longitude.
 */
data class ECEF(
    val x: Double,
    val y: Double,
    val z: Double
) {
    /**
     * Calculate the Euclidean distance to an[other] ECEF point.
     */
    fun distanceTo(other: ECEF): Double {
        val dx = other.x - x
        val dy = other.y - y
        val dz = other.z - z

        return sqrt(dx * dx + dy * dy + dz * dz)
    }
}

/** WGS-84 ellipsoid flattening */
private const val FLATTENING = (1 / 298.257_223_563)

/** Pre-compute the squared eccentricity based on the flattening */
private const val ECCENTRICITY_SQ = (FLATTENING * (2 - FLATTENING))

/** WGS-84 ellipsoid semi-major axis */
private const val SEMI_MAJOR_AXIS = 6_378_137.0

/**
 * Convert a location into ECEF coordinates in meters, based on the WGS-84 ellipsoid. The height is
 * taken to be zero, at the ellipsoid.
 */
fun Location.toECEF(): ECEF {
    val lat = Math.toRadians(this.lat)
    val lon = Math.toRadians(this.lon)

    val cosLat = cos(lat)
    val cosLon = cos(lon)
    val sinLat = sin(lat)
    val sinLon = sin(lon)

    val anomaly = (ECCENTRICITY_SQ * (sinLat * sinLat))
    val distance = (SEMI_MAJOR_AXIS / sqrt(1 - anomaly))

    return ECEF(
        distance * cosLat * cosLon,
        distance * cosLat * sinLon,
        distance * (1 - ECCENTRICITY_SQ) * sinLat
    )
}

/**
 * Add two ECEF vectors.
 */
operator fun ECEF.plus(other: ECEF) = ECEF(
    x + other.x,
    y + other.y,
    z + other.z
)

/**
 * Given a [transponderPosition] at a [location] and a [heading], determine the position of the bow of the ship.
 * The result is returned as ECEF coordinates.
 */
fun bowPosition(transponderPosition: TransponderPosition, location: Location, ecefLocation: ECEF, heading: Double) =
    ENU(
        (transponderPosition.distanceToStarboard - transponderPosition.distanceToPort) / 2.0,
        transponderPosition.distanceToBow.toDouble(),
        0.0
    )
        .rotate(heading)
        .toECEF(location) + ecefLocation

/**
 * Given a [transponderPosition] at a [location] and a [heading], determine the position of the stern of the ship.
 * The result is returned as ECEF coordinates.
 */
fun sternPosition(transponderPosition: TransponderPosition, location: Location, ecefLocation: ECEF, heading: Double) =
    ENU(
        (transponderPosition.distanceToStarboard - transponderPosition.distanceToPort) / 2.0,
        -transponderPosition.distanceToStern.toDouble(),
        0.0
    )
        .rotate(heading)
        .toECEF(location) + ecefLocation
