package nl.teqplay.aisengine.encountermonitor.util

import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.encountermonitor.model.ShipState
import java.time.Duration
import java.util.concurrent.TimeUnit
import kotlin.math.pow
import kotlin.math.sqrt

/** 1nm in meters. */
private const val NAUTICAL_MILE = 1852

/** Margin to assume when extrapolating ship positions. */
private const val ASSUME_MARGIN = 1.10

/** Tolerance percentage of the ship's length used to determine if two ships are still considered alongside each other. */
private const val ALONGSIDE_LENGTH_TOLERANCE = 0.10

/**
 * Calculates the closest distance between two ships. If no dimensions/transponder positions are known, this degenerates
 * to the distance between the two ship locations. If one or both of the dimensions/transponder positions are known, the
 * minimum of the distance of a line between the bow and stern to the other position or other bow or stern is taken.
 */
fun distanceBetweenShips(
    ship1: ShipState,
    dimensions1: TransponderPosition?,
    ship2: ShipState,
    dimensions2: TransponderPosition?,
    extrapolate: Boolean = true
): Double {
    return minOf(
        distanceBetweenShipsAsPointAndLine(ship1, ship2, dimensions2, extrapolate),
        distanceBetweenShipsAsPointAndLine(ship2, ship1, dimensions1, extrapolate)
    )
}

/**
 * Compute the distance between [ship1] and [ship2] taking into account the dimensions given in [dimensions2] of the
 * second ship. A point-to-line distance is used, not taking into account the width of [ship2]. If the time between the
 * ships last updates is less than 15 minutes, the oldest ship's position is extrapolated linearily. This is
 * particularly important for moving tug encouters: it is very easy to trigger flip-flopping if a few updates are
 * missed of one of the two ships.
 */
private fun distanceBetweenShipsAsPointAndLine(
    ship1: ShipState,
    ship2: ShipState,
    dimensions2: TransponderPosition?,
    extrapolate: Boolean
): Double {
    val timeSep = Duration.between(ship1.messageTime, ship2.messageTime)

    // Get the ECEF positions of both ships.
    // If there are less than 15 minutes of time between the updates of both ships,
    // perform extrapolation on the oldest ship position to get a more accurate distance.
    val (ship1ecef, ship2ecef) = when {
        timeSep.abs() > Duration.ofMinutes(15) -> {
            ship1.ecefLocation to ship2.ecefLocation
        }
        timeSep.isNegative && extrapolate -> {
            ship1.ecefLocation to extrapolate(ship2, timeSep.abs())
        }
        !timeSep.isNegative && extrapolate -> {
            extrapolate(ship1, timeSep) to ship2.ecefLocation
        }
        else -> {
            ship1.ecefLocation to ship2.ecefLocation
        }
    }

    // Compute the bow and stern positions. Note that the un-extrapolated lat/lon location is passed in here. This
    // location is used only to compute the orientation of the ENU vector so this does not need to be very accurate.
    val (bow, stern) = if (dimensions2 != null && ship2.heading != null) {
        bowPosition(dimensions2, ship2.location, ship2ecef, ship2.heading.toDouble()) to
            sternPosition(dimensions2, ship2.location, ship2ecef, ship2.heading.toDouble())
    } else return ship2ecef.distanceTo(ship1ecef)

    // Construct an ENU frame centered at the service vessel
    val m = enuMatrix(ship1.location)
    // And derive points in this frame for the bow of the sea vessel ...
    val point1 = ECEF(
        bow.x - ship1ecef.x,
        bow.y - ship1ecef.y,
        bow.z - ship1ecef.z
    ).toENU(m)
    // ... the stern of the sea vessel ...
    val point2 = ECEF(
        stern.x - ship1ecef.x,
        stern.y - ship1ecef.y,
        stern.z - ship1ecef.z
    ).toENU(m)

    var de = point2.e - point1.e
    var dn = point2.n - point1.n

    // Point on the line defined by point1 and point2, where it equals 0 when it equals point1, and equals 1
    // when it equals point2. Any value between is a point within the line segment, any value <0 is closest to point1
    // and any value >1 is closest to point2.
    val t = ((-point1.e * de) + (-point1.n * dn)) / (de * de + dn * dn)

    if (t < 0) {
        // closest to point 1 of line
        de = point1.e
        dn = point1.n
    } else if (t > 1) {
        // closest to point 2 of line
        de = point2.e
        dn = point2.n
    } else {
        // somewhere inside the line segment
        de = point1.e + t * de
        dn = point1.n + t * dn
    }
    return sqrt(de.pow(2) + dn.pow(2))
}

/**
 * Checks if a service provider is alongside a sea vessel. If no dimensions/transponder position are available for the
 * sea vessel, only the given distance is checked. If the dimensions/transponder position are known, the service vessel
 * is deemed to be alongside the sea vessel if its position is inside a rectangle with the length of the sea vessel and
 * double the width of the [maxDistance], centered on the sea vessel. This means that the service vessel cannot be
 * ahead or behind the sea vessel.
 */
fun isAlongside(
    service: ShipState,
    seaVessel: ShipState,
    seaVesselDimensions: TransponderPosition?,
    maxDistance: Double
): Boolean {
    val (bow, stern) = if (seaVesselDimensions != null && seaVessel.heading != null) {
        // Extend the sea vessel dimension to be more lenient towards ships just in front or behind the main vessel
        val extendedDimensions = seaVesselDimensions.extend(ALONGSIDE_LENGTH_TOLERANCE)

        // Calculate original bow and stern positions
        val bow = bowPosition(
            transponderPosition = extendedDimensions,
            location = seaVessel.location,
            ecefLocation = seaVessel.ecefLocation,
            heading = seaVessel.heading.toDouble()
        )
        val stern = sternPosition(
            transponderPosition = extendedDimensions,
            location = seaVessel.location,
            ecefLocation = seaVessel.ecefLocation,
            heading = seaVessel.heading.toDouble()
        )

        bow to stern
    } else return seaVessel.ecefLocation.distanceTo(service.ecefLocation) <= maxDistance

    // Construct an ENU frame centered at the service vessel
    val m = enuMatrix(service.location)
    // And derive points in this frame for the bow of the sea vessel ...
    val point1 = ECEF(
        bow.x - service.ecefLocation.x,
        bow.y - service.ecefLocation.y,
        bow.z - service.ecefLocation.z
    ).toENU(m)
    // ... the stern of the sea vessel ...
    val point2 = ECEF(
        stern.x - service.ecefLocation.x,
        stern.y - service.ecefLocation.y,
        stern.z - service.ecefLocation.z
    ).toENU(m)

    // ... and a line centered at the service vessel, perpendicular to the sea vessel, sized to the encounter distance
    val point3 = ENU(0.0, maxDistance, 0.0)
        .rotate((seaVessel.heading + 90.0) % 360)
    val point4 = ENU(0.0, maxDistance, 0.0)
        .rotate((seaVessel.heading - 90.0) % 360)

    // Then check if the service vessel line intersects the sea vessel. This means that the service vessel is not ahead
    // or behind the sea vessel, and that it is within maxDistance of it.
    return intersects(point1, point2, point3, point4)
}

/**
 * Extrapolate the current [ShipState.ecefLocation] in [ship] using the course over ground and speed, for the given
 * [time]. If either speed or course over ground is null, return the current location.
 */
fun extrapolate(ship: ShipState, time: Duration): ECEF {
    val speed = ship.speed?.toDouble()
    val cog = ship.courseOverGround?.toDouble()

    if (speed == null || cog == null) return ship.ecefLocation

    // Convert the speed in knots (nm/hr) to meters per ms
    val distancePerMs = speed * NAUTICAL_MILE / TimeUnit.MILLISECONDS.convert(1, TimeUnit.HOURS)

    // Create a "straight north" ENU vector with the length traveled in the given time
    return ENU(
        0.0,
        distancePerMs * time.toMillis(),
        0.0
    )
        // Rotate it using the course over ground
        .rotate(cog)
        // Convert it to the ECEF frame and add it to the current ship location to get the result
        .toECEF(ship.location) + ship.ecefLocation
}

/**
 * Determines if it's possible for one ship to catch up to the other based on their speeds and time difference.
 *
 * This function evaluates whether the [distance] between two ships can be covered by either ship
 * given their speed and the time difference between their last reported positions.
 *
 * The calculation uses the following logic:
 * 1. Computes the time difference between the ships' last reported positions (messageTime).
 * 2. Use the speed of the ship with the older position data for the calculation.
 * 3. Calculates the maximum distance that could be traveled in the time difference.
 * 4. Compare this potential travel distance with the actual distance between the ships.
 *
 * @param main The main ship
 * @param service The service ship
 * @param distance The distance between the two ships
 * @return True if the ships can catch up, false if not
 */
fun canCatchUp(main: ShipState?, service: ShipState, distance: Double): Boolean {
    main ?: return false

    val timeSep = Duration.between(main.messageTime, service.messageTime).abs()

    // When the difference in time exceeds 15 minutes, the data is too old for a reliable prediction
    if (timeSep > Duration.ofMinutes(15)) return false

    // Take the oldest ship's speed
    val speed = if (main.messageTime > service.messageTime) {
        service.speed?.toDouble()?.toMetersPerSecond() ?: 0.0
    } else {
        main.speed?.toDouble()?.toMetersPerSecond() ?: 0.0
    }

    // Calculate the distance the ship will be able to travel in the time difference
    val possibleDistance = timeSep.seconds * speed

    return (possibleDistance * ASSUME_MARGIN) > distance
}

// Convert the speed in knots (nm/hr) to meters per ms
private fun Double.toMetersPerSecond() = this * NAUTICAL_MILE / TimeUnit.SECONDS.convert(1, TimeUnit.HOURS)

/**
 * Extend the transponder position by a percentage of the total length of the ship.
 *
 * @param extension The percentage to extend the transponder position by.
 */
private fun TransponderPosition.extend(extension: Double): TransponderPosition {
    val totalLength = distanceToBow + distanceToStern
    val extensionLength = totalLength * extension / 2 // Divide by 2 to distribute evenly

    return this.copy(
        distanceToBow = (distanceToBow + extensionLength).toInt(),
        distanceToStern = (distanceToStern + extensionLength).toInt(),
    )
}
