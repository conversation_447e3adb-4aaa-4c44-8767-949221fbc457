package nl.teqplay.aisengine.encountermonitor.support

import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.web.client.RestTemplate
import java.time.Duration

@Configuration
class RestTemplateConfiguration {
    @Bean
    fun restTemplate(): RestTemplate = RestTemplateBuilder()
        .messageConverters(MappingJackson2HttpMessageConverter())
        .connectTimeout(Duration.ofSeconds(10))
        .readTimeout(Duration.ofSeconds(120))
        .build()
}
