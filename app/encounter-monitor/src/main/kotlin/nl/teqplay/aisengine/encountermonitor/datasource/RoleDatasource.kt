package nl.teqplay.aisengine.encountermonitor.datasource

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.encountermonitor.service.role.FetchRolesService
import nl.teqplay.csi.model.ship.info.component.ShipRole
import org.springframework.stereotype.Component
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write
import kotlin.system.measureTimeMillis

private val LOG = KotlinLogging.logger { }

@Component
class RoleDatasource(
    private val fetchRolesService: FetchRolesService,
) {

    private val roleLock = ReentrantReadWriteLock()

    private var roles = emptyMap<Int, ShipRole>()

    /**
     * Fetches and updates the roles.
     * Note that only the roles other than NONE are stored, since that is the default value.
     */
    fun updateRoles() {
        measureTimeMillis {
            val newRoles = fetchRolesService.fetchRoles()
            roleLock.write {
                roles = newRoles
            }
        }.also {
            LOG.info { "Downloaded ${roles.size} ship roles in $it ms." }
        }
    }

    /**
     * Get the [ShipRole] for the vessel with the given [mmsi].
     */
    fun getRoleForShip(mmsi: Int): ShipRole? {
        return roleLock.read {
            roles[mmsi]
        }
    }
}
