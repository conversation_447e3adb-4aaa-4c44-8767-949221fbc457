package nl.teqplay.aisengine.encountermonitor.properties

import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = EncounterMonitorProperties.PREFIX)
data class EncounterMonitorProperties(
    val cacheTtl: CacheTtl,

    /**
     * Toggle to disable both [EncounterType.BARGE_BUNKER] and [EncounterType.BARGE_WATER] from detecting encounters.
     */
    val disableBargeBunkerAndBargeWater: Boolean = false,
) {
    companion object {
        const val PREFIX = "encounter"
    }

    data class CacheTtl(
        val shipRole: Duration
    )
}
