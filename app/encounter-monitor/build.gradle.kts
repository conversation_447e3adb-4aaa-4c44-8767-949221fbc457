buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":lib:encounter-monitor-common"))
    implementation(project(":api:nats-stream-ais-consume-diff"))
    implementation(project(":api:nats-stream-event"))

    implementation("nl.teqplay.skeleton:nats:$skeletonVersion")
    implementation("nl.teqplay.skeleton:util-location2:$skeletonVersion")
    implementation("nl.teqplay.skeleton:csi-client:$skeletonVersion")
    testImplementation("nl.teqplay.skeleton:nats-test:$skeletonVersion")
    implementation("nl.teqplay.skeleton:poma-client:$skeletonVersion")
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
