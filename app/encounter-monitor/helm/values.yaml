global:
  storageClass: "gp3"
  namespaceOverride: "teqplay-app"

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/encounter-monitor

resources:
  requests:
    cpu: 0.15
    memory: 1Gi
  limits:
    memory: 1Gi

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

mongodb:
  enabled: false

nodeSelector:
  app.teqplay.nl/nodegroup: ais-engine

tolerations:
  - key: nodegroup
    operator: Equal
    value: ais-engine
    effect: NoSchedule
