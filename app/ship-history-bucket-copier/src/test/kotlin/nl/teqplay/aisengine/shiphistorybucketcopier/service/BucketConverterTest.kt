package nl.teqplay.aisengine.shiphistorybucketcopier.service

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.TransponderPositionBucketing
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.model.bucket.UnorderedBucket
import nl.teqplay.aisengine.shiphistorybucketcopier.properties.BucketCopyProperties
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class BucketConverterTest {

    private val bucketCopyProperties = BucketCopyProperties(
        start = Instant.EPOCH,
        end = Instant.EPOCH.plusSeconds(3)
    )
    private val bucketConverter = BucketConverter(bucketCopyProperties)

    @Test
    fun `convert - converts ordered to unordered, as well as filtering within copy window`() {
        val orderedBuckets = listOf(
            OrderedBucket(
                _id = "_id",
                bucketId = "bucketId",
                archiveBucketId = "archiveBucketId",
                data = mutableListOf(
                    orderedDiffMessage.copy(messageTime = bucketCopyProperties.start.minusSeconds(2)),
                    orderedDiffMessage.copy(messageTime = bucketCopyProperties.start.minusSeconds(1)),
                    orderedDiffMessage,
                    orderedDiffMessageZipped,
                    orderedDiffMessage.copy(messageTime = bucketCopyProperties.end),
                    orderedDiffMessage.copy(messageTime = bucketCopyProperties.end.plusSeconds(1)),
                )
            )
        ).iterator()

        val expected = listOf(
            UnorderedBucket(
                bucketId = "bucketId",
                archiveBucketId = "archiveBucketId",
                data = mutableListOf(
                    unorderedDiffMessage,
                    unorderedDiffMessageUnzipped,
                )
            )
        )

        val actual = bucketConverter.convert(orderedBuckets)
        assertThat(actual)
            .usingRecursiveComparison().ignoringFields("_id")
            .isEqualTo(expected)
    }

    private val orderedDiffMessage = AisHistoricOrderedDiffMessage(
        mmsi = 0,
        messageTime = Instant.EPOCH,
        receptionTime = Instant.EPOCH.plusSeconds(1),

        historic = false,
        source = "source",
        subSource = "subSource",
        messageType = "position",

        lat = 52.0,
        lon = 4.0,
        heading = 1,
        speedOverGround = 2f,
        courseOverGround = 3f,
        status = AisMessage.ShipStatus.MOORED,

        imo = 1,
        shipType = AisMessage.ShipType.CARGO,
        draught = 4f,
        eta = Instant.EPOCH.plusSeconds(2),
        destination = "destination",
        transponderPosition = TransponderPositionBucketing(
            distanceToBow = 0,
            distanceToStern = 1,
            distanceToPort = 2,
            distanceToStarboard = 3
        ),
    )

    private val orderedDiffMessageZipped = AisHistoricOrderedDiffMessage(
        mmsi = 0,
        messageTime = Instant.EPOCH.plusSeconds(2),
        receptionTime = Instant.EPOCH.plusSeconds(3),
    )

    private val unorderedDiffMessage = AisHistoricUnorderedDiffMessage(
        mmsi = orderedDiffMessage.mmsi,
        messageTime = orderedDiffMessage.messageTime,
        receptionTime = orderedDiffMessage.receptionTime,

        historic = orderedDiffMessage.historic ?: true,
        source = orderedDiffMessage.source ?: "",
        subSource = orderedDiffMessage.subSource,
        messageType = orderedDiffMessage.messageType,

        lat = orderedDiffMessage.lat ?: -10.0,
        lon = orderedDiffMessage.lon ?: -10.0,
        heading = orderedDiffMessage.heading,
        speedOverGround = orderedDiffMessage.speedOverGround,
        courseOverGround = orderedDiffMessage.courseOverGround,
        status = orderedDiffMessage.status,

        imo = orderedDiffMessage.imo,
        shipType = orderedDiffMessage.shipType,
        draught = orderedDiffMessage.draught,
        eta = orderedDiffMessage.eta,
        destination = orderedDiffMessage.destination,
        transponderPosition = orderedDiffMessage.transponderPosition,
    )

    private val unorderedDiffMessageUnzipped = unorderedDiffMessage.copy(
        messageTime = orderedDiffMessageZipped.messageTime,
        receptionTime = orderedDiffMessageZipped.receptionTime,
    )
}
