package nl.teqplay.aisengine.shiphistorybucketcopier.service

import nl.teqplay.aisengine.shiphistorybucketcopier.config.SourceMongoDbReadStorage
import nl.teqplay.aisengine.shiphistorybucketcopier.datasource.BucketCopyTrackingDataSource
import nl.teqplay.aisengine.shiphistorybucketcopier.model.BucketCopyKeys
import nl.teqplay.aisengine.shiphistorybucketcopier.properties.BucketCopyProperties
import nl.teqplay.skeleton.model.TimeWindow
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class BucketCopyInitializerTest {

    private val bucketCopyTrackingDataSource = mock<BucketCopyTrackingDataSource>()
    private val bucketCopyProperties = mock<BucketCopyProperties>()
    private val sourceShipHistoryByMmsi = mock<SourceMongoDbReadStorage>()
    private val sourceShipHistoryByArea = mock<SourceMongoDbReadStorage>()
    private val bucketCopyInitializer = BucketCopyInitializer(
        bucketCopyTrackingDataSource,
        bucketCopyProperties,
        sourceShipHistoryByMmsi,
        sourceShipHistoryByArea,
    )

    @Test
    fun `init - if already initialized, return results`() {
        val expected = mock<BucketCopyKeys>()

        whenever(bucketCopyTrackingDataSource.isAlreadyInitialized()).thenReturn(true)
        whenever(bucketCopyTrackingDataSource.getRemainingKeys()).thenReturn(expected)

        val actual = bucketCopyInitializer.init()
        assertThat(actual).isEqualTo(expected)

        verify(bucketCopyTrackingDataSource, never()).save(any())
    }

    @Test
    fun `init - if not initialized, request and save results`() {
        val expected = BucketCopyKeys(
            byMmsi = listOf("mmsi"),
            byArea = listOf("area"),
        )
        val window = mock<TimeWindow>()

        whenever(bucketCopyTrackingDataSource.isAlreadyInitialized()).thenReturn(false)
        whenever(bucketCopyProperties.toTimeWindow()).thenReturn(window)

        whenever(sourceShipHistoryByMmsi.findExistingBucketIds(any())).thenReturn(listOf("mmsi").iterator())
        whenever(sourceShipHistoryByArea.findExistingBucketIds(any())).thenReturn(listOf("area").iterator())

        val actual = bucketCopyInitializer.init()
        assertThat(actual).isEqualTo(expected)

        verify(sourceShipHistoryByMmsi, times(1)).findExistingBucketIds(eq(window))
        verify(sourceShipHistoryByArea, times(1)).findExistingBucketIds(eq(window))
        verify(bucketCopyTrackingDataSource, times(1)).save(eq(expected))
        verify(bucketCopyTrackingDataSource, never()).getRemainingKeys()
    }
}
