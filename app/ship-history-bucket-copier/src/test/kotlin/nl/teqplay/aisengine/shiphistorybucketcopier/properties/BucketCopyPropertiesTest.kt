package nl.teqplay.aisengine.shiphistorybucketcopier.properties

import nl.teqplay.skeleton.model.TimeWindow
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class BucketCopyPropertiesTest {

    @Test
    fun toTimeWindow() {
        val start = Instant.parse("2024-02-25T09:20:00Z")
        val end = Instant.parse("2024-02-25T17:10:00Z")

        val properties = BucketCopyProperties(start, end)
        val actual = properties.toTimeWindow()

        assertThat(actual).isEqualTo(TimeWindow(start, end))
    }
}
