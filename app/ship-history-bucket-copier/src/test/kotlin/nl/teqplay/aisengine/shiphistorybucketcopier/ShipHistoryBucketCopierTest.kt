package nl.teqplay.aisengine.shiphistorybucketcopier

import nl.teqplay.aisengine.shiphistorybucketcopier.model.BucketCopyKeys
import nl.teqplay.aisengine.shiphistorybucketcopier.service.BucketCopyInitializer
import nl.teqplay.aisengine.shiphistorybucketcopier.service.BucketCopyProcessor
import org.junit.jupiter.api.Test
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class ShipHistoryBucketCopierTest {

    private val bucketCopyInitializer = mock<BucketCopyInitializer>()
    private val bucketCopyProcessor = mock<BucketCopyProcessor>()
    private val shipHistoryBucketCopier = ShipHistoryBucketCopier(
        bucketCopyInitializer,
        bucketCopyProcessor,
    )

    @Test
    fun `run - takes input from init to process`() {
        val bucketCopyKeys = BucketCopyKeys(
            byMmsi = listOf("mmsi"),
            byArea = listOf("area")
        )

        whenever(bucketCopyInitializer.init()).thenReturn(bucketCopyKeys)

        shipHistoryBucketCopier.run()

        verify(bucketCopyInitializer, times(1)).init()
        verify(bucketCopyProcessor, times(1)).process(eq(bucketCopyKeys))
    }
}
