package nl.teqplay.aisengine.shiphistorybucketcopier.service

import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties.Mongo
import nl.teqplay.aisengine.bucketing.properties.BucketProperties.Mongo.Unordered
import nl.teqplay.aisengine.bucketing.properties.BucketProperties.Mongo.Unordered.BulkWrite
import nl.teqplay.aisengine.shiphistorybucketcopier.config.SourceMongoDbReadStorage
import nl.teqplay.aisengine.shiphistorybucketcopier.config.TargetMongoDbWriteStorage
import nl.teqplay.aisengine.shiphistorybucketcopier.datasource.BucketCopyTrackingDataSource
import nl.teqplay.aisengine.shiphistorybucketcopier.model.BucketCopyKeys
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration

class BucketCopyProcessorTest {

    private val bucketProperties = mock<BucketProperties>().apply {
        whenever(mongo).thenReturn(
            Mongo(
                fetchBatchSize = 0,
                unordered = Unordered(
                    schedule = mock(),
                    flush = mock(),
                    bulkWrite = BulkWrite(
                        maxCount = 1000,
                        sleep = Duration.ZERO
                    )
                )
            )
        )
    }
    private val bucketConverter = mock<BucketConverter>()
    private val bucketCopyTrackingDataSource = mock<BucketCopyTrackingDataSource>()

    private val sourceShipHistoryByMmsi = mock<SourceMongoDbReadStorage>()
    private val sourceShipHistoryByArea = mock<SourceMongoDbReadStorage>()

    private val targetShipHistoryByMmsi = mock<TargetMongoDbWriteStorage>()
    private val targetShipHistoryByArea = mock<TargetMongoDbWriteStorage>()

    private val bucketCopyProcessor = BucketCopyProcessor(
        bucketProperties,
        bucketConverter,
        bucketCopyTrackingDataSource,
        sourceShipHistoryByMmsi,
        sourceShipHistoryByArea,
        targetShipHistoryByMmsi,
        targetShipHistoryByArea,
    )

    @Test
    fun process() {
        val bucketCopyKeys = BucketCopyKeys(
            byMmsi = listOf("mmsi"),
            byArea = listOf("area"),
        )

        val mmsiOrderedBucket = OrderedBucket<AisHistoricOrderedDiffMessage>(
            _id = "_id",
            bucketId = "mmsiBucketId",
            archiveBucketId = "mmsiArchiveBucketId",
            data = mutableListOf(mock())
        )
        val areaOrderedBucket = OrderedBucket<AisHistoricOrderedDiffMessage>(
            _id = "_id",
            bucketId = "mmsiBucketId",
            archiveBucketId = "mmsiArchiveBucketId",
            data = mutableListOf(mock())
        )

        whenever(sourceShipHistoryByMmsi.load(any<List<String>>())).thenReturn(listOf(mmsiOrderedBucket).iterator())
        whenever(sourceShipHistoryByArea.load(any<List<String>>())).thenReturn(listOf(areaOrderedBucket).iterator())

        bucketCopyProcessor.process(bucketCopyKeys)

        verify(sourceShipHistoryByMmsi, times(1)).load(eq(listOf("mmsi")))
        verify(targetShipHistoryByMmsi, times(1)).insertMany(any())

        verify(sourceShipHistoryByArea, times(1)).load(eq(listOf("area")))
        verify(targetShipHistoryByArea, times(1)).insertMany(any())

        verify(bucketConverter, times(2)).convert(any())

        verify(bucketCopyTrackingDataSource, times(1)).completed(eq(bucketCopyKeys.byMmsi))
        verify(bucketCopyTrackingDataSource, times(1)).completed(eq(bucketCopyKeys.byArea))
    }
}
