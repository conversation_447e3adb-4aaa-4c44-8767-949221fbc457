package nl.teqplay.aisengine.aisrabbitmqconverter.service

import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCursor
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.aisrabbitmqconverter.Config
import nl.teqplay.aisengine.aisstream.model.AisLongRangeMessage
import nl.teqplay.aisengine.aisstream.model.AisLongRangeWrapper
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.datasource.MinimalStateDataSourceImpl
import nl.teqplay.aisengine.datasource.MinimalStateWrapper
import nl.teqplay.aisengine.model.MinimalState
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant

class ShipStateServiceTest {

    private val cursor = mock<MongoCursor<MinimalStateWrapper>>()
    private val findIterable = mock<FindIterable<MinimalStateWrapper>>().also {
        whenever(it.cursor()).thenReturn(cursor)
    }
    private val minimalStateDataSource = mock<MinimalStateDataSourceImpl>().also {
        whenever(it.getOlderState(any())).thenReturn(findIterable)
        whenever(it.getInitialState(any())).thenReturn(findIterable)
        whenever(it.findStateById(any())).thenReturn(null)
    }
    private val rabbitMqService = mock<RabbitMqService>()
    private val config = Config("test", "test", "test", 1000)
    private val shipStateService = ShipStateService(minimalStateDataSource, rabbitMqService, config, SimpleMeterRegistry())

    @Test
    fun `test update being processed and put on message queue without subsource`() {
        val mmsi = 123456789
        val imo = 98765432
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test",
            null,
            AisPositionMessage(
                mmsi = mmsi,
                location = Location(12.34, 56.78),
                speedOverGround = 10.0f,
                heading = 90
            )
        )
        val position2 = position1.copy(
            timestamp = Instant.parse("2022-01-01T13:00:00Z"),
            receptionTimestamp = Instant.parse("2022-01-01T13:00:00Z")
        )
        val static1 = AisStaticWrapper(
            Instant.parse("2022-01-01T11:00:00Z"),
            Instant.parse("2022-01-01T11:00:00Z"),
            "unit-test-2",
            null,
            AisStaticMessage(
                mmsi = mmsi,
                imo = imo,
                name = "Unit Test Vessel",
                callSign = "TEQ123",
                shipType = AisMessage.ShipType.CARGO,
                draught = 1.0F,
                eta = Instant.now(),
                destination = "VAN NELLE",
                transponderPosition = TransponderPosition(5, 100, 5, 10),
                positionSensorType = AisMessage.PositionSensorType.GPS,
                aisVersion = AisMessage.AisVersionIndicator.ITU_R_M1371_1,
                usingDataTerminal = false
            )
        )

        val currentState = MinimalState(position1, static1)
        shipStateService.process(position2, currentState)

        val mergedShipInfo = shipStateService.messageQueue.elementAt(0)
        assertEquals("AIS:UNIT-TEST:POSITION, AIS:UNIT-TEST-2:STATIC", mergedShipInfo.source, "Source is not correct")
        assertEquals("TEQ123", mergedShipInfo.callSign, "Callsign is incorrect")
        assertEquals(56.78, mergedShipInfo.location.longitude, "Location is incorrect")
    }

    @Test
    fun `test update being processed and put on message queue`() {
        val mmsi = 123456789
        val imo = 98765432
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test",
            "t",
            AisPositionMessage(
                mmsi = mmsi,
                location = Location(12.34, 56.78),
                speedOverGround = 10.0f,
                heading = 90
            )
        )
        val static1 = AisStaticWrapper(
            Instant.parse("2022-01-01T11:00:00Z"),
            Instant.parse("2022-01-01T11:00:00Z"),
            "unit-test",
            "S123",
            AisStaticMessage(
                mmsi = mmsi,
                imo = imo,
                name = "Unit Test Vessel",
                callSign = "TEQ123",
                shipType = AisMessage.ShipType.CARGO,
                draught = 1.0F,
                eta = Instant.now(),
                destination = "VAN NELLE",
                transponderPosition = TransponderPosition(5, 100, 5, 10),
                positionSensorType = AisMessage.PositionSensorType.GPS,
                aisVersion = AisMessage.AisVersionIndicator.ITU_R_M1371_1,
                usingDataTerminal = false
            )
        )

        val currentState = MinimalState(position1, static1)

        shipStateService.process(position1.copy(historic = true), currentState)
        assertEquals(false, shipStateService.messageQueue.first().streamingIsNewUpdate)
        val position2 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:01Z"),
            Instant.parse("2022-01-01T12:00:02Z"),
            "unit-test",
            "d",
            AisPositionMessage(
                mmsi = mmsi,
                location = Location(12.34, 56.78),
                speedOverGround = 10.0f,
                heading = 90
            )
        )
        shipStateService.process(position2, currentState)
        assertEquals(true, shipStateService.messageQueue.elementAt(1).streamingIsNewUpdate)
        val mergedShipInfo = shipStateService.messageQueue.elementAt(1)
        assertEquals(mmsi.toString(), mergedShipInfo.mmsi, "Ship id is not correct")
        assertEquals("AIS:UNIT-TEST-D:POSITION, AIS:UNIT-TEST-S:STATIC", mergedShipInfo.source, "Source is not correct")
        assertEquals("TEQ123", mergedShipInfo.callSign, "Callsign is incorrect")
        assertEquals(56.78, mergedShipInfo.location.longitude, "Location is incorrect")
    }

    @Test
    fun `LR update is ignored if close`() {
        val pos = getPosition("2022-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
        val lrPos = getLrPosition("2022-01-01T11:02:00Z", null, 12.001, 12.0, 0.0)

        shipStateService.process(lrPos, MinimalState(pos))

        assertTrue(shipStateService.messageQueue.isEmpty())
    }

    @Test
    fun `LR update is converted if distant`() {
        val pos = getPosition("2022-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
        val lrPos = getLrPosition("2022-01-01T11:02:00Z", null, 12.01, 12.0, 0.0)

        shipStateService.process(lrPos, MinimalState(pos))

        assertEquals(1, shipStateService.messageQueue.size)
        val msg = shipStateService.messageQueue.first()
        assertEquals(lrPos.message.location?.lat, msg.location.latitude)
        assertEquals(lrPos.message.location?.lon, msg.location.longitude)
        assertEquals(lrPos.timestamp.toEpochMilli(), msg.timeLastUpdate)
    }

    @Test
    fun `LR update is ignored if old and non-moving`() {
        val pos = getPosition("2022-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
        val lrPos = getLrPosition("2022-01-01T10:40:00Z", "2022-01-01T11:02:00Z", 12.001, 12.0, 0.0)

        shipStateService.process(lrPos, MinimalState(pos))

        assertTrue(shipStateService.messageQueue.isEmpty())
    }

    @Test
    fun `LR update is converted if old but non-moving`() {
        val pos = getPosition("2022-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
        val lrPos = getLrPosition("2022-01-01T10:40:00Z", "2022-01-01T11:02:00Z", 12.001, 12.0, 5.0)

        shipStateService.process(lrPos, MinimalState(pos))

        assertEquals(1, shipStateService.messageQueue.size)
        val msg = shipStateService.messageQueue.first()
        assertEquals(lrPos.message.location?.lat, msg.location.latitude)
        assertEquals(lrPos.message.location?.lon, msg.location.longitude)
        assertEquals(lrPos.timestamp.toEpochMilli(), msg.timeLastUpdate)
    }

    @Test
    fun `old static update received, but real-time, merging with latest position`() {
        val pos = getPosition("2023-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
        val static = getStatic("2022-01-01T11:00:00Z", null, "NLRTM")

        shipStateService.process(static, MinimalState(pos))

        assertEquals(1, shipStateService.messageQueue.size)
        val msg = shipStateService.messageQueue.first()
        assertEquals(static.message.destination, msg.destination)
        assertEquals(pos.message.location?.lat, msg.location.latitude)
        assertEquals(pos.message.location?.lon, msg.location.longitude)
        assertEquals(pos.timestamp.toEpochMilli(), msg.timeLastUpdate)
    }

    @Test
    fun `old and historic position update received, NOT merging with latest static`() {
        val pos = getPosition("2022-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
            .copy(historic = true)
        val statePos = getPosition("2023-01-01T11:00:00Z", null, 0.0, 0.0, 0.0)
        val stateStatic = getStatic("2023-01-01T11:00:00Z", null, "NLRTM").let {
            it.copy(
                message = it.message.copy(
                    imo = 1,
                    shipType = AisMessage.ShipType.CARGO,
                    draught = 3.14f,
                    eta = Instant.EPOCH,
                    transponderPosition = TransponderPosition(0, 1, 2, 3)
                )
            )
        }

        shipStateService.process(pos, MinimalState(statePos, stateStatic))

        assertEquals(1, shipStateService.messageQueue.size)
        val msg = shipStateService.messageQueue.first()

        // all static fields should be null
        assertNull(msg.imoNumber)
        assertNull(msg.shipType)
        assertNull(msg.maxDraught)
        assertNull(msg.eta)
        assertNull(msg.destination)
        assertNull(msg.positionOfTransponder)

        // position fields should remain
        assertEquals(pos.message.location?.lat, msg.location.latitude)
        assertEquals(pos.message.location?.lon, msg.location.longitude)
        assertEquals(pos.timestamp.toEpochMilli(), msg.timeLastUpdate)
    }

    private fun getLrPosition(timestamp: String, receptionTimestamp: String?, lat: Double, lon: Double, speed: Double) = AisLongRangeWrapper(
        timestamp = Instant.parse(timestamp),
        receptionTimestamp = Instant.parse(receptionTimestamp ?: timestamp),
        source = "unit-test",
        subSource = null,
        AisLongRangeMessage(
            mmsi = 123456789,
            location = Location(lat, lon),
            speedOverGround = speed.toFloat()
        )
    )

    private fun getPosition(timestamp: String, receptionTimestamp: String?, lat: Double, lon: Double, speed: Double) = AisPositionWrapper(
        timestamp = Instant.parse(timestamp),
        receptionTimestamp = Instant.parse(receptionTimestamp ?: timestamp),
        source = "unit-test",
        subSource = null,
        AisPositionMessage(
            mmsi = 123456789,
            location = Location(lat, lon),
            speedOverGround = speed.toFloat()
        )
    )

    private fun getStatic(timestamp: String, receptionTimestamp: String?, destination: String?) = AisStaticWrapper(
        timestamp = Instant.parse(timestamp),
        receptionTimestamp = Instant.parse(receptionTimestamp ?: timestamp),
        source = "unit-test",
        subSource = null,
        message = AisStaticMessage(
            mmsi = 123456789,
            destination = destination
        )
    )
}
