package nl.teqplay.aisengine.eventhistory.service

import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.EventByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.PlatformTeqplayEventByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.EventByShipReadCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.PlatformTeqplayEventByShipReadCache
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.testing.event.createAisDestinationChangedEvent
import nl.teqplay.aisengine.testing.event.defaultImo
import nl.teqplay.aisengine.testing.event.defaultMmsi
import nl.teqplay.aisengine.testing.event.defaultOtherAisShipIdentifier
import nl.teqplay.aisengine.testing.event.defaultOtherImo
import nl.teqplay.aisengine.testing.event.defaultOtherMmsi
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.test.context.TestConstructor
import java.time.Instant
import java.time.temporal.ChronoUnit

@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class EventHistoryServiceTest : BaseTest() {

    private val eventByShipReadCache: EventByShipReadCache = mock()
    private val eventByAreaReadCache: EventByAreaReadCache = mock()
    private val csiService: CsiService = mock()

    private val service: EventHistoryService = EventHistoryService(
        eventByShipReadCache = eventByShipReadCache,
        eventByAreaReadCache = eventByAreaReadCache,
        platformTeqplayEventByShipReadCache = mock<PlatformTeqplayEventByShipReadCache>(),
        platformTeqplayEventByAreaReadCache = mock<PlatformTeqplayEventByAreaReadCache>(),
        csiService = csiService,
        platformBucketProperties = mock<PlatformBucketProperties>(),
    )

    companion object {
        private val timestampOne = Instant.ofEpochMilli(1685577600000)
        private val timestampTwo = timestampOne.plus(30, ChronoUnit.DAYS)

        val eventOneTimestamp: Instant = timestampOne.plus(7, ChronoUnit.DAYS)
        val eventOne = createAisDestinationChangedEvent(createdTime = eventOneTimestamp, actualTime = eventOneTimestamp)
        val eventTwoTimestamp: Instant = timestampOne.plus(14, ChronoUnit.DAYS)
        val eventTwo = createAisDestinationChangedEvent(ship = defaultOtherAisShipIdentifier, createdTime = eventTwoTimestamp, actualTime = eventTwoTimestamp)
        val eventThreeTimestamp: Instant = timestampOne.plus(8, ChronoUnit.DAYS)
        val eventThree = createAisDestinationChangedEvent(createdTime = eventThreeTimestamp, actualTime = eventThreeTimestamp)
        val eventFourTimestamp: Instant = timestampOne.plus(9, ChronoUnit.DAYS)
        val eventFour = createAisDestinationChangedEvent(createdTime = eventFourTimestamp, actualTime = eventFourTimestamp)
    }

    @Test
    fun `should find history by mmsi`() {
        whenever(eventByShipReadCache.findHistoryByMmsi(any(), eq(defaultMmsi), any())).thenReturn(sequenceOf(eventOne))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val result = service.findHistoryByMmsi(defaultMmsi, true, timeWindow)
        val expected = listOf(eventOne)

        assertEquals(expected, result.toList())
    }

    @Test
    fun `should not find history by mmsi when no data`() {
        whenever(eventByShipReadCache.findHistoryByMmsi(any(), any(), any())).thenReturn(emptySequence())
        whenever(eventByShipReadCache.findHistoryByMmsi(any(), eq(defaultMmsi), any())).thenReturn(sequenceOf(eventOne))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val result = service.findHistoryByMmsi(defaultOtherMmsi, true, timeWindow)
        val expected = emptyList<Event>()

        assertEquals(expected, result.toList())
    }

    @Test
    fun `should find history by mmsi, with related imos`() {
        whenever(eventByShipReadCache.findHistoryByMmsi(any(), eq(defaultMmsi), any())).thenReturn(sequenceOf(eventOne))
        whenever(eventByShipReadCache.findHistoryByImo(any(), eq(defaultImo), any())).thenReturn(sequenceOf(eventTwo))
        whenever(csiService.getRelevantImosByMmsi(eq(defaultMmsi), any())).thenReturn(setOf(defaultImo))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val result = service.findHistoryByMmsi(defaultMmsi, true, timeWindow)
        val expected = listOf(eventOne, eventTwo)

        assertEquals(expected, result.toList())
    }

    @Test
    fun `should find history by imo, using related mmsi`() {
        whenever(eventByShipReadCache.findHistoryByMmsi(any(), eq(defaultMmsi), any())).thenReturn(sequenceOf(eventOne))
        whenever(eventByShipReadCache.findHistoryByImo(any(), eq(defaultImo), any())).thenReturn(emptySequence())
        whenever(csiService.getRelevantMmsisByImo(eq(defaultImo), any())).thenReturn(setOf(defaultMmsi))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val result = service.findHistoryByImo(defaultImo, true, timeWindow)
        val expected = listOf(eventOne)

        assertEquals(expected, result.toList())
    }

    @Test
    fun `should find history by imo, no related mmsi`() {
        whenever(eventByShipReadCache.findHistoryByImo(any(), eq(defaultImo), any())).thenReturn(sequenceOf(eventOne))
        whenever(csiService.getRelevantMmsisByImo(any(), any())).thenReturn(emptySet())

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val result = service.findHistoryByImo(defaultImo, true, timeWindow)
        val expected = listOf(eventOne)

        assertEquals(expected, result.toList())
    }

    @Test
    fun `should find history by mmsi list`() {
        whenever(eventByShipReadCache.findHistoryByMmsi(any(), eq(defaultMmsi), any())).thenReturn(sequenceOf(eventOne))
        whenever(eventByShipReadCache.findHistoryByMmsi(any(), eq(defaultOtherMmsi), any())).thenReturn(sequenceOf(eventTwo))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val mmsis = setOf(defaultMmsi, defaultOtherMmsi)
        val result = service.findHistoryByMmsis(mmsis, true, timeWindow)
        val reversedResult = service.findHistoryByMmsis(mmsis.reversed().toSet(), true, timeWindow)
        val expected = listOf(eventOne, eventTwo)

        assertEquals(expected, result.toList())
        assertEquals(expected.reversed(), reversedResult.toList())
    }

    @Test
    fun `should find history by imo list`() {
        whenever(eventByShipReadCache.findHistoryByMmsi(any(), eq(defaultMmsi), any())).thenReturn(sequenceOf(eventOne))
        whenever(eventByShipReadCache.findHistoryByImo(any(), eq(defaultImo), any())).thenReturn(emptySequence())
        whenever(eventByShipReadCache.findHistoryByMmsi(any(), eq(defaultOtherMmsi), any())).thenReturn(sequenceOf(eventTwo))
        whenever(eventByShipReadCache.findHistoryByImo(any(), eq(defaultOtherImo), any())).thenReturn(emptySequence())
        whenever(csiService.getRelevantMmsisByImo(eq(defaultImo), any())).thenReturn(setOf(defaultMmsi))
        whenever(csiService.getRelevantMmsisByImo(eq(defaultOtherImo), any())).thenReturn(setOf(defaultOtherMmsi))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val imos = setOf(defaultImo, defaultOtherImo)
        val result = service.findHistoryByImos(imos, true, timeWindow)
        val reversedResult = service.findHistoryByImos(imos.reversed().toSet(), true, timeWindow)
        val expected = listOf(eventOne, eventTwo)

        assertEquals(expected, result.toList())
        assertEquals(expected.reversed(), reversedResult.toList())
    }

    @Test
    fun `should find history by imo and mmsis, keeping correct order`() {
        whenever(eventByShipReadCache.findHistoryByMmsi(any(), eq(defaultMmsi), any())).thenReturn(sequenceOf(eventOne, eventFour))
        whenever(eventByShipReadCache.findHistoryByImo(any(), eq(defaultImo), any())).thenReturn(sequenceOf(eventThree))
        whenever(csiService.getRelevantMmsisByImo(eq(defaultImo), any())).thenReturn(setOf(defaultMmsi))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val result = service.findHistoryByImo(defaultImo, true, timeWindow)
        val expected = listOf(eventOne, eventThree, eventFour)

        assertEquals(expected, result.toList())
    }

    @Test
    fun `should find history by bounding box`() {
        whenever(eventByAreaReadCache.findHistoryInBoundingBox(any(), any(), any(), any())).thenReturn(sequenceOf(eventOne))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val boundingBox = BoundingBox(Location(0.0, 0.0), Location(1.0, 1.0))
        val result = service.findHistoryInBoundingBox(boundingBox, true, timeWindow)
        val expected = listOf(eventOne)

        assertEquals(expected, result.toList())
    }

    @Test
    fun `should find history by polygon`() {
        whenever(eventByAreaReadCache.findHistoryInPolygon(any(), any(), any(), any())).thenReturn(sequenceOf(eventOne))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val polygon = Polygon(Location(0.0, 0.0), Location(0.0, 1.0), Location(1.0, 1.0), Location(0.0, 1.0))
        val result = service.findHistoryInPolygon(polygon, true, timeWindow)
        val expected = listOf(eventOne)

        assertEquals(expected, result.toList())
    }

    @Test
    fun `should find history by circle`() {
        whenever(eventByAreaReadCache.findHistoryInCircle(any(), any(), any(), any(), any())).thenReturn(sequenceOf(eventOne))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val center = Location(0.0, 0.0)
        val result = service.findHistoryInCircle(center, 1.0, true, timeWindow)
        val expected = listOf(eventOne)

        assertEquals(expected, result.toList())
    }

    @Test
    fun `should find history by query`() {
        whenever(eventByAreaReadCache.findHistoryInPolygon(any(), any(), any(), any())).thenReturn(sequenceOf(eventOne))

        val timeWindow = TimeWindow(timestampOne, timestampTwo)
        val polygon = Polygon(Location(0.0, 0.0), Location(0.0, 1.0), Location(1.0, 1.0), Location(0.0, 1.0))
        val result = service.findHistoryByQuery(timeWindow, polygon, null, null, null, true, null)
        val expected = listOf(eventOne)

        assertEquals(expected, result?.collectList()?.block())
    }
}
