package nl.teqplay.aisengine.eventhistory.util

import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Instant
import java.time.temporal.ChronoUnit
import nl.teqplay.platform.resource.params.TimeWindow as PlatformTimeWindow

class TimeWindowUtilsTest {
    companion object {
        private val TIMESTAMP_1 = Instant.ofEpochMilli(1685577600000)
        private val TIMESTAMP_2 = TIMESTAMP_1.plus(7, ChronoUnit.DAYS)
    }

    @Test
    fun `should converter platform time window to ais engine time window`() {
        val input = PlatformTimeWindow(TIMESTAMP_1.toEpochMilli(), TIMESTAMP_2.toEpochMilli())
        val expected = TimeWindow(TIMESTAMP_1, TIMESTAMP_2)
        val result = input.convert()

        assertEquals(expected, result)
    }

    @Test
    fun `should throw exception when from is null`() {
        val input = PlatformTimeWindow(null, TIMESTAMP_2.toEpochMilli())

        assertThrows<BadRequestException> { input.convert() }
    }

    @Test
    fun `should throw exception when to is null`() {
        val input = PlatformTimeWindow(TIMESTAMP_1.toEpochMilli(), null)

        assertThrows<BadRequestException> { input.convert() }
    }

    @Test
    fun `should throw exception when from and to are null`() {
        val input = PlatformTimeWindow()

        assertThrows<BadRequestException> { input.convert() }
    }
}
