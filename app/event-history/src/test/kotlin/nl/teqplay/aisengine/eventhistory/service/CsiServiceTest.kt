package nl.teqplay.aisengine.eventhistory.service

import nl.teqplay.csi.model.ship.mapping.ImoMmsiMapping
import nl.teqplay.csi.model.ship.mapping.ShipRegisterMapping
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.csi.client.CsiShipClient
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.TestConstructor
import java.time.Instant
import java.time.temporal.ChronoUnit

@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class CsiServiceTest(
    private val service: CsiService
) : BaseTest() {
    companion object {
        private const val TEST_IMO = 1111111
        private val timestampOne = Instant.ofEpochMilli(1685577600000)
        private val timestampOneEpochMilli = timestampOne.toEpochMilli()
        private val timestampTwo = timestampOne.plus(30, ChronoUnit.DAYS)
        private val timestampTwoEpochMilli = timestampTwo.toEpochMilli()

        private val mappingOne = ImoMmsiMapping(
            "1",
            timestampOneEpochMilli,
            null,
            timestampTwoEpochMilli,
            timestampOneEpochMilli,
            timestampTwoEpochMilli
        )
        private val mappingTwo = ImoMmsiMapping(
            "2",
            timestampTwoEpochMilli,
            null,
            null,
            timestampTwoEpochMilli,
            null
        )

        val mappings = listOf(mappingOne, mappingTwo)
        val registerMapping = listOf(
            ShipRegisterMapping(
                imo = TEST_IMO.toString(),
                mapping = mappings,
                ignored = emptySet(),
                state = "",
            ),
        )
    }

    @TestConfiguration
    class Config {
        @Bean
        fun csiService(): CsiService = CsiService(mock<CsiShipClient>())
    }

    @Test
    fun `Should get relevant mmsis by imo`() {
        whenever(service.getMmsiMappingUntilResponse()).thenReturn(registerMapping)
        service.refreshMapping()

        val timeWindow = TimeWindow(
            timestampOne.plus(7, ChronoUnit.DAYS),
            timestampTwo.minus(7, ChronoUnit.DAYS)
        )
        val result = service.getRelevantMmsisByImo(TEST_IMO, timeWindow)
        val expected = setOf(1)

        assertEquals(expected, result)
    }

    @Test
    fun `Should get no mmsis by imo on empty mapping`() {
        whenever(service.getMmsiMappingUntilResponse()).thenReturn(emptyList())
        service.refreshMapping()

        val timeWindow = TimeWindow(
            timestampOne.plus(7, ChronoUnit.DAYS),
            timestampTwo.minus(7, ChronoUnit.DAYS)
        )
        val result = service.getRelevantMmsisByImo(TEST_IMO, timeWindow)
        val expected = emptySet<Int>()

        assertEquals(expected, result)
    }

    @Test
    fun `Should get active mmsi from mmsi mappings time window outside mapping`() {
        val timeWindow = TimeWindow(
            timestampOne.minus(7, ChronoUnit.DAYS),
            timestampTwo.plus(7, ChronoUnit.DAYS)
        )

        val expected = listOf(mappingOne, mappingTwo)
        val result = mappings.getActiveMmsis(timeWindow)
        assertEquals(expected, result)
    }

    @Test
    fun `Should get active mmsi from mmsi mappings time window inside mapping`() {
        val timeWindow = TimeWindow(
            timestampOne.plus(7, ChronoUnit.DAYS),
            timestampOne.plus(14, ChronoUnit.DAYS)
        )

        val expected = listOf(mappingOne)
        val result = mappings.getActiveMmsis(timeWindow)
        assertEquals(expected, result)
    }

    @Test
    fun `Should get active mmsi from mmsi mappings for future`() {
        val timeWindow = TimeWindow(
            timestampTwo.plus(30, ChronoUnit.DAYS),
            timestampTwo.plus(60, ChronoUnit.DAYS)
        )

        val expected = listOf(mappingTwo)
        val result = mappings.getActiveMmsis(timeWindow)
        assertEquals(expected, result)
    }

    @Test
    fun `Should get empty active mmsi from mmsi mappings on from edge`() {
        val timeWindow = TimeWindow(
            timestampOne.minus(7, ChronoUnit.DAYS),
            timestampOne
        )

        val expected = emptyList<ImoMmsiMapping>()
        val result = mappings.getActiveMmsis(timeWindow)
        assertEquals(expected, result)
    }

    @Test
    fun `Should get empty active mmsi from mmsi mappings on time window outside mapping`() {
        val timeWindow = TimeWindow(
            timestampOne.minus(14, ChronoUnit.DAYS),
            timestampOne.minus(7, ChronoUnit.DAYS)
        )

        val expected = emptyList<ImoMmsiMapping>()
        val result = mappings.getActiveMmsis(timeWindow)
        assertEquals(expected, result)
    }
}
