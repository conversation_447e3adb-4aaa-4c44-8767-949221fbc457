nats:
  enabled: true

  event-stream:
    enabled: true
    password:
    url:
    username:

mongodb:
  db: event-history-processor

bucket:
  archive:
    event:
      ship:
        enabled: false
        name: event-history.teqplay
      area:
        enabled: false
        name: event-history.teqplay

event:
  logging-interval: PT1M

# Prometheus JVM stats exposing
management:
  metrics:
    enable:
      jvm: true
    tags:
      component: event-history-processor
  endpoints:
    web:
      exposure:
        include:
          - health
          - prometheus
