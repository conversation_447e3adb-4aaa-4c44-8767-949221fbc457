package nl.teqplay.aisengine.eventhistoryprocessor.support

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.eventhistoryprocessor.config.EventWriteCache
import nl.teqplay.aisengine.eventhistoryprocessor.service.EventProcessService
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.context.event.ContextClosedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

private val LOG = KotlinLogging.logger {}

@Component
class ShutdownHandler(
    private val eventWriteCaches: List<EventWriteCache<*>>,
    private val consumer: NatsConsumerStream<Event>,
    private val eventProcessService: EventProcessService
) {
    @EventListener(ContextClosedEvent::class)
    fun shutdown(event: ContextClosedEvent) {
        LOG.info { "Shutting down" }
        eventProcessService.shutdown()
        shutdownEventConsumer()
        shutdownEventWriteCaches()
        LOG.info { "Shutdown complete" }
    }

    /**
     * Helper function to shut down gracefully the event stream consumer.
     */
    private fun shutdownEventConsumer() {
        try {
            LOG.info { "Draining event stream consumer" }
            consumer.drain()
        } catch (e: Exception) {
            LOG.error(e) { "Something went wrong while draining the consumer" }
        }
    }

    /**
     * Helper function to shut down gracefully the event write caches.
     */
    private fun shutdownEventWriteCaches() {
        LOG.info { "Shutting down EventWriteCaches" }

        eventWriteCaches.parallelStream().forEach { writeCache ->
            try {
                writeCache.shutdown()
            } catch (e: Throwable) {
                LOG.warn(e) { "Error shutting down write cache ${writeCache.javaClass.name}" }
            }
        }
    }
}
