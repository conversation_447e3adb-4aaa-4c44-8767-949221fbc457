package nl.teqplay.aisengine.eventhistoryprocessor.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.config.BucketExecutor
import nl.teqplay.aisengine.bucketing.config.BucketingMetricRegistry
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.EventByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.EventByShipArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.cache.BucketWriteCache
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.EventByAreaWriteCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.EventByShipWriteCache
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration
import java.util.concurrent.ScheduledExecutorService

/**
 * An alias for the [BucketWriteCache] to easily access the beans for the [Event] and [LocationBasedEvent] write caches.
 */
internal typealias EventWriteCache<T> = BucketWriteCache<T, *, *>

@Configuration
class EventHistoryAutoConfiguration {

    @Bean
    fun eventWriteCache(
        mongoDatabase: MongoDatabase,
        objectMapper: ObjectMapper,

        bucket: BucketProperties,
        eventByEntityArchive: EventByShipArchiveProperties,
        archiveGlobal: BucketArchiveGlobalProperties,

        @BucketExecutor scheduledExecutorService: ScheduledExecutorService,
        bucketingMetricRegistry: BucketingMetricRegistry,
    ): EventWriteCache<Event> = EventByShipWriteCache(
        bucketCacheFactory = BucketCacheFactory,
        archiveStorageFactory = ArchiveStorageFactory,
        mongoDatabase = mongoDatabase,
        objectMapper = objectMapper,
        bucket = bucket,
        archive = eventByEntityArchive,
        archiveGlobal = archiveGlobal,
        scheduledExecutorService = scheduledExecutorService,
        flushInitialDelay = Duration.ZERO,
        bucketingMetricRegistry = bucketingMetricRegistry,
    )

    @Bean
    fun locationBasedEventWriteCache(
        mongoDatabase: MongoDatabase,
        objectMapper: ObjectMapper,

        bucket: BucketProperties,
        eventByAreaArchive: EventByAreaArchiveProperties,
        archiveGlobal: BucketArchiveGlobalProperties,

        @BucketExecutor scheduledExecutorService: ScheduledExecutorService,
        bucketingMetricRegistry: BucketingMetricRegistry,
    ): EventWriteCache<LocationBasedEvent> {
        val flushInterval = bucket.mongo.unordered.flush.interval

        return EventByAreaWriteCache(
            bucketCacheFactory = BucketCacheFactory,
            archiveStorageFactory = ArchiveStorageFactory,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,
            bucket = bucket,
            archive = eventByAreaArchive,
            archiveGlobal = archiveGlobal,
            scheduledExecutorService = scheduledExecutorService,
            flushInitialDelay = flushInterval.dividedBy(2),
            bucketingMetricRegistry = bucketingMetricRegistry,
        )
    }
}
