package nl.teqplay.aisengine.eventhistoryprocessor.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "event")
data class EventHistoryProcessorProperties(
    /**
     * Interval used to decide with which frequency we should log the total amount of processed events.
     */
    val loggingInterval: Duration
)
