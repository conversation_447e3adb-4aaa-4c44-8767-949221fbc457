package nl.teqplay.aisengine.eventhistoryprocessor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.aisengine.eventhistoryprocessor.config.EventWriteCache
import nl.teqplay.aisengine.eventhistoryprocessor.properties.EventHistoryProcessorProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.skeleton.nats.NatsConsumerStream
import nl.teqplay.skeleton.nats.NatsSubscriber
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.concurrent.atomic.AtomicLong

/**
 * Service containing all the logic to process all events from the Nats event stream and forward them to the [EventWriteCache]s.
 */
@Component
class EventProcessService(
    private val eventWriteCache: EventWriteCache<Event>,
    private val locationBasedEventWriteCache: EventWriteCache<LocationBasedEvent>,
    private val consumer: NatsConsumerStream<Event>,
    private val eventStreamService: EventStreamService,
    properties: EventHistoryProcessorProperties
) {
    private val LOG = KotlinLogging.logger {}

    private val eventStatsIntervalMs: Double = properties.loggingInterval.toMillis().toDouble()
    private val retrieved = AtomicLong(0)
    private val processedByShip = AtomicLong(0)
    private val processedByLocation = AtomicLong(0)

    private var subscription: NatsSubscriber? = null

    @EventListener(ApplicationReadyEvent::class)
    fun initialize() {
        LOG.info { "Starting event consuming" }
        subscription = eventStreamService.consume(consumer) { event, message ->
            process(event)
            message.ack()
        }
    }

    fun shutdown() {
        subscription?.unsubscribe()
    }

    /**
     * Process the provided event to the desired [EventWriteCache] instances.
     */
    fun process(event: Event) {
        retrieved.incrementAndGet()

        // Only insert the event in the location based write cache if we have an event containing a location
        if (event is LocationBasedEvent) {
            locationBasedEventWriteCache.insert(event)
            processedByLocation.incrementAndGet()
        }

        eventWriteCache.insert(event)
        processedByShip.incrementAndGet()
    }

    /**
     * Function that prints out the total amount of events processed per minute per category.
     */
    @Scheduled(initialDelayString = "\${event.logging-interval}", fixedRateString = "\${event.logging-interval}")
    private fun logConverterStatistics() {
        val retrieved = retrieved.getAndSet(0)
        val processedByShip = processedByShip.getAndSet(0)
        val processedByLocation = processedByLocation.getAndSet(0)

        LOG.info {
            buildString {
                append("Retrieved $retrieved events (${countPerSecond(retrieved)} per second), ")
                append("Processed by ship $processedByShip events (${countPerSecond(processedByShip)} per second), ")
                append("Processed by location $processedByLocation events (${countPerSecond(processedByLocation)} per second)")
            }
        }
    }

    /**
     * Helper function to convert the amount of processed events to the amount per second
     */
    private fun countPerSecond(count: Long) = (count.toDouble() * 1000 / eventStatsIntervalMs).toLong()
}
