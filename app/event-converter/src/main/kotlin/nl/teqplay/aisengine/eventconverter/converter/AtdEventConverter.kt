package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.hamis.HamisAtdEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.AtdEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component

@Component
class AtdEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : PlatformConverter<HamisAtdEvent, AtdEvent>(
    properties = properties,
    converter = EventConverterProperties.Converters.HAMIS,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun toAisEngineEvent(input: AtdEvent): HamisAtdEvent? {
        return aisEngineConverter.convert<HamisAtdEvent>(input)
    }
}
