package nl.teqplay.aisengine.eventconverter.properties

import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.skeleton.model.BoundingBox
import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = EventConverterProperties.PREFIX)
data class EventConverterProperties(
    /**
     * A map containing all converter settings using [Converters] as the key of each entry
     */
    val converters: Map<Converters, EnabledConverter> = emptyMap(),

    /**
     * Interval used when to log statistics about the converters to console
     */
    val loggingInterval: Duration,

    val rabbitmq: RabbitMq,

    val area: AreaEventConfig,

    /**
     * The area where each event should be located in. If the event happened outside the [BoundingBox], ignore it.
     * When set to null, accept all events.
     */
    val boundingBox: BoundingBox? = null
) {
    data class RabbitMq(
        /**
         * Settings for incoming Platform events
         */
        val incoming: Incoming,

        /**
         * Settings for outgoing Platform events
         */
        val outgoing: Outgoing
    ) {
        data class Incoming(
            val consume: Boolean,
            val uri: String,
            val queue: String,
            val qos: Int
        )

        data class Outgoing(
            val uri: String,
            val exchange: String
        )
    }

    companion object {
        const val PREFIX = "event"
    }

    enum class Converters {
        AIS_DRAUGHT_CHANGED,
        AIS_ETA_CHANGED,
        AIS_STATUS_CHANGED,
        ANCHORED,
        BERTH,
        AREA_START,
        AREA_END,
        ENCOUNTER_START,
        ENCOUNTER_END,
        SHIP_MOVING_START,
        SHIP_MOVING_END,
        TRUE_DESTINATION_CHANGED,
        HAMIS,
        PORTCALL_PILOT_BOARDING_ETA,
        LOCK_ETA_ETD
    }

    enum class EnabledConverter {
        // Converting of events is disabled
        DISABLED,

        // Indicates that the converter should forward AisEngine events to Platform
        AISENGINE,

        // Indicates that the converter should forward Platform events to AisEngine
        PLATFORM,

        // Indicates that the converter should forward event both ways
        BOTH
    }

    data class AreaEventConfig(
        val berthEventsEnabled: Boolean
    )
}
