package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.EnabledConverter
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.platform.converter.AisEngineEventConverter
import nl.teqplay.aisengine.platform.converter.PlatformEventConverter
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import nl.teqplay.skeleton.util.pointInBoundingBox

/**
 * Base converter class which will handle sending events bidirectional.
 * This class will also provide two convert functions that need to be implemented, [toAisEngineEvent] and [toPlatformEvent].
 *
 * @param T the type of the new Event model
 * @param S the type of the old TeqplayEvent model
 * @param properties the settings for all converters to configure Nats and RabbitMQ
 * @param converter the converter key used to select the corresponding converter settings
 */
@Suppress("UNCHECKED_CAST")
abstract class Converter<T : Event, S : TeqplayEvent>(
    private val properties: EventConverterProperties,
    private val converter: EventConverterProperties.Converters,
    private val objectMapper: ObjectMapper,
    private val rabbitMqEventSender: RabbitMqEventSender,
    private val eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) {
    private val LOG = KotlinLogging.logger {}

    private val metricRegistry = MetricRegistry.of<Converter<T, S>>(meterRegistry, listOf("converter", "converter-for"))
    private val converterPlatformStatistics = ConverterStatistics(properties, metricRegistry, converter, true)
    private val converterAisEngineStatistics = ConverterStatistics(properties, metricRegistry, converter, false)
    var platformToAisEngineEnabled: Boolean = false
    var aisEngineToPlatformEnabled: Boolean = false

    val aisEngineConverter = AisEngineEventConverter<T, S>()
    val platformConverter = PlatformEventConverter<S, T>()

    fun initialize() {
        // Find the converter configuration based on the provided converter
        // When not found, don't set up anything
        val converterEnabled = properties.converters[converter] ?: return LOG.warn { "[$converter] Could not initialize converter, no config found" }

        when (converterEnabled) {
            // Nothing needed to set up when both ways are disabled
            EnabledConverter.DISABLED -> return LOG.info { "[$converter] Converter not enabled" }
            EnabledConverter.PLATFORM -> platformToAisEngineEnabled = true
            EnabledConverter.AISENGINE -> aisEngineToPlatformEnabled = true
            EnabledConverter.BOTH -> {
                platformToAisEngineEnabled = true
                aisEngineToPlatformEnabled = true
            }
        }

        if (platformToAisEngineEnabled) {
            LOG.info { "[$converter] Enabled processing of Platform events" }
        }

        if (aisEngineToPlatformEnabled) {
            LOG.info { "[$converter] Enabled processing of AisEngine events" }
        }
    }

    /**
     * Process an AisEngine event:
     * - Convert the AisEngine event to the corresponding Platform TeqplayEvent model
     * - Publish the result to the configured RabbitMq exchange, with optionally the provided routing key
     */
    fun processAisEngineEvent(input: Event) {
        // Only process the event when it is enabled in the configuration
        if (!aisEngineToPlatformEnabled) return

        // Only process the AisEngine event that is configured for this converter, skip other types
        val castInput = input as? T ?: return

        converterAisEngineStatistics.countReceived()

        // Only continue processing an event if it should be forwarded to the configured Platform server.
        if (!shouldForwardToPlatform(input)) {
            return
        }

        val platformEvent = try {
            toPlatformEvent(castInput)?.also { converterAisEngineStatistics.countConverted() }
        } catch (_: ClassCastException) {
            // Ignore all ClassCastExceptions
            LOG.trace { "[$converter] Skipping AisEngine event [${castInput::class.simpleName}]" }
            null
        }

        if (platformEvent != null) {
            LOG.debug { "[$converter] Sending Platform [${platformEvent::class.simpleName}] converted from AisEngine [${castInput::class.simpleName}] to NATs [${castInput.getSubject()}]" }

            val message = objectMapper.writeValueAsString(platformEvent)
            val routingKey = generateRoutingKey(platformEvent)

            if (routingKey != null) {
                rabbitMqEventSender.send(properties.rabbitmq.outgoing.exchange, message.toByteArray(), routingKey)
            } else {
                rabbitMqEventSender.send(properties.rabbitmq.outgoing.exchange, message.toByteArray())
            }

            converterAisEngineStatistics.countSent()
        }
    }

    /**
     * Process a Platform TeqplayEvent:
     * - Convert the Platform event to the corresponding AisEvent model
     * - Publish the result to the configured Nats subject
     */
    fun processPlatformEvent(input: TeqplayEvent) {
        // Only process the event when it is enabled in the configuration
        if (!platformToAisEngineEnabled) return

        // Only process the Platform event that is configured for this converter, skip other types
        val castInput = input as? S ?: return

        converterPlatformStatistics.countReceived()

        val aisEngineEvent = try {
            toAisEngineEvent(castInput)?.also { converterPlatformStatistics.countConverted() }
        } catch (_: ClassCastException) {
            LOG.trace { "[$converter] Skipping Platform event [${castInput::class.simpleName}]" }
            // Ignore all ClassCastExceptions
            null
        }

        if (aisEngineEvent != null) {
            LOG.debug { "[$converter] Sending AisEngine [${aisEngineEvent::class.simpleName}] converted from Platform [${castInput::class.simpleName}] to NATs [${aisEngineEvent.getSubject()}]" }

            // Publish the event to nats when everything passed
            eventStreamService.publish(aisEngineEvent)
            converterPlatformStatistics.countSent()
        }
    }

    fun logStatistics() {
        if (platformToAisEngineEnabled) {
            LOG.info { "[$converter] Platform to AisEngine: ${converterPlatformStatistics.printStatistics()}" }
            converterPlatformStatistics.reset()
        }

        if (aisEngineToPlatformEnabled) {
            LOG.info { "[$converter] AisEngine to Platform: ${converterAisEngineStatistics.printStatistics()}" }
            converterAisEngineStatistics.reset()
        }
    }

    /**
     * Check if the given [input] [Event] should be forwarded to the configured RabbitMQ of a Platform.
     *
     * @param input The AisEngine [Event] that will be used.
     * @return True when either:
     * - The [EventConverterProperties.boundingBox] isn't configured.
     * - The provided event is not a location based event.
     * - The provided event is inside the configured convert area.
     */
    private fun shouldForwardToPlatform(input: T): Boolean {
        // When no bounding box is configured, allow all events to be sent to the specific platform
        val boundingBox = properties.boundingBox ?: return true

        return when (input) {
            is LocationBasedEvent -> pointInBoundingBox(boundingBox, input.location)
            // Events without a location field can't be filtered via bounding box, allow all.
            else -> true
        }
    }

    /**
     * Converts a Platform event to the AisEngine model
     *
     * @param input the Platform event
     * @return the AisEngine event
     */
    abstract fun toAisEngineEvent(input: S): T?

    /**
     * Converts an AisEngine event to the Platform model
     *
     * @param input the AisEngine event
     * @return the Platform event
     */
    abstract fun toPlatformEvent(input: T): S?

    /**
     * Generate a routing key needed by RabbitMQ to send together with the already configured exchange.
     * We need to do this based on the Platform event that has been converted as we want to create routing keys
     * containing the mmsi of a ship for example which is only known in the event itself.
     *
     * @param input an AisEngine event that has been converted to a Platform event
     * @return a RabbtiMQ routing key
     */
    abstract fun generateRoutingKey(input: S): String?
}
