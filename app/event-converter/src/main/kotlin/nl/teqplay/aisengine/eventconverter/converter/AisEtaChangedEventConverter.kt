package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.AisEtaChangedEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.util.toShipIdentifier
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.platform.toLocation
import nl.teqplay.aisengine.platform.toPlatformLocation
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component
import java.time.Instant
import nl.teqplay.platform.model.event.AisEtaChangedEvent as PlatformAisEtaChangedEvent

@Component
class AisEtaChangedEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry,
) : Converter<AisEtaChangedEvent, PlatformAisEtaChangedEvent>(
    properties = properties,
    converter = EventConverterProperties.Converters.AIS_ETA_CHANGED,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun toAisEngineEvent(input: PlatformAisEtaChangedEvent): AisEtaChangedEvent? {
        val ship = input.toShipIdentifier() ?: return null
        val oldEta = input.oldEta?.let { Instant.ofEpochMilli(it) }
        val newEta = input.newEta?.let { Instant.ofEpochMilli(it) }

        return AisEtaChangedEvent(
            _id = input.uuid,
            ship = ship,
            location = input.location.toLocation(),
            oldValue = oldEta,
            newValue = newEta,
            createdTime = Instant.ofEpochMilli(input.datetime),
            actualTime = Instant.ofEpochMilli(input.eventTime),
            deleted = input.isDeleted,
            regenerated = null
        )
    }

    override fun toPlatformEvent(input: AisEtaChangedEvent): PlatformAisEtaChangedEvent {
        val description = "Eta changed in AIS (" + input.oldValue + " to " + input.newValue + ")"

        return PlatformAisEtaChangedEvent(
            uuid = input._id,
            timestamp = input.createdTime.toEpochMilli(),
            shipMmsi = input.ship.mmsi.toString(),
            description = description,
            title = description,
            summary = description,
            location = input.location.toPlatformLocation(),
            oldEta = input.oldValue?.toEpochMilli(),
            newEta = input.newValue?.toEpochMilli()
        )
    }

    override fun generateRoutingKey(input: PlatformAisEtaChangedEvent): String {
        return TeqplayEvent.AIS_ETA_CHANGED
    }
}
