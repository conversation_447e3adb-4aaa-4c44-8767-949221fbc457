package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.ShipMovingEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component

@Component
class ShipMovingStartEndEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : StartEndConverter<ShipMovingStartEvent, ShipMovingEndEvent, ShipMovingEvent>(
    properties = properties,
    startConverter = EventConverterProperties.Converters.SHIP_MOVING_START,
    endConverter = EventConverterProperties.Converters.SHIP_MOVING_END,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun generateStartEventRoutingKey(input: ShipMovingEvent): String {
        return "ship.movement.start"
    }

    override fun generateEndEventRoutingKey(input: ShipMovingEvent): String {
        return "ship.movement.end"
    }

    override fun toAisEngineStartEvent(input: ShipMovingEvent): ShipMovingStartEvent? {
        return startConverter.aisEngineConverter.convert<ShipMovingStartEvent>(input)
    }

    override fun toPlatformStartEvent(input: ShipMovingStartEvent): ShipMovingEvent? {
        return startConverter.platformConverter.convert<ShipMovingEvent>(input)
    }

    override fun toAisEngineEndEvent(input: ShipMovingEvent): ShipMovingEndEvent? {
        return endConverter.aisEngineConverter.convert<ShipMovingEndEvent>(input)
    }

    override fun toPlatformEndEvent(input: ShipMovingEndEvent): ShipMovingEvent? {
        return endConverter.platformConverter.convert<ShipMovingEvent>(input)
    }
}
