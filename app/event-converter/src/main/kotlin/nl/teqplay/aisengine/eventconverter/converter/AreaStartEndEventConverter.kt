package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.BerthEvent
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component

@Component
class AreaStartEndEventConverter(
    val properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : StartEndConverter<AreaStartEvent, AreaEndEvent, TeqplayLocationBasedEvent>(
    properties = properties,
    startConverter = EventConverterProperties.Converters.AREA_START,
    endConverter = EventConverterProperties.Converters.AREA_END,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    val customAreaEventType = "area.newevents"

    override fun toAisEngineStartEvent(input: TeqplayLocationBasedEvent): AreaStartEvent? {
        return null
    }

    override fun toPlatformStartEvent(input: AreaStartEvent): TeqplayLocationBasedEvent? {
        return startConverter.platformConverter.convert<TeqplayLocationBasedEvent>(input)
            .takeUnless { (it is BerthEvent && !properties.area.berthEventsEnabled) }
    }

    override fun generateStartEventRoutingKey(input: TeqplayLocationBasedEvent): String {
        // Fix for when custom event area types do not start with area.
        // But we still need to put it on rabbitmq to the right routing key
        if (input.type.startsWith("area.")) return input.type
        return customAreaEventType
    }

    override fun toAisEngineEndEvent(input: TeqplayLocationBasedEvent): AreaEndEvent? {
        return null
    }

    override fun toPlatformEndEvent(input: AreaEndEvent): TeqplayLocationBasedEvent? {
        return endConverter.platformConverter.convert<TeqplayLocationBasedEvent>(input)
            .takeUnless { (it is BerthEvent && !properties.area.berthEventsEnabled) }
    }

    override fun generateEndEventRoutingKey(input: TeqplayLocationBasedEvent): String {
        // Fix for when custom event area types do not start with area.
        // But we still need to put it on rabbitmq to the right routing key
        if (input.type.startsWith("area.")) return input.type
        return customAreaEventType
    }
}
