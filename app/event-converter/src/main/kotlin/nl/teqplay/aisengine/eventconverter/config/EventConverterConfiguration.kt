package nl.teqplay.aisengine.eventconverter.config

import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.eventconverter.service.ConverterService
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.context.annotation.Configuration

@Configuration
class EventConverterConfiguration(
    private val converterService: ConverterService,
    private val consumer: NatsConsumerStream<Event>,
    private val eventStreamService: EventStreamService,
) {
    @PostConstruct
    fun configureConverters() {
        val converters = converterService.getAisEngineEventConverters()

        if (converters.isNotEmpty()) {
            eventStreamService.consume(consumer) { event, message ->
                converters.forEach { it.processAisEngineEvent(event) }
                message.ack()
            }
        }
    }
}
