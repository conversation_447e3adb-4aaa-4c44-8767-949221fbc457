package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.EncounterEndEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.platform.fromPlatform
import nl.teqplay.aisengine.platform.toPlatformEvent
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component

@Component
class EncounterStartEndEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : StartEndConverter<EncounterStartEvent, EncounterEndEvent, TeqplayLocationBasedEvent>(
    properties = properties,
    startConverter = EventConverterProperties.Converters.ENCOUNTER_START,
    endConverter = EventConverterProperties.Converters.ENCOUNTER_END,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun toAisEngineStartEvent(input: TeqplayLocationBasedEvent): EncounterStartEvent? {
        return if (input.type.startsWith("ship-ship") && input.isStartEvent) {
            fromPlatform(input) as? EncounterStartEvent
        } else null
    }

    override fun toPlatformStartEvent(input: EncounterStartEvent) = input.toPlatformEvent()

    override fun generateStartEventRoutingKey(input: TeqplayLocationBasedEvent): String = input.type

    override fun toAisEngineEndEvent(input: TeqplayLocationBasedEvent): EncounterEndEvent? {
        return if (input.type.startsWith("ship-ship") && input.isEndEvent) {
            fromPlatform(input) as? EncounterEndEvent
        } else null
    }

    override fun toPlatformEndEvent(input: EncounterEndEvent) = input.toPlatformEvent()

    override fun generateEndEventRoutingKey(input: TeqplayLocationBasedEvent): String = input.type
}
