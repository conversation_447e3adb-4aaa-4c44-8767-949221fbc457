package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.util.toShipIdentifier
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.platform.toLegacyStatus
import nl.teqplay.aisengine.platform.toLocation
import nl.teqplay.aisengine.platform.toPlatformLocation
import nl.teqplay.aisengine.platform.toShipInfo
import nl.teqplay.aisengine.platform.toStatus
import nl.teqplay.platform.model.event.StatusChangedEvent
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component
import java.time.Instant

@Component
class AisStatusChangedEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : Converter<AisStatusChangedEvent, StatusChangedEvent>(
    properties = properties,
    converter = EventConverterProperties.Converters.AIS_STATUS_CHANGED,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun toAisEngineEvent(input: StatusChangedEvent): AisStatusChangedEvent? {
        val ship = input.toShipIdentifier() ?: return null
        val newStatus = input.newStatus ?: return null

        return AisStatusChangedEvent(
            _id = input.uuid,
            ship = ship,
            location = input.location.toLocation(),
            oldValue = input.oldStatus?.toStatus(),
            newValue = newStatus.toStatus(),
            createdTime = Instant.ofEpochMilli(input.datetime),
            actualTime = Instant.ofEpochMilli(input.eventTime),
            deleted = input.isDeleted,
            regenerated = null
        )
    }

    override fun toPlatformEvent(input: AisStatusChangedEvent): StatusChangedEvent {
        val description = "AIS status changed (${input.oldValue} to ${input.newValue})"

        return StatusChangedEvent(
            uuid = input._id,
            timestamp = input.createdTime.toEpochMilli(),
            description = description,
            title = description,
            summary = description,
            ship = input.ship.toShipInfo(),
            location = input.location.toPlatformLocation(),
            oldStatus = input.oldValue?.toLegacyStatus(),
            newStatus = input.newValue?.toLegacyStatus()
        )
    }

    override fun generateRoutingKey(input: StatusChangedEvent): String {
        return TeqplayEvent.STATUS_CHANGED
    }
}
