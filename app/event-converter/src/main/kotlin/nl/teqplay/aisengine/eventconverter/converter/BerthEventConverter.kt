package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.ConfirmedBerthEndEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthStartEvent
import nl.teqplay.aisengine.event.model.UniqueBerthEndEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.platform.converter.BerthInformationResolver
import nl.teqplay.platform.model.event.ConfirmedBerthEvent
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.platform.model.event.UniqueBerthEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component
import nl.teqplay.aisengine.event.interfaces.RelatedBerthEvent as AisEngineBerthEvent

@Component
class BerthEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry,
    private val berthConverter: BerthInformationResolver
) : Converter<AisEngineBerthEvent, TeqplayLocationBasedEvent>(
    properties = properties,
    converter = Converters.BERTH,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun toAisEngineEvent(input: TeqplayLocationBasedEvent): AisEngineBerthEvent? {
        return when (input) {
            is UniqueBerthEvent -> {
                if (input.isStartEvent) {
                    aisEngineConverter.convert<UniqueBerthStartEvent>(input)
                } else {
                    aisEngineConverter.convert<UniqueBerthEndEvent>(input)
                }
            }
            is ConfirmedBerthEvent -> {
                if (input.isStartEvent) {
                    aisEngineConverter.convert<ConfirmedBerthStartEvent>(input)
                } else {
                    aisEngineConverter.convert<ConfirmedBerthEndEvent>(input)
                }
            }
            else -> null
        }
    }

    override fun toPlatformEvent(input: AisEngineBerthEvent): TeqplayLocationBasedEvent? {
        return when (input) {
            is UniqueBerthStartEvent,
            is UniqueBerthEndEvent -> platformConverter.convert<UniqueBerthEvent>(input, berthConverter)
            is ConfirmedBerthStartEvent,
            is ConfirmedBerthEndEvent -> platformConverter.convert<ConfirmedBerthEvent>(input, berthConverter)
            else -> null
        }
    }

    override fun generateRoutingKey(input: TeqplayLocationBasedEvent): String? {
        return input.type
    }
}
