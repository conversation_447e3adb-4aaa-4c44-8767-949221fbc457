package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.HamisEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotOnBoardEndEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotOnBoardStartEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.platform.converter.toAisEngineEvent
import nl.teqplay.platform.model.event.hbr.AddPortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.AgentOrderEvent
import nl.teqplay.platform.model.event.hbr.AgentReportsTugsEvent
import nl.teqplay.platform.model.event.hbr.CancelPortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.EtaCancelEvent
import nl.teqplay.platform.model.event.hbr.EtaRequestEvent
import nl.teqplay.platform.model.event.hbr.EtdRequestEvent
import nl.teqplay.platform.model.event.hbr.NauticalOrderEvent
import nl.teqplay.platform.model.event.hbr.PilotOnBoardEvent
import nl.teqplay.platform.model.event.hbr.ProntoEvent
import nl.teqplay.platform.model.event.hbr.UpdatePortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.VisitCancellationEvent
import nl.teqplay.platform.model.event.hbr.VisitDeclarationEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component
import nl.teqplay.platform.model.event.hbr.HamisPilotBoardingEtaEvent as PlatformHamisPilotBoardingEtaEvent

@Component
class ProntoEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : PlatformConverter<HamisEvent, ProntoEvent>(
    properties = properties,
    converter = EventConverterProperties.Converters.HAMIS,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun toAisEngineEvent(input: ProntoEvent): HamisEvent? {

        return when (input) {
            is AddPortcallVisitEvent -> input.toAisEngineEvent()
            is UpdatePortcallVisitEvent -> input.toAisEngineEvent()
            is CancelPortcallVisitEvent -> input.toAisEngineEvent()
            is AgentOrderEvent -> input.toAisEngineEvent()
            is AgentReportsTugsEvent -> input.toAisEngineEvent()
            is EtaCancelEvent -> input.toAisEngineEvent()
            is EtaRequestEvent -> input.toAisEngineEvent()
            is EtdRequestEvent -> input.toAisEngineEvent()
            is PlatformHamisPilotBoardingEtaEvent -> input.toAisEngineEvent()
            is NauticalOrderEvent -> input.toAisEngineEvent()
            is PilotOnBoardEvent -> {
                return if (input.isStartEvent) {
                    aisEngineConverter.convert<HamisPilotOnBoardStartEvent>(input)
                } else {
                    aisEngineConverter.convert<HamisPilotOnBoardEndEvent>(input)
                }
            }
            is VisitCancellationEvent -> input.toAisEngineEvent()
            is VisitDeclarationEvent -> input.toAisEngineEvent()
            else -> null
        }
    }
}
