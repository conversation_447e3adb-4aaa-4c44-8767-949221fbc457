package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.hamis.HamisAtaEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.AtaEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component

@Component
class AtaEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : PlatformConverter<HamisAtaEvent, AtaEvent>(
    properties = properties,
    converter = EventConverterProperties.Converters.HAMIS,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun toAisEngineEvent(input: AtaEvent): HamisAtaEvent? {
        return aisEngineConverter.convert<HamisAtaEvent>(input)
    }
}
