package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.AnchoredEvent
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component
import nl.teqplay.aisengine.event.interfaces.AnchoredEvent as AisEngineAnchoredEvent

@Component
class AnchoredEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : Converter<AisEngineAnchoredEvent, AnchoredEvent>(
    properties = properties,
    converter = Converters.ANCHORED,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun toAisEngineEvent(input: AnchoredEvent): AisEngineAnchoredEvent? {
        return aisEngineConverter.convert<AisEngineAnchoredEvent>(input)
    }

    override fun toPlatformEvent(input: AisEngineAnchoredEvent): AnchoredEvent? {
        return platformConverter.convert<AnchoredEvent>(input)
    }

    override fun generateRoutingKey(input: AnchoredEvent): String? {
        return TeqplayEvent.ANCHORED
    }
}
