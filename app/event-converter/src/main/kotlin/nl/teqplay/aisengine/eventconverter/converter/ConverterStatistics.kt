package nl.teqplay.aisengine.eventconverter.converter

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import java.time.Duration
import java.util.concurrent.atomic.AtomicLong

/**
 * Wrapper class that is used to count steps of the converter, so it can later be used to log in console
 */
class ConverterStatistics<C : Converter<T, S>, T : Event, S : TeqplayEvent>(
    private val properties: EventConverterProperties,
    metricRegistry: MetricRegistry<C>,
    converter: EventConverterProperties.Converters,
    isPlatformStatistics: Boolean
) {
    private val received = AtomicLong()
    private val converted = AtomicLong()
    private val sent = AtomicLong()
    private val totalReceived: AtomicLong
    private val totalConverted: AtomicLong
    private val totalSent: AtomicLong

    init {
        val converterFor = if (isPlatformStatistics) {
            "platform"
        } else {
            "ais-engine"
        }

        totalReceived = metricRegistry.createGauge(Metric.MESSAGE_COUNT_INPUT, AtomicLong(), converter.name, converterFor)
        totalConverted = metricRegistry.createGauge(Metric.MESSAGE_COUNT_PROCESSED, AtomicLong(), converter.name, converterFor)
        totalSent = metricRegistry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong(), converter.name, converterFor)
    }

    fun countReceived() {
        received.incrementAndGet()
        totalReceived.incrementAndGet()
    }

    fun countConverted() {
        converted.incrementAndGet()
        totalConverted.incrementAndGet()
    }

    fun countSent() {
        sent.incrementAndGet()
        totalSent.incrementAndGet()
    }

    /**
     * Reset the converter statistics all back 0
     */
    fun reset() {
        received.set(0)
        converted.set(0)
        sent.set(0)
    }

    fun printStatistics(): String {
        return "Receiving ${received.get()} (${countPerMinute(received)} per minute), " +
            "Converted ${converted.get()} (${countPerMinute(converted)} per minute), " +
            "Sent ${sent.get()} (${countPerMinute(sent)} per minute)"
    }

    private fun countPerMinute(count: AtomicLong): Long {
        val oneMinuteInMs = Duration.ofMinutes(1).toMillis().toDouble()
        val eventStatsIntervalMs: Double = properties.loggingInterval.toMillis().toDouble()
        return (count.get().toDouble() * oneMinuteInMs / eventStatsIntervalMs).toLong()
    }
}
