package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender

/**
 * Wrapper class that has two [Converter] instances. [startConverter] for [StartEvent] and [endConverter] for [EndEvent].
 *
 * @property T The start event in AisEngine.
 * @property S The end event in AisEngine.
 * @property U The event in platform.
 */
abstract class StartEndConverter<T : StartEvent, S : EndEvent, U : TeqplayEvent>(
    properties: EventConverterProperties,
    startConverter: EventConverterProperties.Converters,
    endConverter: EventConverterProperties.Converters,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry,
) {
    val startConverter: Converter<T, U> = StartConverter(
        properties = properties,
        converter = startConverter,
        objectMapper = objectMapper,
        rabbitMqEventSender = rabbitMqEventSender,
        eventStreamService = eventStreamService,
        meterRegistry = meterRegistry,
        toAisEngineStartEvent = ::toAisEngineStartEvent,
        toPlatformStartEvent = ::toPlatformStartEvent,
        generateStartEventRoutingKey = ::generateStartEventRoutingKey
    )

    val endConverter: Converter<S, U> = EndConverter(
        properties = properties,
        converter = endConverter,
        objectMapper = objectMapper,
        rabbitMqEventSender = rabbitMqEventSender,
        eventStreamService = eventStreamService,
        meterRegistry = meterRegistry,
        toAisEngineEndEvent = ::toAisEngineEndEvent,
        toPlatformEndEvent = ::toPlatformEndEvent,
        generateEndEventRoutingKey = ::generateEndEventRoutingKey
    )

    /**
     * Helper class that can converter [StartEvent] and [TeqplayEvent] both ways.
     *
     * @property T The start event in AisEngine.
     * @property U The event in platform.
     */
    private class StartConverter<T : StartEvent, U : TeqplayEvent>(
        properties: EventConverterProperties,
        converter: EventConverterProperties.Converters,
        objectMapper: ObjectMapper,
        rabbitMqEventSender: RabbitMqEventSender,
        eventStreamService: EventStreamService,
        meterRegistry: MeterRegistry,
        private val toAisEngineStartEvent: (U) -> T?,
        private val toPlatformStartEvent: (T) -> U?,
        private val generateStartEventRoutingKey: (U) -> String?
    ) : Converter<T, U>(properties, converter, objectMapper, rabbitMqEventSender, eventStreamService, meterRegistry) {
        override fun toAisEngineEvent(input: U): T? = toAisEngineStartEvent(input)
        override fun toPlatformEvent(input: T): U? = toPlatformStartEvent(input)
        override fun generateRoutingKey(input: U): String? = generateStartEventRoutingKey(input)
    }

    /**
     * Helper class that can converter [EndEvent] and [TeqplayEvent] both ways.
     *
     * @property S The end event in AisEngine.
     * @property U The event in platform.
     */
    private class EndConverter<S : EndEvent, U : TeqplayEvent>(
        properties: EventConverterProperties,
        converter: EventConverterProperties.Converters,
        objectMapper: ObjectMapper,
        rabbitMqEventSender: RabbitMqEventSender,
        eventStreamService: EventStreamService,
        meterRegistry: MeterRegistry,
        private val toAisEngineEndEvent: (U) -> S?,
        private val toPlatformEndEvent: (S) -> U?,
        private val generateEndEventRoutingKey: (U) -> String?
    ) : Converter<S, U>(properties, converter, objectMapper, rabbitMqEventSender, eventStreamService, meterRegistry) {
        override fun toAisEngineEvent(input: U): S? = toAisEngineEndEvent(input)
        override fun toPlatformEvent(input: S): U? = toPlatformEndEvent(input)
        override fun generateRoutingKey(input: U): String? = generateEndEventRoutingKey(input)
    }

    /**
     * Converts a Platform event to the AisEngine [StartEvent] equivalent.
     *
     * @param input the Platform event.
     * @return the AisEngine [StartEvent].
     * @see Converter.toAisEngineEvent
     */
    abstract fun toAisEngineStartEvent(input: U): T?

    /**
     * Converts an AisEngine [StartEvent] to the Platform model.
     *
     * @param input the AisEngine [StartEvent].
     * @return the Platform event.
     * @see Converter.toPlatformEvent
     */
    abstract fun toPlatformStartEvent(input: T): U?

    /**
     * @see Converter.generateRoutingKey
     */
    abstract fun generateStartEventRoutingKey(input: U): String?

    /**
     * Converts a Platform event to the AisEngine [EndEvent] equivalent.
     *
     * @param input the Platform event.
     * @return the AisEngine [EndEvent].
     * @see Converter.toAisEngineEvent
     */
    abstract fun toAisEngineEndEvent(input: U): S?

    /**
     * Converts an AisEngine [EndEvent] to the Platform model.
     *
     * @param input the AisEngine [EndEvent].
     * @return the Platform event.
     * @see Converter.toPlatformEvent
     */
    abstract fun toPlatformEndEvent(input: S): U?

    /**
     * @see Converter.generateRoutingKey
     */
    abstract fun generateEndEventRoutingKey(input: U): String?
}
