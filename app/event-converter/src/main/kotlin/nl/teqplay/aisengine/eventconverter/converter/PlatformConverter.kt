package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender

/**
 * [Converter] class used only to convert a Platform event to the AisEngine model.
 * Removing the functionality to convert from AisEngine to Platform.
 */
abstract class PlatformConverter<T : Event, S : TeqplayEvent>(
    properties: EventConverterProperties,
    converter: EventConverterProperties.Converters,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : Converter<T, S>(properties, converter, objectMapper, rabbitMqEventSender, eventStreamService, meterRegistry) {
    /**
     * We only forward Platform events to AisEngine, so no need to implement this
     */
    override fun toPlatformEvent(input: T): S? {
        return null
    }

    override fun generateRoutingKey(input: S): String? {
        return null
    }
}
