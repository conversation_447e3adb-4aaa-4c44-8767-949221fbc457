package nl.teqplay.aisengine.eventconverter.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.platform.converter.BerthInformationResolver
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.skeleton.poma.client.PomaInfrastructureClient
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.util.concurrent.ConcurrentHashMap

private val LOG = KotlinLogging.logger { }

@Service
class BerthResolverService(
    private val pomaClient: PomaInfrastructureClient
) : BerthInformationResolver {
    private var pomaIdToAuthorityId = ConcurrentHashMap<String, String>()
    private var pomaIdToName = ConcurrentHashMap<String, String>()

    init {
        refreshBerths()
    }

    @Scheduled(cron = "0 0 8 * * *")
    private fun refreshBerths() {
        try {
            val allBerths = pomaClient.getBerths()
            val idToAuthorityId = mutableMapOf<String, String>()
            val idToName = mutableMapOf<String, String>()
            allBerths.forEach { berth: Berth ->
                val berthId = berth._id ?: return@forEach
                // Mapped port's should have there authorityId in the uniqueId, so can be used as a fallback
                val authorityId = berth.authorityId?.takeIf(String::isNotBlank) ?: berth.uniqueId
                val berthName = berth.nameLong?.takeIf(String::isNotBlank) ?: berth.name

                if (authorityId != null) {
                    idToAuthorityId[berthId] = authorityId
                }
                idToName[berthId] = berthName
            }

            // We can't refresh if we didn't have any berths with an id and unique id
            if (idToAuthorityId.isNotEmpty()) {
                pomaIdToAuthorityId = ConcurrentHashMap(idToAuthorityId.toMap())
            }
            if (idToName.isNotEmpty()) {
                pomaIdToName = ConcurrentHashMap(idToName.toMap())
            }
        } catch (e: Exception) {
            LOG.error { "Berth refresh failed $e" }
        }
    }

    override fun getPlatformBerthId(eventBerthId: String): String? {
        return pomaIdToAuthorityId[eventBerthId]
    }

    override fun getPomaLongName(eventBerthId: String): String? {
        return pomaIdToName[eventBerthId]
    }
}
