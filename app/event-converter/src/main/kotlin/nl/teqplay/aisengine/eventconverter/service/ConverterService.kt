package nl.teqplay.aisengine.eventconverter.service

import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.eventconverter.converter.Converter
import nl.teqplay.aisengine.eventconverter.converter.StartEndConverter
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

/**
 * Service that will load in all [Converter].
 *
 * @property singleEventConverters a list of all [Converter]
 *  that are for a single event, like the TrueDestinationChangedEventConverter.
 * @property startEndEventConverters a list of all [StartEndConverter]
 *  that are for events with an StartEvent and EndEvent instance, like the ShipMovingStartEndEventConverter.
 */
@Service
class ConverterService(
    private val singleEventConverters: List<Converter<*, *>>,
    private val startEndEventConverters: List<StartEndConverter<*, *, *>>
) {
    lateinit var allConverters: List<Converter<*, *>>

    @PostConstruct
    fun loadConverters() {
        val startConverters = startEndEventConverters.map { it.startConverter }
        val endConverters = startEndEventConverters.map { it.endConverter }
        allConverters = startConverters + endConverters + singleEventConverters

        // Initialize the needed configuration for all the converters that are found
        allConverters.forEach { it.initialize() }
    }

    /**
     * Calling statistics for all converters as the [StartEndConverter]s initialize [Converter]s that are not Spring beans.
     */
    @Scheduled(initialDelayString = "\${event.logging-interval}", fixedRateString = "\${event.logging-interval}")
    fun logConverterStatistics() {
        allConverters.forEach { it.logStatistics() }
    }

    /**
     * @return a list of all converters that have AisEngine => Platform enabled
     */
    fun getAisEngineEventConverters(): List<Converter<*, *>> {
        return allConverters.filter { it.aisEngineToPlatformEnabled }
    }

    /**
     * @return a list of all converters that have Platform => AisEngine enabled
     */
    fun getPlatformEventConverters(): List<Converter<*, *>> {
        return allConverters.filter { it.platformToAisEngineEnabled }
    }
}
