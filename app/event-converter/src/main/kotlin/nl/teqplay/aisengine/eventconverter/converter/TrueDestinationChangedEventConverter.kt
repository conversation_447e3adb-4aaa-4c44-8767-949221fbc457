package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.DestinationChangedEvent
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component

@Component
class TrueDestinationChangedEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : Converter<TrueDestinationChangedEvent, DestinationChangedEvent>(
    properties = properties,
    converter = Converters.TRUE_DESTINATION_CHANGED,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun generateRoutingKey(input: DestinationChangedEvent): String? {
        return TeqplayEvent.DESTINATION_CHANGED
    }

    override fun toAisEngineEvent(input: DestinationChangedEvent): TrueDestinationChangedEvent? {
        return aisEngineConverter.convert<TrueDestinationChangedEvent>(input)
    }

    override fun toPlatformEvent(input: TrueDestinationChangedEvent): DestinationChangedEvent? {
        return platformConverter.convert<DestinationChangedEvent>(input)
    }
}
