package nl.teqplay.aisengine.eventconverter.config

import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.service.ConverterService
import nl.teqplay.aisengine.eventconverter.service.TeqplayEventsMessageHandler
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventHandler
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.amqp.rabbit.annotation.EnableRabbit
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.DependsOn
import org.springframework.context.event.ContextClosedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

/**
 * Register beans for connecting to the AMQP queue.
 * This queue is used to receive port events from the platform
 */
@Configuration
@EnableRabbit
class AmqpConfiguration(
    private val properties: EventConverterProperties
) {
    @Bean
    @DependsOn("eventConverterConfiguration")
    fun rabbitMqTeqplayEventsEventHandler() = RabbitMqEventHandler(
        properties.rabbitmq.incoming.qos,
        properties.rabbitmq.incoming.uri
    )

    @Bean
    fun rabbitMqEventSender() = RabbitMqEventSender(properties.rabbitmq.outgoing.uri)
        .also { it.ensureChannelOpened() }
}

/** Ensures that the event consumption would start only after application is initialized. */
@Component
class RabbitMqInitializer(
    private val properties: EventConverterProperties,
    private val teqplayEventsHandler: RabbitMqEventHandler,
    private val incomingTeqplayEventsMessageHandler: TeqplayEventsMessageHandler,
    private val converterService: ConverterService
) {
    @EventListener(ApplicationReadyEvent::class)
    fun initQueues() {
        val platformConverters = converterService.getPlatformEventConverters()
        val shouldConsume = properties.rabbitmq.incoming.consume && platformConverters.isNotEmpty()

        teqplayEventsHandler.createChannelAndListen(
            messageHandler = incomingTeqplayEventsMessageHandler,
            queueName = properties.rabbitmq.incoming.queue,
            consume = shouldConsume
        )
    }

    @EventListener(ContextClosedEvent::class)
    fun closeQueues() {
        teqplayEventsHandler.closeChannel()
    }
}
