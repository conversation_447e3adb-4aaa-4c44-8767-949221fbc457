package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.LockEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component
import nl.teqplay.platform.model.event.LockEvent as PlatformLockEvent

@Component
class LockEtaEtdEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : PlatformConverter<LockEvent, PlatformLockEvent>(
    properties = properties,
    converter = EventConverterProperties.Converters.LOCK_ETA_ETD,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun toAisEngineEvent(input: PlatformLockEvent): LockEvent? {
        return aisEngineConverter.convert<LockEvent>(input)
    }
}
