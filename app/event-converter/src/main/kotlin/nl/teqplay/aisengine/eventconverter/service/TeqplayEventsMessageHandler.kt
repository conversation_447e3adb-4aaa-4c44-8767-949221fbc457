package nl.teqplay.aisengine.eventconverter.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.rabbitmq.BaseMessageHandler
import org.springframework.stereotype.Service

/**
 * Service to deserialize and dispatch messages with events coming in over the event bus
 */
@Service
class TeqplayEventsMessageHandler(
    private val objectMapper: ObjectMapper,
    converterService: ConverterService
) : BaseMessageHandler() {
    private val log = KotlinLogging.logger {}
    private val converters = converterService.getPlatformEventConverters()

    override fun accept(payload: String) {
        val teqplayEvent = try {
            objectMapper.readValue<TeqplayEvent>(payload)
        } catch (e: Throwable) {
            log.error(e) { "Error deserializing a TeqplayEvent message" }

            return
        }

        converters.forEach { converter ->
            converter.processPlatformEvent(teqplayEvent)
        }
    }
}
