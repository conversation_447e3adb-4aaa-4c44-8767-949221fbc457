package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.AisDraughtChangedEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.DraughtChangedEvent
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component

@Component
class AisDraughtChangedEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : Converter<AisDraughtChangedEvent, DraughtChangedEvent>(
    properties = properties,
    converter = Converters.AIS_DRAUGHT_CHANGED,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun generateRoutingKey(input: DraughtChangedEvent): String? {
        return TeqplayEvent.DRAUGHT_CHANGED
    }

    override fun toAisEngineEvent(input: DraughtChangedEvent): AisDraughtChangedEvent? =
        aisEngineConverter.convert<AisDraughtChangedEvent>(input)

    override fun toPlatformEvent(input: AisDraughtChangedEvent): DraughtChangedEvent? =
        platformConverter.convert<DraughtChangedEvent>(input)
}
