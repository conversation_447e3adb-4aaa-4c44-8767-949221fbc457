package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Component
import nl.teqplay.platform.model.event.PortcallPilotBoardingEtaEvent as PlatformPortcallPilotBoardingEtaEvent

@Component
class PortcallPilotBoardingEtaEventConverter(
    properties: EventConverterProperties,
    objectMapper: ObjectMapper,
    rabbitMqEventSender: RabbitMqEventSender,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : PlatformConverter<PortcallPilotBoardingEtaEvent, PlatformPortcallPilotBoardingEtaEvent>(
    properties = properties,
    converter = EventConverterProperties.Converters.PORTCALL_PILOT_BOARDING_ETA,
    objectMapper = objectMapper,
    rabbitMqEventSender = rabbitMqEventSender,
    eventStreamService = eventStreamService,
    meterRegistry = meterRegistry
) {
    override fun toAisEngineEvent(input: PlatformPortcallPilotBoardingEtaEvent): PortcallPilotBoardingEtaEvent? {
        return aisEngineConverter.convert<PortcallPilotBoardingEtaEvent>(input)
    }
}
