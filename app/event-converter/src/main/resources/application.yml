event:
  rabbitmq:
    incoming:
      consume: false
      uri: amqps://localhost:5671/TeqplayEventsDev
      queue: TeqplayEventsDevAisStream
      qos: 10
    outgoing:
      uri: amqps://localhost:5671/TeqplayEventsDev
  logging-interval: PT5M
  area:
    berth-events-enabled: false
  converters:
    AIS_DRAUGHT_CHANGED: DISABLED
    AIS_ETA_CHANGED: DISABLED
    AIS_STATUS_CHANGED: DISABLED
    ANCHORED: DISABLED
    AREA_START: DISABLED
    AREA_END: DISABLED
    TRUE_DESTINATION_CHANGED: DISABLED
    SHIP_MOVING_START: DISABLED
    SHIP_MOVING_END: DISABLED

nats:
  event-stream:
    enabled: 'false'
    password:
    url:
    username:

poma:
  url:
  realm:
  client-id:
  client-secret:
  domain:

# Prometheus JVM stats exposing
management:
  metrics:
    enable:
      jvm: true
    tags:
      component: event-converter
  endpoints:
    web:
      exposure:
        include:
          - health
          - prometheus
