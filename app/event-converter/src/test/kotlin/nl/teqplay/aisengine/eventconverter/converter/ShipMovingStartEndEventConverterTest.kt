package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.eventconverter.EventConverterBaseTest
import nl.teqplay.aisengine.eventconverter.createTestRabbitMqProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.EnabledConverter
import nl.teqplay.aisengine.nats.stream.EventStreamAutoConfiguration.Companion.EVENT_STREAM
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.ShipMovingEvent
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.queue
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import java.time.Duration
import java.time.Instant

@ContextConfiguration
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class ShipMovingStartEndEventConverterTest(
    private val converter: ShipMovingStartEndEventConverter,
    private val objectMapper: ObjectMapper,
    private val natsClientMock: NatsClientMock
) : EventConverterBaseTest() {

    companion object {
        val properties = EventConverterProperties(
            converters = mapOf(
                Converters.SHIP_MOVING_START to EnabledConverter.BOTH,
                Converters.SHIP_MOVING_END to EnabledConverter.AISENGINE
            ),
            loggingInterval = Duration.ofHours(1),
            rabbitmq = createTestRabbitMqProperties(),
            area = EventConverterProperties.AreaEventConfig(false)
        )

        val shipMovingStartEvent = ShipMovingStartEvent(
            _id = "e560e73a-689b-4ddc-84f0-f95057bf99ca",
            ship = AisShipIdentifier(mmsi = 413827363),
            location = Location(lat = 31.372700000000002, lon = 121.59617333333334),
            actualTime = Instant.ofEpochMilli(1664980096000),
            createdTime = Instant.ofEpochMilli(1664980096000),
            deleted = false,
            regenerated = null
        )

        val shipMovingEndEvent = ShipMovingEndEvent(
            _id = "911ca899-9366-49f7-a384-f4cbf375231f",
            startEventId = "03816aff-38ae-4b97-aa80-6bffeff9aaaf",
            ship = AisShipIdentifier(mmsi = 271073599, imo = 9957256),
            location = Location(lat = 20.75595, lon = -17.262263333333333),
            actualTime = Instant.ofEpochMilli(1664980181319),
            createdTime = Instant.ofEpochMilli(1664980181319),
            deleted = false,
            regenerated = null
        )
    }

    @TestConfiguration
    class Config {
        @Bean
        fun converter(
            meterRegistry: MeterRegistry,
            objectMapper: ObjectMapper,
            testEventStreamService: EventStreamService
        ): ShipMovingStartEndEventConverter {
            return ShipMovingStartEndEventConverter(
                properties,
                objectMapper,
                mock<RabbitMqEventSender>(),
                testEventStreamService,
                meterRegistry
            )
        }
    }

    @Test
    fun `should initialize correctly`() {
        converter.startConverter.initialize()
        converter.endConverter.initialize()

        Assertions.assertTrue(converter.startConverter.platformToAisEngineEnabled)
        Assertions.assertTrue(converter.startConverter.aisEngineToPlatformEnabled)
        Assertions.assertFalse(converter.endConverter.platformToAisEngineEnabled)
        Assertions.assertTrue(converter.endConverter.aisEngineToPlatformEnabled)
    }

    @Test
    fun `should put platform start moving event on nats stream and read correctly`() {
        converter.startConverter.initialize()
        converter.endConverter.initialize()
        val platformStartEvent = objectMapper.loadEventFile<ShipMovingEvent>("./platform/ShipMovingStartEvent.json")
        converter.startConverter.processPlatformEvent(platformStartEvent)
        converter.endConverter.processPlatformEvent(platformStartEvent)

        val startEventQueue = natsClientMock.queue<ShipMovingStartEvent>(EVENT_STREAM)
        assertEquals(1, startEventQueue.size, "Converted event wasn't set on the nats stream")

        val (_, aisEngineEvent) = startEventQueue.last()
        assertEquals(shipMovingStartEvent, aisEngineEvent)
    }

    @Test
    fun `should not put platform stop moving event on nats stream`() {
        converter.endConverter.initialize()
        converter.endConverter.initialize()
        val platformEndEvent = objectMapper.loadEventFile<ShipMovingEvent>("./platform/ShipMovingEndEvent.json")
        converter.startConverter.processPlatformEvent(platformEndEvent)
        converter.endConverter.processPlatformEvent(platformEndEvent)

        val eventQueue = natsClientMock.queue<ShipMovingEndEvent>(EVENT_STREAM)
        assertEquals(0, eventQueue.size, "Converted event was set on the nats stream")
    }

    @Test
    fun `should convert platform start event model correctly`() {
        val platformEvent = objectMapper.loadEventFile<ShipMovingEvent>("./platform/ShipMovingStartEvent.json")
        val result = converter.startConverter.toAisEngineEvent(platformEvent)

        assertEquals(shipMovingStartEvent, result)
    }

    @Test
    fun `should convert ais-engine start event model correctly`() {
        val result = converter.startConverter.toPlatformEvent(shipMovingStartEvent)
        val expectedPlatformEvent = objectMapper.loadEventFile<ShipMovingEvent>("./platform/ShipMovingStartEvent.json")

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expectedPlatformEvent)
    }

    @Test
    fun `should convert platform end event model correctly`() {
        val platformEvent = objectMapper.loadEventFile<ShipMovingEvent>("./platform/ShipMovingEndEvent.json")
        val result = converter.endConverter.toAisEngineEvent(platformEvent)

        assertEquals(shipMovingEndEvent, result)
    }

    @Test
    fun `should convert ais-engine end event model correctly`() {
        val result = converter.endConverter.toPlatformEvent(shipMovingEndEvent)
        val expectedPlatformEvent = objectMapper.loadEventFile<ShipMovingEvent>("./platform/ShipMovingEndEvent.json")

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expectedPlatformEvent)
    }
}
