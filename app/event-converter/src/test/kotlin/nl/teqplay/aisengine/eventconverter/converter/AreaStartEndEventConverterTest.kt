package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.eventconverter.EventConverterBaseTest
import nl.teqplay.aisengine.eventconverter.createTestRabbitMqProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.EnabledConverter
import nl.teqplay.aisengine.nats.stream.EventStreamAutoConfiguration.Companion.EVENT_STREAM
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.testing.event.createAreaEndEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.aisengine.testing.event.defaultPortAreaIdentifier
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.queue
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import java.time.Duration
import java.time.Instant

@ContextConfiguration
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class AreaStartEndEventConverterTest(
    private val converter: AreaStartEndEventConverter,
    private val objectMapper: ObjectMapper,
    private val natsClientMock: NatsClientMock
) : EventConverterBaseTest() {

    companion object {
        val properties = EventConverterProperties(
            converters = mapOf(
                Converters.AREA_START to EnabledConverter.AISENGINE,
                Converters.AREA_END to EnabledConverter.AISENGINE
            ),
            loggingInterval = Duration.ofHours(1),
            rabbitmq = createTestRabbitMqProperties(),
            area = EventConverterProperties.AreaEventConfig(false)
        )

        val area = AreaIdentifier("rtmpilotarea_lng", AreaIdentifier.AreaType.ANCHOR, "rtmpilotarea_lng", "NLRTM")
        val eightyNm = AreaIdentifier("E57297BAEF6EE30BCDE04E8DFAED7E389A8AF210.80nm", AreaIdentifier.AreaType.NAUTICAL_MILE, "NLRTM 80 nm Area", "NLRTM")

        val anchorStartEvent = AreaStartEvent(
            _id = "99c65550-3f60-44fc-b9a9-576f950fdb19",
            ship = AisShipIdentifier(mmsi = 563170400, imo = 9933030),
            location = Location(lat = 51.93324833333334, lon = 3.5977116666666666),
            actualTime = Instant.ofEpochMilli(1684764157000),
            createdTime = Instant.ofEpochMilli(1684764157000),
            area = area,
            berth = null,
            heading = null,
            draught = null,
            speedOverGround = null,
            deleted = false,
            regenerated = null
        )

        val eightNmStartEvent = AreaStartEvent(
            _id = "ijkf81nd-b9d9-4572-98f7-b0c961b7858a",
            ship = AisShipIdentifier(mmsi = 215909000, imo = 9487378),
            location = Location(lat = 51.935583333333334, lon = 3.810083333333333),
            actualTime = Instant.ofEpochMilli(1675397569207),
            createdTime = Instant.ofEpochMilli(1675397569207),
            area = eightyNm,
            berth = null,
            heading = null,
            draught = null,
            speedOverGround = null,
            deleted = false,
            regenerated = null
        )

        val anchorEndEvent = AreaEndEvent(
            _id = "fdbbbaca-07f8-4b8e-83f1-a6dc5ff3c7de",
            startEventId = "99c65550-3f60-44fc-b9a9-576f950fdb19",
            ship = AisShipIdentifier(mmsi = 563170400, imo = 9933030),
            location = Location(lat = 51.93324833333334, lon = 3.5977116666666666),
            actualTime = Instant.ofEpochMilli(1684764157000),
            createdTime = Instant.ofEpochMilli(1684764157000),
            area = area,
            berth = null,
            heading = null,
            draught = null,
            speedOverGround = null,
            deleted = false,
            regenerated = null
        )

        val eightyNmEndEvent = AreaEndEvent(
            _id = "bm2189fmn-07f8-4b8e-83f1-a6dc5ff3c7de",
            startEventId = "ijkf81nd-b9d9-4572-98f7-b0c961b7858a",
            ship = AisShipIdentifier(mmsi = 215909000, imo = 9487378),
            location = Location(lat = 51.941916666666664, lon = 3.8066833333333334),
            actualTime = Instant.ofEpochMilli(1676644920000),
            createdTime = Instant.ofEpochMilli(1676644920000),
            area = eightyNm,
            berth = null,
            heading = null,
            draught = null,
            speedOverGround = null,
            deleted = false,
            regenerated = null
        )

        val portStartEvent = createAreaStartEvent(
            _id = "99c65550-3f60-44fc-b9a9-576f950fdb19",
            ship = AisShipIdentifier(563170400, 9933030),
            area = defaultPortAreaIdentifier.copy(name = "Rotterdam", unlocode = "NLRTM"),
            location = Location(lat = 51.93324833333334, lon = 3.5977116666666666)
        )
        val portEndEvent = createAreaEndEvent(
            _id = "fdbbbaca-07f8-4b8e-83f1-a6dc5ff3c7de",
            startEventId = "99c65550-3f60-44fc-b9a9-576f950fdb19",
            ship = AisShipIdentifier(563170400, 9933030),
            area = defaultPortAreaIdentifier.copy(name = "Rotterdam", unlocode = "NLRTM"),
            location = Location(lat = 51.93324833333334, lon = 3.5977116666666666)
        )
    }

    @TestConfiguration
    class Config {
        @Bean
        fun converter(
            meterRegistry: MeterRegistry,
            objectMapper: ObjectMapper,
            testEventStreamService: EventStreamService
        ): AreaStartEndEventConverter {
            return AreaStartEndEventConverter(
                properties,
                objectMapper,
                mock<RabbitMqEventSender>(),
                testEventStreamService,
                meterRegistry
            )
        }
    }

    @Test
    fun `should initialize correctly`() {
        converter.startConverter.initialize()
        converter.endConverter.initialize()

        Assertions.assertFalse(converter.startConverter.platformToAisEngineEnabled)
        Assertions.assertTrue(converter.startConverter.aisEngineToPlatformEnabled)
        Assertions.assertFalse(converter.endConverter.platformToAisEngineEnabled)
        Assertions.assertTrue(converter.endConverter.aisEngineToPlatformEnabled)
    }

    @Test
    fun `should not put platform stop moving event on nats stream`() {
        converter.endConverter.initialize()
        converter.endConverter.initialize()
        val platformEndEvent = objectMapper.loadEventFile<TeqplayLocationBasedEvent>("./platform/AnchorEndEvent.json")
        converter.startConverter.processPlatformEvent(platformEndEvent)
        converter.endConverter.processPlatformEvent(platformEndEvent)

        val eventQueue = natsClientMock.queue<AreaEndEvent>(EVENT_STREAM)
        assertEquals(0, eventQueue.size, "Converted event was set on the nats stream")
    }

    @Test
    fun `should convert ais-engine start event model correctly`() {
        val result = converter.startConverter.toPlatformEvent(anchorStartEvent)
        val expectedPlatformEvent = objectMapper.loadEventFile<TeqplayLocationBasedEvent>("./platform/AnchorStartEvent.json")

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expectedPlatformEvent)
    }

    @Test
    fun `should convert ais-engine end event model correctly`() {
        val result = converter.endConverter.toPlatformEvent(anchorEndEvent)
        val expectedPlatformEvent = objectMapper.loadEventFile<TeqplayLocationBasedEvent>("./platform/AnchorEndEvent.json")

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expectedPlatformEvent)
    }

    @Test
    fun `should convert ais-engine port start event model correctly`() {
        val result = converter.startConverter.toPlatformEvent(portStartEvent)
        val expectedPlatformEvent = objectMapper.loadEventFile<TeqplayLocationBasedEvent>("./platform/PortStartEvent.json")

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expectedPlatformEvent)
    }

    @Test
    fun `should convert ais-engine port end event model correctly`() {
        val result = converter.endConverter.toPlatformEvent(portEndEvent)
        val expectedPlatformEvent = objectMapper.loadEventFile<TeqplayLocationBasedEvent>("./platform/PortEndEvent.json")

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expectedPlatformEvent)
    }

    @Test
    fun `should convert to right custom event start name`() {
        val result = converter.toPlatformStartEvent(eightNmStartEvent)
        assertThat(result)
            .isNotNull()
        assertThat(result?.type)
            .isEqualTo("area.rtm80nm.start")
    }

    @Test
    fun `should convert to right custom event end name`() {
        val result = converter.toPlatformEndEvent(eightyNmEndEvent)
        assertThat(result)
            .isNotNull()
        assertThat(result?.type)
            .isEqualTo("area.rtm80nm.end")
    }
}
