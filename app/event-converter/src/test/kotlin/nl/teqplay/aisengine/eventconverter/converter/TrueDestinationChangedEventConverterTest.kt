package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.eventconverter.EventConverterBaseTest
import nl.teqplay.aisengine.eventconverter.createTestRabbitMqProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.EnabledConverter
import nl.teqplay.aisengine.nats.stream.EventStreamAutoConfiguration.Companion.EVENT_STREAM
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.DestinationChangedEvent
import nl.teqplay.platform.model.event.DraughtChangedEvent
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.queue
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import java.time.Duration
import java.time.Instant

@ContextConfiguration
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class TrueDestinationChangedEventConverterTest(
    private val objectMapper: ObjectMapper,
    private val natsClientMock: NatsClientMock,
    eventStreamService: EventStreamService,
    meterRegistry: MeterRegistry
) : EventConverterBaseTest() {
    private val rabbitMqEventSender: RabbitMqEventSender = mock()
    private val converter = TrueDestinationChangedEventConverter(
        properties,
        objectMapper,
        rabbitMqEventSender,
        eventStreamService,
        meterRegistry
    )

    companion object {
        val properties = EventConverterProperties(
            converters = mapOf(
                Converters.TRUE_DESTINATION_CHANGED to EnabledConverter.BOTH
            ),
            loggingInterval = Duration.ofHours(1),
            rabbitmq = createTestRabbitMqProperties(),
            boundingBox = BoundingBox(
                topright = Location(lat = 51.0, lon = -1.0),
                bottomleft = Location(lat = 50.0, lon = -2.0)
            ),
            area = EventConverterProperties.AreaEventConfig(false)
        )

        val expectedAisEngineEvent = TrueDestinationChangedEvent(
            _id = "25be6142-7b80-406e-b1b7-2c0ca7beb6ab",
            ship = AisShipIdentifier(
                mmsi = 636017346,
                imo = 9036002
            ),
            oldValue = "SOUTHAMPTON",
            newValue = "WANDELAAR  ANTWERP",
            trueDestination = "BEANR",
            location = Location(
                lat = 50.90938666666667,
                lon = -1.4638300000000002
            ),
            actualTime = Instant.ofEpochMilli(1657937203008),
            createdTime = Instant.ofEpochMilli(1657937203008),
            deleted = false,
            regenerated = null
        )

        val expectedPlatformEvent = DestinationChangedEvent(
            uuid = "25be6142-7b80-406e-b1b7-2c0ca7beb6ab",
            shipMmsi = "636017346",
            imo = "9036002",
            oldDestination = "SOUTHAMPTON",
            newDestination = "WANDELAAR  ANTWERP",
            newTrueDestination = "BEANR",
            location = nl.teqplay.platform.model.Location(
                50.90938666666667,
                -1.4638300000000002
            ),
            timestamp = 1657937203008,
            description = "Destination changed (SOUTHAMPTON to WANDELAAR  ANTWERP)",
            title = "Destination changed (SOUTHAMPTON to WANDELAAR  ANTWERP)",
            summary = "Destination changed (SOUTHAMPTON to WANDELAAR  ANTWERP)"
        )
    }

    @Test
    fun `should initialize correctly`() {
        converter.initialize()

        assertTrue(converter.platformToAisEngineEnabled)
        assertTrue(converter.aisEngineToPlatformEnabled)
    }

    @Test
    fun `should put platform event on nats stream and read correctly`() {
        converter.initialize()
        val platformEvent = objectMapper.loadEventFile<DestinationChangedEvent>("./platform/DestinationChangedEvent.json")
        converter.processPlatformEvent(platformEvent)

        val eventQueue = natsClientMock.queue<TrueDestinationChangedEvent>(EVENT_STREAM)
        assertEquals(1, eventQueue.size, "Converted event wasn't set on the nats stream")

        val (_, aisEngineEvent) = eventQueue.last()
        assertEquals(expectedAisEngineEvent, aisEngineEvent)
    }

    @Test
    fun `should ignore different type of platform event`() {
        converter.initialize()
        val draughtChangedEvent = objectMapper.loadEventFile<DraughtChangedEvent>("./platform/DraughtChangedEvent.json")
        converter.processPlatformEvent(draughtChangedEvent)

        val eventQueue = natsClientMock.queue<TrueDestinationChangedEvent>(EVENT_STREAM)
        assertEquals(0, eventQueue.size, "Different type of platform event shouldn't be put on the nats stream")
    }

    @Test
    fun `should convert platform event model correctly`() {
        val platformEvent = objectMapper.loadEventFile<DestinationChangedEvent>("./platform/DestinationChangedEvent.json")
        val result = converter.toAisEngineEvent(platformEvent)

        assertEquals(expectedAisEngineEvent, result)
    }

    @Test
    fun `should convert ais-engine event model correctly`() {
        val aisEngineEvent = objectMapper.loadEventFile<TrueDestinationChangedEvent>("./aisengine/TrueDestinationChangedEvent.json")
        val result = converter.toPlatformEvent(aisEngineEvent)

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expectedPlatformEvent)
    }

    @Test
    fun `should forward ais-engine event inside convert area`() {
        converter.initialize()
        val eventInsideArea = expectedAisEngineEvent

        converter.processAisEngineEvent(eventInsideArea)

        verify(rabbitMqEventSender).send(eq("TestAisEngineTeqplayEventsDev"), any(), eq("ship.destinationchanged"))
    }

    @Test
    fun `should not forward ais-engine event outside convert area`() {
        converter.initialize()
        val eventOutsideArea = expectedAisEngineEvent.copy(location = Location(1.0, 1.0))

        converter.processAisEngineEvent(eventOutsideArea)

        verify(rabbitMqEventSender, never()).send(any(), any(), any())
    }
}
