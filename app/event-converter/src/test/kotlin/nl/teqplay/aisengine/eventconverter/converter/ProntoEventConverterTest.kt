package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.eventconverter.EventConverterBaseTest
import nl.teqplay.aisengine.eventconverter.createTestRabbitMqProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.EnabledConverter
import nl.teqplay.aisengine.nats.stream.EventStreamAutoConfiguration.Companion.EVENT_STREAM
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.testing.event.createHamisAddPortcallVisitEvent
import nl.teqplay.aisengine.testing.event.createHamisAgentOrderEvent
import nl.teqplay.aisengine.testing.event.createHamisAgentReportsTugsEvent
import nl.teqplay.aisengine.testing.event.createHamisCancelPortcallVisitEvent
import nl.teqplay.aisengine.testing.event.createHamisEtaCancelEvent
import nl.teqplay.aisengine.testing.event.createHamisEtaRequestEvent
import nl.teqplay.aisengine.testing.event.createHamisEtdRequestEvent
import nl.teqplay.aisengine.testing.event.createHamisPilotOnBoardEndEvent
import nl.teqplay.aisengine.testing.event.createHamisPilotOnBoardStartEvent
import nl.teqplay.aisengine.testing.event.createHamisUpdatePortcallVisitEvent
import nl.teqplay.aisengine.testing.event.createHamisVisitCancellationEvent
import nl.teqplay.aisengine.testing.event.createHamisVisitDeclarationEvent
import nl.teqplay.aisengine.testing.event.defaultShipName
import nl.teqplay.aisengine.testing.event.defaultStartEventIdentifier
import nl.teqplay.aisengine.testing.platform.createPlatformAddPortcallVisitEvent
import nl.teqplay.aisengine.testing.platform.createPlatformAgentOrderEvent
import nl.teqplay.aisengine.testing.platform.createPlatformAgentReportsTugsEvent
import nl.teqplay.aisengine.testing.platform.createPlatformCancelPortcallVisitEvent
import nl.teqplay.aisengine.testing.platform.createPlatformEtaCancelEvent
import nl.teqplay.aisengine.testing.platform.createPlatformEtaRequestEvent
import nl.teqplay.aisengine.testing.platform.createPlatformEtdRequestEvent
import nl.teqplay.aisengine.testing.platform.createPlatformPilotOnBoardEvent
import nl.teqplay.aisengine.testing.platform.createPlatformUpdatePortcallVisitEvent
import nl.teqplay.aisengine.testing.platform.createPlatformVisitCancellationEvent
import nl.teqplay.aisengine.testing.platform.createPlatformVisitDeclarationEvent
import nl.teqplay.aisengine.testing.platform.defaultPlatformBerthAreaIdentifier
import nl.teqplay.aisengine.testing.platform.defaultPlatformOtherPortAreaIdentifier
import nl.teqplay.aisengine.testing.platform.defaultPlatformPortAreaIdentifier
import nl.teqplay.aisengine.testing.platform.defaultShipInfo
import nl.teqplay.platform.model.event.DraughtChangedEvent
import nl.teqplay.platform.model.event.hbr.ProntoEvent
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.skeleton.nats.queue
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.mock
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import java.time.Duration
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ContextConfiguration
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class ProntoEventConverterTest(
    private val converter: ProntoEventConverter,
    private val objectMapper: ObjectMapper,
    private val natsClientMock: NatsClientMock
) : EventConverterBaseTest() {
    companion object {
        val properties = EventConverterProperties(
            converters = mapOf(
                Converters.HAMIS to EnabledConverter.BOTH
            ),
            loggingInterval = Duration.ofHours(1),
            rabbitmq = createTestRabbitMqProperties(),
            boundingBox = BoundingBox(
                topright = Location(lat = 51.0, lon = -1.0),
                bottomleft = Location(lat = 50.0, lon = -2.0)
            ),
            area = EventConverterProperties.AreaEventConfig(false)
        )
    }

    @TestConfiguration
    class Config {
        @Bean
        fun converter(
            meterRegistry: MeterRegistry,
            objectMapper: ObjectMapper,
            eventStreamService: EventStreamService,
            natsEventProducer: NatsProducerStream<Event>
        ) = ProntoEventConverter(
            properties,
            objectMapper,
            mock<RabbitMqEventSender>(),
            eventStreamService,
            meterRegistry
        )
    }

    @Test
    fun `should initialize correctly`() {
        converter.initialize()

        assertTrue(converter.platformToAisEngineEnabled)
        assertTrue(converter.aisEngineToPlatformEnabled)
    }

    private fun prontoEvents(): Stream<Arguments> = Stream.of(
        Arguments.of(
            createPlatformAddPortcallVisitEvent(),
            createHamisAddPortcallVisitEvent(port = defaultPlatformPortAreaIdentifier)
        ),
        Arguments.of(
            createPlatformUpdatePortcallVisitEvent(),
            createHamisUpdatePortcallVisitEvent(port = defaultPlatformPortAreaIdentifier)
        ),
        Arguments.of(
            createPlatformCancelPortcallVisitEvent(),
            createHamisCancelPortcallVisitEvent(port = defaultPlatformPortAreaIdentifier)
        ),
        Arguments.of(
            createPlatformAgentOrderEvent(),
            createHamisAgentOrderEvent(port = defaultPlatformPortAreaIdentifier)
        ),
        Arguments.of(
            createPlatformAgentReportsTugsEvent(),
            createHamisAgentReportsTugsEvent(
                port = defaultPlatformPortAreaIdentifier,
                berth = defaultPlatformBerthAreaIdentifier
            )
        ),
        Arguments.of(
            createPlatformEtaCancelEvent(),
            createHamisEtaCancelEvent(port = defaultPlatformPortAreaIdentifier)
        ),
        Arguments.of(
            createPlatformEtaRequestEvent(),
            createHamisEtaRequestEvent(
                port = defaultPlatformPortAreaIdentifier,
                berth = defaultPlatformBerthAreaIdentifier
            )
        ),
        Arguments.of(
            createPlatformEtdRequestEvent(),
            createHamisEtdRequestEvent(
                port = defaultPlatformPortAreaIdentifier,
                berth = defaultPlatformBerthAreaIdentifier
            )
        ),
        Arguments.of(
            createPlatformPilotOnBoardEvent(),
            createHamisPilotOnBoardStartEvent(port = defaultPlatformPortAreaIdentifier)
        ),
        Arguments.of(
            createPlatformPilotOnBoardEvent(relatedEventId = defaultStartEventIdentifier),
            createHamisPilotOnBoardEndEvent(port = defaultPlatformPortAreaIdentifier)
        ),
        Arguments.of(
            createPlatformVisitCancellationEvent(shipInfo = defaultShipInfo.also { it.name = defaultShipName }),
            createHamisVisitCancellationEvent(port = defaultPlatformPortAreaIdentifier)
        ),
        Arguments.of(
            createPlatformVisitDeclarationEvent(shipInfo = defaultShipInfo.also { it.name = defaultShipName }),
            createHamisVisitDeclarationEvent(
                port = defaultPlatformPortAreaIdentifier,
                previousPort = defaultPlatformOtherPortAreaIdentifier
            )
        )
    )

    @ParameterizedTest
    @MethodSource("prontoEvents")
    fun `should put platform event on nats stream and read correctly`(
        platformEvent: ProntoEvent,
        expected: Event
    ) {
        converter.initialize()
        converter.processPlatformEvent(platformEvent)

        val eventQueue = natsClientMock.queue<Event>(EVENT_STREAM)
        assertEquals(1, eventQueue.size, "Converted event wasn't set on the nats stream")

        val (_, aisEngineEvent) = eventQueue.removeLast()
        assertThat(aisEngineEvent)
            .usingRecursiveComparison().ignoringFields("deleted")
            .isEqualTo(expected)
    }

    @Test
    fun `should ignore different type of platform event`() {
        converter.initialize()
        val draughtChangedEvent = objectMapper.loadEventFile<DraughtChangedEvent>("./platform/DraughtChangedEvent.json")
        converter.processPlatformEvent(draughtChangedEvent)

        val eventQueue = natsClientMock.queue<Event>(EVENT_STREAM)
        assertEquals(0, eventQueue.size, "Different type of platform event shouldn't be put on the nats stream")
    }
}
