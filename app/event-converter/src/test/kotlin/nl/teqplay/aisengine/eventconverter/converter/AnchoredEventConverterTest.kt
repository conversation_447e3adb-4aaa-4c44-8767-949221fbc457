package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.eventconverter.EventConverterBaseTest
import nl.teqplay.aisengine.eventconverter.createTestRabbitMqProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.EnabledConverter
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.platform.model.event.TeqplayEvent.EventType
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import java.time.Duration
import java.time.Instant

@ContextConfiguration
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class AnchoredEventConverterTest(
    private val converter: AnchoredEventConverter,
    private val objectMapper: ObjectMapper,
    private val natsClientMock: NatsClientMock
) : EventConverterBaseTest() {

    companion object {
        val properties = EventConverterProperties(
            converters = mapOf(
                Converters.ANCHORED to EnabledConverter.AISENGINE,
            ),
            loggingInterval = Duration.ofHours(1),
            rabbitmq = createTestRabbitMqProperties(),
            area = EventConverterProperties.AreaEventConfig(false)
        )

        val area = AreaIdentifier("id", AreaIdentifier.AreaType.ANCHOR, "anchor-area", "NLRTM")
        val ship = AisShipIdentifier(mmsi = 413827363, imo = 9827815)

        val startEvent = AnchoredStartEvent(
            _id = "e560e73a-689b-4ddc-84f0-f95057bf99ca",
            ship = ship,
            location = Location(lat = 31.372700000000002, lon = 121.59617333333334),
            actualTime = Instant.ofEpochMilli(1664980096000),
            createdTime = Instant.ofEpochMilli(1664980096000),
            deleted = false,
            area = area
        )

        val endEvent = AnchoredEndEvent(
            _id = "911ca899-9366-49f7-a384-f4cbf375231f",
            startEventId = "e560e73a-689b-4ddc-84f0-f95057bf99ca",
            ship = ship,
            location = Location(lat = 31.372700000000002, lon = 121.59617333333334),
            actualTime = Instant.ofEpochMilli(1664980181319),
            createdTime = Instant.ofEpochMilli(1664980181319),
            deleted = false,
            area = area
        )
    }

    @TestConfiguration
    class Config {
        @Bean
        fun converter(
            meterRegistry: MeterRegistry,
            objectMapper: ObjectMapper,
            testEventStreamService: EventStreamService
        ): AnchoredEventConverter {
            return AnchoredEventConverter(
                properties,
                objectMapper,
                mock<RabbitMqEventSender>(),
                testEventStreamService,
                meterRegistry
            )
        }
    }

    @Test
    fun `should convert platform start event model correctly`() {
        converter.initialize()
        val result = converter.toPlatformEvent(startEvent)

        assertEquals(result?.uuid, startEvent._id)
        assertEquals(result?.isStartEvent, true)
        assertEquals(result?.eventTime, startEvent.actualTime.toEpochMilli())
        assertEquals(result?.generatedTime, startEvent.createdTime.toEpochMilli())
        assertEquals(result?.shipMmsi?.toInt(), startEvent.ship.mmsi)
        assertEquals(result?.imo?.toInt(), startEvent.ship.imo)
        assertEquals(result?.title, "${startEvent.ship.mmsi} started to be anchored at ${startEvent.area.name}")
        assertEquals(result?.area, startEvent.area.name)
        assertEquals(result?.type, "ship.anchored.start")
        assertEquals(result?.eventType, EventType.ANCHORED)
        assertEquals(result?.location?.latitude, startEvent.location.lat)
        assertEquals(result?.location?.longitude, startEvent.location.lon)
    }

    @Test
    fun `should convert platform end event model correctly`() {
        converter.initialize()
        val result = converter.toPlatformEvent(endEvent)

        assertEquals(result?.uuid, endEvent._id)
        assertEquals(result?.isStartEvent, false)
        assertEquals(result?.relatedEvent, startEvent._id)
        assertEquals(result?.eventTime, endEvent.actualTime.toEpochMilli())
        assertEquals(result?.generatedTime, endEvent.createdTime.toEpochMilli())
        assertEquals(result?.shipMmsi?.toInt(), endEvent.ship.mmsi)
        assertEquals(result?.imo?.toInt(), endEvent.ship.imo)
        assertEquals(result?.title, "${endEvent.ship.mmsi} stopped to be anchored at ${endEvent.area.name}")
        assertEquals(result?.area, endEvent.area.name)
        assertEquals(result?.type, "ship.anchored.end")
        assertEquals(result?.eventType, EventType.ANCHORED)
        assertEquals(result?.location?.latitude, endEvent.location.lat)
        assertEquals(result?.location?.longitude, endEvent.location.lon)
    }
}
