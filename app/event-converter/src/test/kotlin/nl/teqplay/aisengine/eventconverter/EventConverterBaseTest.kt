package nl.teqplay.aisengine.eventconverter

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.nats.stream.EventStreamAutoConfiguration
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.EventStreamServiceImpl
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.NatsProducerStream
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor

@ContextConfiguration
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
open class EventConverterBaseTest : BaseTest() {
    inline fun <reified T> ObjectMapper.loadEventFile(resourceLocation: String): T {
        val eventJson = this.javaClass.classLoader.getResource(resourceLocation)
        return this.readValue(eventJson)
    }

    @TestConfiguration
    class ConverterBaseConfig {
        @Bean
        fun testMeterRegistry(): MeterRegistry = SimpleMeterRegistry()

        @Bean
        fun testNatsEventProducer(
            natsClientMock: NatsClientMock,
            objectMapper: ObjectMapper
        ): NatsProducerStream<Event> {
            return natsClientMock.producerStream(
                stream = EventStreamAutoConfiguration.EVENT_STREAM,
                subjects = listOf(EventStreamAutoConfiguration.EVENT_STREAM_SUBJECT),
                serializer = { objectMapper.writeValueAsBytes(it) }
            )
        }

        @Bean
        fun testEventStreamService(
            producer: NatsProducerStream<Event>
        ): EventStreamService {
            return EventStreamServiceImpl(producer)
        }

        @Bean
        fun natsClientMock() = NatsClientMock()

        @Bean
        fun objectMapper(): ObjectMapper = jacksonObjectMapper()
            .registerModule(JavaTimeModule())
    }
}

fun createTestRabbitMqProperties(): EventConverterProperties.RabbitMq {
    return EventConverterProperties.RabbitMq(
        incoming = EventConverterProperties.RabbitMq.Incoming(
            consume = true,
            uri = "amqp://localhost:5672/TestTeqplayEventsDev",
            queue = "TestQueue",
            qos = 10
        ),
        outgoing = EventConverterProperties.RabbitMq.Outgoing(
            uri = "amqp://localhost:5672/TestTeqplayEventsDev",
            exchange = "TestAisEngineTeqplayEventsDev"
        )
    )
}
