package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.model.encountermetadata.BoatmanEncounterMetadata
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.eventconverter.EventConverterBaseTest
import nl.teqplay.aisengine.eventconverter.createTestRabbitMqProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.EnabledConverter
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.testing.event.createEncounterEndEvent
import nl.teqplay.aisengine.testing.event.createEncounterStartEvent
import nl.teqplay.platform.model.event.BoatmanEvent
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import java.time.Duration
import java.time.Instant

@ContextConfiguration
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class EncounterStartEndEventConverterTest(
    private val converter: EncounterStartEndEventConverter,
    private val objectMapper: ObjectMapper,
    private val natsClientMock: NatsClientMock
) : EventConverterBaseTest() {

    companion object {
        val properties = EventConverterProperties(
            converters = mapOf(
                Converters.ENCOUNTER_START to EnabledConverter.AISENGINE,
                Converters.ENCOUNTER_END to EnabledConverter.AISENGINE
            ),
            loggingInterval = Duration.ofHours(1),
            rabbitmq = createTestRabbitMqProperties(),
            area = EventConverterProperties.AreaEventConfig(false)
        )

        val encounterStartEvent = createEncounterStartEvent(
            _id = "60e78f58-5224-420e-aa39-916d1a25be71",
            ship = AisShipIdentifier(mmsi = 354654000, imo = 9811012),
            otherShip = AisShipIdentifier(mmsi = 244790247),
            location = Location(lat = 51.97351333333334, lon = 4.036715),
            actualTime = Instant.ofEpochMilli(1690388097780),
            createdTime = Instant.ofEpochMilli(1690388097780),
            encounterType = EncounterEvent.EncounterType.BOATMAN,
            metadata = BoatmanEncounterMetadata(true, 1, true),
            deleted = false,
            regenerated = null
        )

        val encounterEndEvent = createEncounterEndEvent(
            _id = "7e9e197d-83ec-4d74-923c-96a7772ac5fd",
            startEventId = "60e78f58-5224-420e-aa39-916d1a25be71",
            ship = AisShipIdentifier(mmsi = 354654000, imo = 9811012),
            otherShip = AisShipIdentifier(mmsi = 244790247),
            location = Location(lat = 51.97407166666667, lon = 4.036053333333333),
            actualTime = Instant.ofEpochMilli(1690389797045),
            createdTime = Instant.ofEpochMilli(1690389797045),
            encounterType = EncounterEvent.EncounterType.BOATMAN,
            metadata = BoatmanEncounterMetadata(true, 1, true),
            deleted = false,
            regenerated = null
        )
    }

    @TestConfiguration
    class Config {
        @Bean
        fun converter(
            meterRegistry: MeterRegistry,
            objectMapper: ObjectMapper,
            testEventStreamService: EventStreamService
        ): EncounterStartEndEventConverter {
            return EncounterStartEndEventConverter(
                properties,
                objectMapper,
                mock<RabbitMqEventSender>(),
                testEventStreamService,
                meterRegistry
            )
        }
    }

    @BeforeEach
    fun setUp() {
        converter.startConverter.initialize()
        converter.endConverter.initialize()
    }

    @Test
    fun `should initialize correctly`() {
        assertFalse(converter.startConverter.platformToAisEngineEnabled)
        assertTrue(converter.startConverter.aisEngineToPlatformEnabled)
        assertFalse(converter.endConverter.platformToAisEngineEnabled)
        assertTrue(converter.endConverter.aisEngineToPlatformEnabled)
    }

    @Test
    fun `should convert ais-engine start event model correctly`() {
        val result = converter.startConverter.toPlatformEvent(encounterStartEvent)
        val expectedPlatformEvent = objectMapper.loadEventFile<BoatmanEvent>("./platform/BoatmanStartEvent.json")

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expectedPlatformEvent)
    }

    @Test
    fun `should convert ais-engine end event model correctly`() {
        val result = converter.endConverter.toPlatformEvent(encounterEndEvent)
        val expectedPlatformEvent = objectMapper.loadEventFile<BoatmanEvent>("./platform/BoatmanEndEvent.json")

        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expectedPlatformEvent)
    }
}
