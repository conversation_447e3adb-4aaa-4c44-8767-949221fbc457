package nl.teqplay.aisengine.eventconverter.converter

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.model.ConfirmedBerthEndEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.aisengine.eventconverter.EventConverterBaseTest
import nl.teqplay.aisengine.eventconverter.createTestRabbitMqProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.Converters
import nl.teqplay.aisengine.eventconverter.properties.EventConverterProperties.EnabledConverter
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.platform.toLocation
import nl.teqplay.aisengine.platform.toPlatformLocation
import nl.teqplay.aisengine.platform.toShipInfo
import nl.teqplay.platform.model.event.BerthEvent
import nl.teqplay.platform.model.event.ConfirmedBerthEvent
import nl.teqplay.platform.model.event.TeqplayEvent.EventType
import nl.teqplay.platform.model.event.UniqueBerthEvent
import nl.teqplay.platform.model.infra.Berth
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import java.time.Duration
import java.time.Instant
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ContextConfiguration
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class BerthEventConverterTest(
    private val converter: BerthEventConverter,
) : EventConverterBaseTest() {

    companion object {
        val properties = EventConverterProperties(
            converters = mapOf(
                Converters.BERTH to EnabledConverter.AISENGINE,
            ),
            loggingInterval = Duration.ofHours(1),
            rabbitmq = createTestRabbitMqProperties(),
            area = EventConverterProperties.AreaEventConfig(false)
        )

        const val PLATFORM_BERTH_ID = "PLATFORM_BERTH_ID"
        val area = AreaIdentifier("id", AreaIdentifier.AreaType.BERTH, "berth-1", "NLRTM")
        val berth = BerthIdentifier(0.0, "Teqplay Terminal")
        val ship = AisShipIdentifier(mmsi = 413827363, imo = 9827815)

        val startEvent = ConfirmedBerthStartEvent(
            _id = "e560e73a-689b-4ddc-84f0-f95057bf99ca",
            ship = ship,
            location = Location(lat = 31.372700000000002, lon = 121.59617333333334),
            actualTime = Instant.ofEpochMilli(1664980096000),
            createdTime = Instant.ofEpochMilli(1664980096000),
            deleted = false,
            area = area,
            berthEventId = "e560e73a-689b-4ddc-84f0-f95057bf99ca",
            berth = berth,
            heading = 1,
            draught = 1.0F,
            regenerated = false
        )

        val berthStartEvent = BerthEvent(
            true, 1664980181319L, null, ship.toShipInfo(),
            "test", "test", "test",
            Location(lat = 31.372700000000002, lon = 121.59617333333334).toPlatformLocation(),
            Berth("berth-1"), null, null, null, null,
            "NLRTM", null, null
        )

        val berthEndEvent = BerthEvent(
            false, 1664980181319L, "relatedEventId", ship.toShipInfo(),
            "test", "test", "test",
            Location(lat = 31.372700000000002, lon = 121.59617333333334).toPlatformLocation(),
            Berth("berth-1"), null, null, null, null,
            "NLRTM", null, null
        )

        val startConfirmedEventPlatform = ConfirmedBerthEvent(berthStartEvent, ship.toShipInfo(), 1664980181319L)
        val endConfirmedEventPlatform = ConfirmedBerthEvent(berthEndEvent, ship.toShipInfo(), 1664980181319L)
        val startUniqueEventPlatform = UniqueBerthEvent(startConfirmedEventPlatform, ship.toShipInfo(), 1664980181319L)
        val endUniqueEventPlatform = UniqueBerthEvent(endConfirmedEventPlatform, ship.toShipInfo(), 1664980181319L)

        val endEvent = ConfirmedBerthEndEvent(
            _id = "911ca899-9366-49f7-a384-f4cbf375231f",
            startEventId = "e560e73a-689b-4ddc-84f0-f95057bf99ca",
            ship = ship,
            location = Location(lat = 31.372700000000002, lon = 121.59617333333334),
            actualTime = Instant.ofEpochMilli(1664980181319),
            createdTime = Instant.ofEpochMilli(1664980181319),
            deleted = false,
            area = area,
            berthEventId = "e560e73a-689b-4ddc-84f0-f95057bf99ca",
            berth = berth,
            heading = 1,
            draught = 1.0F,
            regenerated = false
        )
    }

    @TestConfiguration
    class Config {
        @Bean
        fun converter(
            meterRegistry: MeterRegistry,
            objectMapper: ObjectMapper,
            testEventStreamService: EventStreamService
        ): BerthEventConverter {
            return BerthEventConverter(
                properties,
                objectMapper,
                mock<RabbitMqEventSender>(),
                testEventStreamService,
                meterRegistry,
                mock { whenever(it.getPlatformBerthId(any())).thenReturn(PLATFORM_BERTH_ID) }
            )
        }
    }

    @Test
    fun `should convert platform start event model correctly`() {
        converter.initialize()
        val result = converter.toPlatformEvent(startEvent) as ConfirmedBerthEvent?

        assertEquals(result?.uuid, startEvent._id)
        assertEquals(result?.isStartEvent, true)
        assertEquals(result?.eventTime, startEvent.actualTime.toEpochMilli())
        assertEquals(result?.generatedTime, startEvent.createdTime.toEpochMilli())
        assertEquals(result?.shipMmsi?.toInt(), startEvent.ship.mmsi)
        assertEquals(result?.title, "Confirmed: enters ${startEvent.area.name}")
        assertEquals(result?.description, "Confirmed: ${startEvent.ship.mmsi} enters ${startEvent.area.name}")
        assertEquals(result?.berthId, PLATFORM_BERTH_ID)
        assertEquals(result?.berthName, startEvent.area.name)
        assertEquals(result?.type, "ship-infrastructure.terminal.start.confirm")
        assertEquals(result?.eventType, EventType.CONFIRMED_MOORED)
        assertEquals(result?.location?.latitude, startEvent.location.lat)
        assertEquals(result?.location?.longitude, startEvent.location.lon)
    }

    @Test
    fun `should convert platform end event model correctly`() {
        converter.initialize()
        val result = converter.toPlatformEvent(endEvent) as ConfirmedBerthEvent?

        assertEquals(result?.uuid, endEvent._id)
        assertEquals(result?.isStartEvent, false)
        assertEquals(result?.relatedEvent, startEvent._id)
        assertEquals(result?.eventTime, endEvent.actualTime.toEpochMilli())
        assertEquals(result?.generatedTime, endEvent.createdTime.toEpochMilli())
        assertEquals(result?.shipMmsi?.toInt(), endEvent.ship.mmsi)
        assertEquals(result?.title, "Confirmed: exits ${endEvent.area.name}")
        assertEquals(result?.description, "Confirmed: ${endEvent.ship.mmsi} exits ${endEvent.area.name}")
        assertEquals(result?.berthId, PLATFORM_BERTH_ID)
        assertEquals(result?.berthName, endEvent.area.name)
        assertEquals(result?.type, "ship-infrastructure.terminal.end.confirm")
        assertEquals(result?.eventType, EventType.CONFIRMED_MOORED)
        assertEquals(result?.location?.latitude, endEvent.location.lat)
        assertEquals(result?.location?.longitude, endEvent.location.lon)
    }

    @Test
    fun `convert platform to ais-engine event`() {
        converter.initialize()
        val result = converter.toAisEngineEvent(startConfirmedEventPlatform)

        assertEquals(result?._id, startConfirmedEventPlatform.uuid)
        assertEquals(result?.location, startConfirmedEventPlatform.location.toLocation())
        assertEquals(result?.area?.id, startConfirmedEventPlatform.berthId)
        assertEquals(result?.area?.name, startConfirmedEventPlatform.berthName)
        assertEquals(result?.actualTime, Instant.ofEpochMilli(startConfirmedEventPlatform.datetime))
        assertEquals(result?.createdTime, Instant.ofEpochMilli(startConfirmedEventPlatform.generatedTime))
        assertEquals(result?.ship?.mmsi, startConfirmedEventPlatform.shipMmsi.toInt())
    }

    private fun routingKeyTestData() = Stream.of(
        Arguments.of(startConfirmedEventPlatform, "ship-infrastructure.terminal.start.confirm"),
        Arguments.of(endConfirmedEventPlatform, "ship-infrastructure.terminal.end.confirm"),
        Arguments.of(startUniqueEventPlatform, "ship-infrastructure.terminal.start.confirm.unique"),
        Arguments.of(endUniqueEventPlatform, "ship-infrastructure.terminal.end.confirm.unique"),
    )

    @ParameterizedTest
    @MethodSource("routingKeyTestData")
    fun <T : BerthEvent> `should generate rabbitmq routing key correctly`(event: T, expectedRoutingKey: String) {
        val result = converter.generateRoutingKey(event)
        assertEquals(expectedRoutingKey, result)
    }
}
