global:
  storageClass: "gp2"
  namespaceOverride: "teqplay-app"

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/event-converter

resources:
  requests:
    cpu: 0.05
    memory: 1Gi
  limits:
    memory: 1Gi

mongodb:
  enabled: false

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

nodeSelector:
  app.teqplay.nl/nodegroup: ais-engine

tolerations:
  - key: nodegroup
    operator: Equal
    value: ais-engine
    effect: NoSchedule
