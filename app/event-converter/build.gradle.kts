buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":api:nats-stream-event"))
    implementation(project(":lib:platform"))

    // Logging
    implementation("io.github.oshai:kotlin-logging-jvm:$kotlinLoggingVersion")
    implementation("org.codehaus.janino:janino:$janinoVersion")

    implementation("org.springframework.boot:spring-boot-starter-amqp")

    implementation("nl.teqplay.skeleton:poma-client:$skeletonVersion")
    implementation("nl.teqplay.skeleton:rabbitmq:$skeletonVersion")
    implementation("nl.teqplay.skeleton:util-location2:$skeletonVersion")
    testImplementation("nl.teqplay.skeleton:nats-test:$skeletonVersion")
    testImplementation(project(":lib:testing-event"))
    testImplementation(project(":lib:testing-platform"))
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
