plugins {
    id("ais-engine.kotlin-lib-conventions")
}

tasks.matching { it.name == "resolveMainClassName" }.configureEach {
    enabled = false
}

dependencies {
    implementation(project(":lib:platform"))
    implementation(project(":lib:testing-event"))
    implementation("nl.teqplay.skeleton:datasource-builder2:$skeletonVersion")
    implementation("nl.teqplay.skeleton:auth-credentials-keycloak-s2s-common:$skeletonVersion")
    implementation("org.springframework.boot:spring-boot-starter-web")
    api(project(":api:client-common"))
}
